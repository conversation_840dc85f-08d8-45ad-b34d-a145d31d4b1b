# 常用模式和最佳实践

- Key Secret Token管理最佳实践：1)主页面添加管理Token按钮跳转传参 2)Token页面自动生成12位随机token和UUID clientId 3)使用面包屑导航和返回按钮 4)关键字段只读，支持重新生成 5)优化表格显示使用标签和提示
- Token管理双向同步最佳实践：1)有效期支持2位小数，默认90天 2)有效期变化自动更新截止时间 3)截止时间变化自动更新有效期 4)使用isUpdatingTime标志防止循环更新 5)时间计算精确到毫秒并保留2位小数
- Token截止时间验证最佳实践：1)日期选择器添加pickerOptions限制选择过去的日期 2)添加自定义验证方法validateExpiredTime 3)时间变化时检查isValidExpiredTime 4)无效时间自动重置为默认值 5)提供明确的错误提示信息
- Token状态管理最佳实践：1)表格中使用el-switch显示和切换状态 2)查询表单支持按状态筛选 3)新增表单使用radio-group选择状态 4)状态变更时只传递id和status字段调用更新接口 5)操作失败时自动恢复原状态
- Token用量管理字段更新最佳实践：1)统计显示区域使用新字段totalPointCount、pointUsage、totalCallCount 2)编辑表单更新字段名称和验证规则 3)数据结构usageForm更新字段定义 4)相关计算方法更新为基于积分的计算 5)删除不需要的字段如remark备注
