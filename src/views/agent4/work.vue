<template>
  <div class="industry-edu-page">
    <div class="banner">
      <div class="banner-text">
        <h1>产教融合分析</h1>
        <p>多维数据融合 · 驱动教育创新</p>
      </div>
    </div>

    <el-container class="content-wrapper">
      <!-- 左侧图表 + 指标 -->
      <el-aside width="360px" class="aside-panel">
        <el-card class="aside-section">
          <h3 class="section-title">产教融合问数</h3>
          <div class="question-grid">
            <div class="q-box" v-for="(q, index) in questionData" :key="index">
              <div class="q-value">{{ q.value }}</div>
              <div class="q-title">{{ q.title }}</div>
            </div>
          </div>
        </el-card>

        <el-card class="aside-section">
          <h3 class="section-title">三年岗位趋势</h3>
          <div id="lineChart" class="aside-chart"></div>
        </el-card>
      </el-aside>

      <!-- 右侧主要内容 -->
      <el-main class="main-panel">
        <el-card class="main-section">
          <h3 class="section-title">行业-专业匹配分析</h3>
          <div id="radarChart" class="main-chart"></div>
        </el-card>

        <el-card class="main-section">
          <h3 class="section-title">智能体综合建议</h3>
          <blockquote class="suggestion-text">{{ suggestionText }}</blockquote>
        </el-card>

        <el-card class="main-section">
          <h3 class="section-title">维度详情分析</h3>
          <el-table :data="tableData" stripe border>
            <el-table-column prop="dimension" label="维度" width="180" />
            <el-table-column prop="value" label="值" width="100" />
            <el-table-column prop="trend" label="趋势" width="100" />
            <el-table-column prop="comment" label="建议分析" />
          </el-table>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import * as echarts from 'echarts'

export default {
  name: 'IndustryEduPage',
  data() {
    return {
      questionData: [
        { title: '岗位增长率', value: '18%' },
        { title: '专业匹配度', value: '87%' },
        { title: '技能缺口指数', value: '12%' },
        { title: '课程匹配率', value: '76%' },
        { title: '毕业去向满意度', value: '82%' },
        { title: '实训对接程度', value: '64%' },
      ],
      suggestionText:
        '根据岗位需求趋势与技能要求变化，建议强化数据类与工程类课程建设，增强与产业的实训协同；同时需优化课程资源配置，提高专业与行业发展趋势的匹配度，提升毕业生整体就业质量。',
      tableData: [
        {
          dimension: '人才需求趋势',
          value: '↑ 18%',
          trend: '上升',
          comment: '数字经济相关岗位需求持续增长，应加强相关课程开设。',
        },
        {
          dimension: '技能要求变化',
          value: 'AI, 大数据, 自动化',
          trend: '变化大',
          comment: '应强化AI与数据分析模块课程比例。',
        },
        {
          dimension: '专业匹配度',
          value: '87%',
          trend: '较好',
          comment: '现有课程体系较为贴合行业需求。',
        },
        {
          dimension: '课程内容前沿性',
          value: '76%',
          trend: '需优化',
          comment: '应引入更多产业实际项目内容。',
        },
        {
          dimension: '实训报告数据',
          value: '部分滞后',
          trend: '更新慢',
          comment: '实训体系需对接实时行业需求。',
        },
      ],
    }
  },
  mounted() {
    this.initRadar()
    this.initLine()
  },
  methods: {
    initRadar() {
      const radar = echarts.init(document.getElementById('radarChart'))
      radar.setOption({
        title: { text: '行业-专业匹配雷达图' },
        tooltip: {},
        radar: {
          indicator: [
            { name: '岗位需求', max: 100 },
            { name: '课程匹配', max: 100 },
            { name: '实训对接', max: 100 },
            { name: '毕业发展', max: 100 },
            { name: '资源投入', max: 100 },
          ],
        },
        series: [
          {
            type: 'radar',
            data: [
              {
                value: [88, 76, 64, 82, 70],
                name: '融合指数',
              },
            ],
          },
        ],
      })
    },
    initLine() {
      const line = echarts.init(document.getElementById('lineChart'))
      line.setOption({
        title: { text: '三年岗位需求趋势图' },
        tooltip: {},
        xAxis: {
          type: 'category',
          data: ['2022', '2023', '2024'],
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '岗位增长率',
            type: 'line',
            data: [12, 15, 18],
            smooth: true,
          },
        ],
      })
    },
  },
}
</script>
<style scoped>
.industry-edu-page {
  background: #f5f7fa;
  min-height: 100vh;
  font-family: 'Segoe UI', 'PingFang SC', sans-serif;
  color: #333;
}

.banner {
  background: linear-gradient(120deg, #2c3e50, #3498db);
  color: #fff;
  padding: 40px 20px;
  text-align: center;
}

.banner-text h1 {
  font-size: 32px;
  margin: 0;
}

.banner-text p {
  font-size: 16px;
  opacity: 0.9;
}

.content-wrapper {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.aside-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.q-box {
  background: #eef3f8;
  border-radius: 8px;
  text-align: center;
  padding: 12px 8px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
}

.q-value {
  font-size: 20px;
  color: #409EFF;
  font-weight: bold;
}

.q-title {
  font-size: 14px;
  margin-top: 4px;
  color: #666;
}

.aside-chart {
  height: 220px;
}

.main-chart {
  height: 400px;
}

.suggestion-text {
  background: #fffaf0;
  padding: 15px;
  border-left: 4px solid #ff9900;
  color: #555;
  font-size: 15px;
  line-height: 1.8;
  margin: 0;
}
</style>
