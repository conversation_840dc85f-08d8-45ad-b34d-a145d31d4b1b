<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="专业" prop="major">
        <el-select class="ck-input" v-model="form.major" filterable placeholder="请选择专业"
          :disabled="flag=='review'||courseflag">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="课程" prop="course">
        <el-input class="ck-input" v-model="form.course" placeholder="请输入课程名称" maxlength="50"
          :disabled="flag=='review'||courseflag" show-word-limit @blur="getKnowledge()" />
      </el-form-item>

      <el-form-item label="教材" prop="textBookName">
        <el-select class="ck-input" v-model="book" filterable placeholder="请选择教材" :disabled="flag=='review'"
          @focus="getKnowledge()">
          <el-option v-for="item in KnowledgeOptions" :key="item.id" :label="item.fileName" :value="item.fileName">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="章节" prop="chapter">
        <el-select class="ck-input" v-model="chapter" multiple filterable placeholder="请选择章节" :disabled="flag=='review'"
          @focus="getChapter()">
          <el-option v-for="(chapter, index) in chagetList" :key="index" :label="chapter" :value="chapter">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="课件名称" prop="presentationName">
        <el-input class="ck-input" v-model="form.presentationName" placeholder="请输入课件名称" maxlength="50"
          :disabled="flag=='review'" show-word-limit />
      </el-form-item>
      <el-form-item label="上传PPT" prop="deiDesc">
        <el-upload class="upload-demo ck-input" :class="flag == 'review'?'view-upload':''" drag :action="uploadPptUrl"
          :data="uploadPptData" :headers="headers" :on-success="handleUploadPptSuccess" :on-remove="handleRemovePpt"
          :file-list="pptFileList" accept=".pptx,.ppt"  :disabled="flag=='review'" :limit="1" :before-upload="beforeUploadppt">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip" v-if="flag != 'review'">支持ppt或pptx文件上传,仅能上传一个文件</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="上传演讲稿" prop="deiDesc">
        <el-upload class="upload-demo  ck-input" :class="flag == 'review'?'view-upload':''" drag :action="uploadUrl"
          :data="uploadData" :headers="headers" :on-success="handleUploadSuccess" :on-remove="handleRemove"
          :file-list="fileList" accept=".txt"  :disabled="flag=='review'" :limit="1" :before-upload="beforeUploadtxt">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip" v-if="flag != 'review'">支持txt文件上传,仅能上传一个文件
            <el-button style="margin-left: 20px;" type="text" @click="handleOpenExampleDialog">演讲稿示例</el-button>
          </div>
        </el-upload>
        <el-button type="primary" v-if="flag != 'review' && form.speechdraftFileId" @click="handleEditSpeech"
          size="mini">编辑演讲稿</el-button>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input class="ck-input" type="textarea" autosize v-model="form.remark" placeholder="请输入备注内容"
          :disabled="flag=='review'" />
      </el-form-item>
    </el-form>
    <el-row class=" step-btn-box" v-if="flag!='review'">
      <el-button type="primary" @click="handleSubmint">确定</el-button>
      <el-button @click="handleBack">取消</el-button>
    </el-row>

    <el-dialog title="演讲稿编辑" :visible.sync="dialogFormVisible" width="70%" class="txt-edit-dialog">
      <el-input ref="inputRef" type="textarea" v-model="txt" :autosize="{ minRows: 18, maxRows: 18}" @blur="updateRate"
        id="text-box"></el-input>
      <div style="width:100%">
        <div>
          <div style="margin-top: 10px;">操作:</div>
          <el-tooltip v-for="dict in dict.type.speech_manipulation" :key="dict.value" :content="dict.label"
            placement="top" effect="light">
            <img class="action-img" :src="require(`@/assets/actionImg/${dict.raw.cssClass}.png`)" alt=""
              @click="getCursor(dict.value)">
          </el-tooltip>
        </div>
        <div>
          <div>动作:</div>
          <el-tooltip v-for="dict in dict.type.speech_action" :key="dict.value" :content="dict.label" placement="top"
            effect="light">
            <img class="action-img" :src="require(`@/assets/actionImg/${dict.raw.cssClass}.png`)" alt=""
              @click="getCursor(dict.value)">
          </el-tooltip>
        </div>
        <div>
          <div>表情:</div>
          <el-tooltip v-for="dict in dict.type.speech_expression" :key="dict.value" :content="dict.label"
            placement="top" effect="light">
            <img class="action-img" :src="require(`@/assets/actionImg/${dict.raw.cssClass}.png`)" alt=""
              @click="getCursor(dict.value)">
          </el-tooltip>

        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleColseDialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmintSpeech">确 定</el-button>

      </div>
    </el-dialog>

    <el-dialog title="演讲稿示例" :visible.sync="exampleVisible" width="70%" class="txt-edit-dialog">
      <el-input type="textarea" v-model="exampleInfo" :autosize="{ minRows: 26, maxRows: 26}" readonly></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleColseExampleDialog">关闭</el-button>
        <el-button type="primary" @click="handleDownloadExample">下载</el-button>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { getPresentation, addPresentation, updatePresentation, getUniversity, getTxt, updateTxt, getMajorList, downloadPresentation, downloadSpeech, getKnowledge } from "@/api/teachingMaterials/teachingMaterials.js";

export default {
  name: 'FormContent',
  dicts: ['speech_action', 'speech_manipulation', 'speech_expression'],
  props: {
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploadPptUrl: process.env.VUE_APP_BASE_API + "/file/file/uploadPPtTemp",
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/uploadPath", // 上传的图片服务器地址
      uploadData: { modeltype: 'yjg' },
      uploadPptData: { modeltype: 'ppt' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      pptFileList: [],
      form: { speechdraftFileId: undefined },
      rules: {
        major: [
          { required: true, message: '请选择专业', trigger: ['blur', 'change'] },
        ],
        course: [
          { required: true, message: '请输入课程名称', trigger: ['blur', 'change'] },
        ],
        presentationName: [
          { required: true, message: '请输入课件名称', trigger: ['blur', 'change'] },
        ],
      },
      options: [],
      universityOptions: [],
      dialogFormVisible: false,
      txt: '',
      textCursorStart: 0,
      textCursorEnd: 0,
      exampleVisible: false,
      exampleInfo: '',
      courseflag: false,
      KnowledgeOptions: [],
      queryParmk: {
        course: "",
        major: ""
      },
      book: "",
      chagetList: [],
      chapter: [],
    }
  },
  created() {
    this.getPresentation()
    this.getMajorList()
    this.getUniversity()
    this.initialize()
  },
  watch: {
    book(newVal, oldVal) {
      const index = this.KnowledgeOptions.findIndex(item => item.fileName === newVal);
      this.chagetList = this.KnowledgeOptions[index].chapterList
      this.form.textBookId = this.KnowledgeOptions[index].id
    },
  },

  methods: {
    getChapter() {
      if (!this.form.course || !this.form.major) {
        this.$message.warning('数据准备中，请重试')
        return;
      }
      const queryParmkT = {
        course: this.form.course,
        major: this.form.major
      }
      if (this.book != "") {
        getKnowledge(queryParmkT).then(res => {
          this.KnowledgeOptions = res.data

          // 确保数据赋值后再执行查找
          const index = this.KnowledgeOptions.findIndex(item => item.fileName === this.book);
          if (index !== -1) { // 检查是否找到匹配项
            this.chagetList = this.KnowledgeOptions[index].chapterList;
            this.form.textBookId = this.KnowledgeOptions[index].id;
          } else {
            console.log("数据不存在");
          }
        }).catch(error => {
          console.error("数据加载失败:", error);
        });
      }
    },

    getKnowledge() {

      this.queryParmk.course = this.form.course
      this.queryParmk.major = this.form.major

      console.log("----------------------" + this.queryParmk)
      getKnowledge(this.queryParmk).then(res => {
        console.log(res)
        this.KnowledgeOptions = res.data
      })
    },

    initialize() {
      if (this.$route.query && this.$route.query.courseflag) {
        this.form.course = this.$route.query && this.$route.query.course
        this.form.major = this.$route.query && this.$route.query.major
        this.courseflag = true
      }


    },
    getPresentation() {
      if (this.flag != 'add') {
        getPresentation(this.$route.query.id).then(res => {
          this.form = res.data
          this.chapter = res.data.chapter.split(",")
          this.book = res.data.textBookName
          this.fileList = res.data.speechdraftFileList
          this.pptFileList = res.data.presentationFileList
        })
      }
    },
    beforeUploadppt(file) {
      // 定义允许的文件类型
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
        'application/vnd.ms-powerpoint' // .ppt
      ];
      const fileType = file.type;
      const isValidType = allowedTypes.includes(fileType);

      // 检查文件类型
      if (!isValidType) {
        this.$message.error('上传文件只能是 pptx或ppt 格式!');
        return false;
      }
    },
    beforeUploadtxt(file) {
      // 定义允许的文件类型
      const allowedTypes = ['text/plain'];
      const fileType = file.type;
      const isValidType = allowedTypes.includes(fileType);

      // 检查文件类型
      if (!isValidType) {
        this.$message.error('上传文件只能是 txt 格式!');
        return false;
      }
    },
    handleUploadSuccess(res, file) {
      this.form.speechdraftpath = res.data.SpeechDraftPath
      this.form.speechdraftFileId = res.data.SpeechDraftId

    },
    handleRemove(file, fileList) {
      if( this.form.idCopy == null){
        this.form.idCopy=this.form.id
      }

      console.log("备份id为"+this.form.idCopy)
      this.form.id = null
      this.fileList = []
      this.form.speechdraftpath = undefined
      this.form.speechdraftFileId = undefined
    },
    handleUploadPptSuccess(res, file) {
      if (res.code === 200) {
        this.form.fileName = res.data.fileName
        this.form.presentationAllpage = res.data.pptAllPage
        this.form.presentationHttp = res.data.presentationHttp
        this.form.presentationId = res.data.presentationId
        this.form.presentationPath = res.data.presentationPath
        this.form.presentationFileId = res.data.fileid

        this.form.speechdraftFileId = Number(this.form.speechdraftFileId)
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传文件只能是 pptx或ppt 格式!');
        //删除上传文件
        deletePresentation(this.form.presentationFileId).then(res)
        this.$message.error('上传文件只能是 pptx或ppt 格式!');
        //删除前端页面中的文件
        this.pptFileList = []
      }

    },
    handleRemovePpt(file, fileList) {
      this.$confirm('是否一并删除演讲稿？', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
      })
        .then(() => {
          // 用户点击了“是”，连演讲稿也删
          this.handleRemove(); // 自定义的演讲稿清除函数
          this.clearPptInfo();         // 清除 PPT 字段
        })
        .catch(() => {
          // 用户点击了“否”，只删 PPT
          this.clearPptInfo();         // 只清除 PPT 字段
        });

    },
    clearPptInfo() {
      this.form.fileName = undefined;
      this.form.presentationAllpage = undefined;
      this.form.presentationHttp = undefined;
      this.form.presentationId = undefined;
      this.form.presentationPath = undefined;
      this.form.presentationFileId = undefined;
    },
    handleSubmint() {
      console.log("被复制的id为"+this.form.idCopy)
      if(this.form.idCopy != null){
        this.form.id = this.form.idCopy
      }

      if (this.validateForm()) {
        if (!this.form.speechdraftFileId || !this.form.presentationFileId) {
          this.$message.warning('请先上传演讲稿和ppt')
          return
        }
        console.log(this.form)
        this.form.chapter = this.chapter.toString()
        console.log(this.form)
        if (this.flag == 'add') {
          addPresentation(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('新增成功')
              this.handleBack()
            }
          })
        } else {
          updatePresentation(this.form).then(res => {
            if (res.code === 200) {
              this.$message.success('修改成功')
              this.handleBack()
            }
          })
        }
      }
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      if (this.form.course && this.form.major) {
        this.$router.push("/teachingMaterials/coursewareManagement?course=" + this.form.course + "&major=" + this.form.major)
      } else {
        this.$router.push("/explorationCenter6/teachingMaterials")
      }
    },
    getMajorList() {
      getMajorList().then(res => {
        if (res.code === 200) {
          const data = [];
          for (let i = 0; i < res.data.length; i++) {
            const major = {
              value: String(res.data[i].id),
              label: res.data[i].majorName
            }
            data.push(major);
          }
          this.options = data;
        }
      })
    },
    getUniversity() {
      getUniversity().then((res) => {
        this.universityOptions = res.data.map((item) => {
          return {
            value: item.id,
            label: item.name,
            children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
              return {
                value: item.id,
                label: item.name,
                children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                  };
                }) : null
              };
            }) : null
          };
        });
      })
    },
    handlePreview(file) {
      var link = document.createElement("a"); //定义一个a标签
      link.download = file.name; //下载后的文件名称
      link.href = file.url; //需要生成一个 URL 来实现下载
      link.click(); //模拟在按钮上实现一次鼠标点击
      window.URL.revokeObjectURL(link.href);
    },
    handlePreviewp(file) {

      console.log(this.form.presentationId)
      this.$router.push({
        path: "/teachingMaterials/presentationPreview",
        query: {
          presentationId: this.form.presentationId
        },
      });
      // downloadPresentation(file.url).then(res => {
      //   // 假设res是一个Blob数据或文件
      //   this.triggerDownload(res, file.name+".pptx");
      // });
    },
    handlePreviews(file) {
      downloadSpeech(file.url).then(res => {
        // 假设res是一个Blob数据或文件
        this.triggerDownload(res, file.name);
      });
    },
    triggerDownload(data, fileName) {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName); // 设置下载的文件名
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link); // 下载后移除link元素
      window.URL.revokeObjectURL(url); // 释放URL对象
    },
    updateRate(e) {
      this.textCursorStart = this.$refs.inputRef.$el.children[0].selectionStart;
      this.textCursorEnd = this.$refs.inputRef.$el.children[0].selectionEnd;
    },
    getCursor(insertTxt) {
      const elInput = document.getElementById("text-box");
      elInput.focus();
      elInput.setSelectionRange(this.textCursorStart, this.textCursorEnd);
      // 获取el-input的值
      let txt = elInput.value;
      // 获取选区开始位置
      let startPos = elInput.selectionStart;
      // 获取选区结束位置
      let endPos = elInput.selectionEnd;
      if (startPos === undefined || endPos === undefined) return;
      this.txt = txt.substring(0, startPos) + insertTxt + txt.substring(endPos);
      elInput.setSelectionRange(this.textCursorStart + insertTxt.length, this.textCursorEnd + insertTxt.length,);
    },
    handleEditSpeech() {
      const param = {
        speechdraftpath: this.form.speechdraftpath,
        id: this.form.id
      };
      getTxt(param).then(res => {
        this.dialogFormVisible = true
        this.txt = res

      })
    },
    handleSubmintSpeech() {
      const param = {
        speechdraftpath: this.form.speechdraftpath,
        speechdraftFileTxt: this.txt,
        id: this.form.id
      };
      updateTxt(param).then(res => {
        if (res.code === 200) {
          this.$message.success("修改成功")
          this.handleColseDialog()
        }
      })
    },
    handleColseDialog() {
      this.dialogFormVisible = false
      this.txt = ''
    },
    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleOpenExampleDialog() {
      this.exampleVisible = true
      let xhr = new XMLHttpRequest(),
        okStatus = document.location.protocol === "file:" ? 0 : 200;
      xhr.open("GET", "../../../../speechExample.txt", false); // 文件路径
      xhr.overrideMimeType("text/html;charset=utf-8"); //默认为utf-8
      xhr.send(null);
      this.exampleInfo = xhr.responseText
    },
    handleColseExampleDialog() {
      this.exampleVisible = false
      this.exampleInfo = ''
    },
    handleDownloadExample() {
      const element = document.createElement('a')
      element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(this.exampleInfo))
      element.setAttribute('download', '演讲稿示例')
      element.style.display = 'none'
      element.click()
    }
  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
.action-img {
  width: 30px;
  height: 30px;
  margin: 10px;
}
</style>
<style lang="scss">
.view-upload .el-upload-dragger {
  display: none;
}
.view-upload .el-upload-list__item:first-child {
  margin-top: -25px;
}
</style>
<style>
.txt-edit-dialog .el-dialog__body {
  padding: 0 10px;
}
</style>
