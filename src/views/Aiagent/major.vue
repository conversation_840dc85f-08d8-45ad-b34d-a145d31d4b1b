<template>
  <div class="dashboard-container">
    <div class="header">
      <h1>专业发展画像分析</h1>
      <el-button type="primary" @click="downloadReport">下载分析报告</el-button>
    </div>

    <el-card class="section">
      <h2>一、专业问数画像</h2>
      <el-table :data="questionnaireData" border style="width: 100%">
        <el-table-column prop="question" label="问题" width="400" />
        <el-table-column prop="result" label="结果" />
      </el-table>
    </el-card>

    <el-card class="section">
      <h2>二、专业发展多维评估</h2>
      <div class="chart-row">
        <div class="chart-box">
          <h3>OBE达成度雷达图</h3>
          <div ref="radarChart" style="height: 300px;"></div>
        </div>
        <div class="chart-box">
          <h3>满意度与就业质量折线图</h3>
          <div ref="lineChart" style="height: 300px;"></div>
        </div>
      </div>
    </el-card>

    <el-card class="section">
      <h2>三、自定义评估维度</h2>
      <el-form :model="customMetric" inline>
        <el-form-item label="维度名称">
          <el-input v-model="customMetric.name" />
        </el-form-item>
        <el-form-item label="得分">
          <el-input-number v-model="customMetric.score" :min="0" :max="100" />
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="addCustomMetric">添加</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="customMetrics" border style="margin-top: 20px">
        <el-table-column prop="name" label="维度" />
        <el-table-column prop="score" label="得分" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      questionnaireData: [
        { question: '当前课程体系是否支撑专业核心能力？', result: '高度支撑' },
        { question: '师资结构是否满足发展需求？', result: '基本满足' },
        { question: '毕业生就业质量如何？', result: '高质量就业' },
        { question: '学生满意度如何？', result: '满意度高' }
      ],
      radarData: [
        { dimension: 'OBE达成度', value: 80 },
        { dimension: '课程支撑度', value: 75 },
        { dimension: '师资结构', value: 65 },
        { dimension: '就业质量', value: 90 },
        { dimension: '满意度', value: 85 }
      ],
      lineData: [
        { year: '2021', satisfaction: 75, employment: 85 },
        { year: '2022', satisfaction: 80, employment: 88 },
        { year: '2023', satisfaction: 85, employment: 92 }
      ],
      customMetric: {
        name: '',
        score: null
      },
      customMetrics: [],
      radarChartInstance: null,
      lineChartInstance: null
    }
  },
  methods: {
    addCustomMetric() {
      if (this.customMetric.name && this.customMetric.score !== null) {
        this.customMetrics.push({ ...this.customMetric })
        this.customMetric.name = ''
        this.customMetric.score = null
        this.updateRadarChart() // 自定义维度也更新雷达图
      }
    },
    downloadReport() {
      this.$message.success('模拟报告下载成功！')
    },
    getRadarOption() {
      // 把自定义维度也加进来
      const allRadarData = [
        ...this.radarData,
        ...this.customMetrics.map(item => ({ dimension: item.name, value: item.score }))
      ]

      return {
        tooltip: {},
        radar: {
          indicator: allRadarData.map(item => ({
            name: item.dimension,
            max: 100
          }))
        },
        series: [{
          type: 'radar',
          data: [{
            value: allRadarData.map(item => item.value),
            name: '得分'
          }]
        }]
      }
    },
    getLineOption() {
      return {
        tooltip: { trigger: 'axis' },
        legend: { data: ['满意度', '就业质量'] },
        xAxis: {
          type: 'category',
          data: this.lineData.map(item => item.year)
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '满意度',
            type: 'line',
            data: this.lineData.map(item => item.satisfaction)
          },
          {
            name: '就业质量',
            type: 'line',
            data: this.lineData.map(item => item.employment)
          }
        ]
      }
    },
    initRadarChart() {
      if (!this.radarChartInstance) {
        this.radarChartInstance = echarts.init(this.$refs.radarChart)
      }
      this.updateRadarChart()
    },
    updateRadarChart() {
      if (this.radarChartInstance) {
        this.radarChartInstance.setOption(this.getRadarOption())
      }
    },
    initLineChart() {
      if (!this.lineChartInstance) {
        this.lineChartInstance = echarts.init(this.$refs.lineChart)
      }
      this.updateLineChart()
    },
    updateLineChart() {
      if (this.lineChartInstance) {
        this.lineChartInstance.setOption(this.getLineOption())
      }
    },
    resizeCharts() {
      this.radarChartInstance && this.radarChartInstance.resize()
      this.lineChartInstance && this.lineChartInstance.resize()
    }
  },
  mounted() {
    this.initRadarChart()
    this.initLineChart()
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.radarChartInstance && this.radarChartInstance.dispose()
    this.lineChartInstance && this.lineChartInstance.dispose()
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.section {
  margin-bottom: 30px;
}
.chart-row {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}
.chart-box {
  flex: 1;
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}
</style>
