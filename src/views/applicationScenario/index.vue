<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="场景名称" prop="applicationName">
        <el-input v-model="queryParams.applicationName" placeholder="请输入场景名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scenarioList">
      <!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="场景名称" align="center" prop="applicationName" />
      <el-table-column label="场景背景" align="center" prop="imagePath">
        <template slot-scope="scope">
          <span class="blue-font-color" @click="lookImage(scope.row)">
            预览
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          <!-- 上传文件按钮 -->
          <el-button size="mini" type="text" icon="el-icon-upload" @click="putBase(scope.row)">上传文件</el-button>


        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改应用场景对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body @close="deleteImgByPath()">

      <el-form ref="formData" :model="formData" :rules="rules" label-width="80px">

        <el-form-item label="场景名称" prop="applicationName">
          <el-input v-model="formData.applicationName" placeholder="请输入场景名称" />
        </el-form-item>


        <el-form-item label="专业名称" prop="major">
          <el-select class="ck-input" v-model="formData.major" filterable placeholder="请选择专业">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 场景问答提示词 --------------------------------------------------------->
        <el-form-item label="场景问答"  prop="pormpt">
          <el-input v-model="formData.prompt" :rows="4" type="textarea" placeholder="场景固定的引导词，通过调整该内容，可以引导问答对话方向。"
            :maxlength="sizeMaxLength"  show-word-limit />
        </el-form-item>
        <!-- ---------------------------------------------------------------------------->

        <el-carousel :height="carouselHeight" indicator-position="none" :autoplay="false" :interval="999999"
          @change="handleCarouselChange">

          <el-carousel-item v-for="item in 2" :key="item">
            <div v-if="item === 1">
              <el-form-item label="场景描述" prop="applicationName">
                <el-input v-model="imageMake" :rows="4" type="textarea" placeholder="请输入场景描述" :maxlength="sizeMaxLength"
                  show-word-limit />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getImage">生成预览图</el-button>
              </el-form-item>

              <div v-if="makeImage && !haveImage" style="padding-left: 80px">
                <el-skeleton style="width: 240px" animated>
                  <template slot="template">
                    <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
                    <div style="padding: 14px;">
                      <el-skeleton-item variant="p" style="width: 50%" />
                      <div style="display: flex; align-items: center;">
                        <el-skeleton-item variant="text" style="margin-right: 16px;" />
                        <el-skeleton-item variant="text" style="width: 30%;" />
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>

              <img v-if="haveImage" :src="imageBase64" alt="1111" style="max-width: 100%; height: auto;">
            </div>
            <div v-else-if="item === 2">
              <el-form-item label="图片上传" prop="applicationName">
                <el-upload class="upload-demo ck-input" drag :action="uploadPng" multiple :data="uploadPngData"
                  :headers="headers" :on-success="handleUploadPngSuccess" :on-remove="handleRemovePng"
                  :file-list="pngFileList" accept=".png,.jpg,.jpeg" :on-preview="handlePreviewPng" :limit="1"
                  :before-upload="beforeUpload">
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip" slot="tip">支持.png,.jpg,.jpeg图片上传,仅能上传一个文件</div>
                </el-upload>
              </el-form-item>
            </div>

          </el-carousel-item>
        </el-carousel>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="预览图片" :visible.sync="lookImages" width="500px" append-to-body>
      <img :src="imageUrl" alt="" style="max-width: 100%; height: auto;">
    </el-dialog>
  </div>
</template>

<script>
import {
  listScenario,
  getScenario,
  delScenario,
  addScenario,
  updateScenario,
  getImage,
  getScenarioImage,
  deleteImgByPath,
  getMajor
} from "@/api/applicationScenario/scenario.js";
import { getToken } from "@/utils/auth";
import { getDicts } from "@/api/system/dict/data";
import { addNewZsk, addPrompt } from "@/api/dataSet/knowledgeBase.js";
// import dayjs from 'dayjs'

export default {
  name: "Scenario",
  dicts: ['size_max_length'],
  data() {
    return {
      // promptInput: '',
      // promptForm: {
      //   id: null,
      //   name: "name", //场景名称
      //   majorId: "majorId", //专业id
      //   prompt: "prompt", //提示词
      //   applicationScenarioId: "applicationScenarioId", //应用场景id
      //   createBy: null,
      //   createTime: null,
      //   updateBy: null,
      //   updateTime: null
      // },

      uploadPng: process.env.VUE_APP_BASE_API + "/file/file/upload",
      uploadPngData: { modeltype: 'scenario' },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应用场景表格数据
      scenarioList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      options: [
        { value: '城市管理学' },
        { value: '1' },
        { value: 'yingyu' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationName: null,
        imagePath: null,
      },
      makeQuery: {
        make: '',
      },
      // 表单参数
      formData: {
        applicationName: '',
        imagePath: '',
        major: '',
        prompt: '',
      },
      // 表单校验
      rules: {
        applicationName: [
          { required: true, message: "场景名称不能为空", trigger: "blur" },
        ],
        imagePath: [
          { required: true, message: "场景背景图路径不能为空", trigger: "blur" },
        ],
        major: [
          { required: true, message: "专业不能为空", trigger: "blur" },
        ],
      },
      makeImage: false,
      promptModel: '',
      haveImage: false,
      imageBase64: "",
      lookImages: false,
      imageUrl: '',
      imageMake: "",
      carouselHeight: '300px', // 默认高度
      pngFileList: [],
      sizeMaxLength: 100,//文本字数最大限制
      imgUrl: false,
    };
  },
  created() {
    this.getSizeMaxLength('size_max_length');
    this.getList();
    this.getMajor();
  },
  watch: {
    open(newVal) {
      if (!newVal) {
        this.makeImage = false;
        //  this.makeImage1 = false;
        this.haveImage = false;
      }
      this.formData.imagePath = null;
    },
  },
  methods: {
    // 提示词提交
    // handleInput(value) {
    //   this.promptInput = value;
    //   console.log("此时提示词this.promptInput内容：", this.promptInput)
    // },
    /** 提交按钮 */
    submitForm() {
      this.$refs["formData"].validate(valid => {
        if (valid) {
          if (this.imgUrl) {
            this.$message.warning('请先制作背景图')
            return
          }
          if (this.formData.id != null) {
            updateScenario(this.formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addScenario(this.formData).then(response => {
              console.log("这里是新增应用场景的响应结果：", response)
              this.$modal.msgSuccess("新增成功");
              this.open = false;
               this.getList();
            });
          }

         
          console.log("提交表单执行了getlist")

        }
      });

    },
    // 上传文件到知识库
    putBase(row) {
      console.log("这里是当前行参数：====================", row)
      const id = row.id || this.ids;
      const name = row.applicationName;
      const major = row.major

      this.$router.push({
        path: '/applicationScenario/putzsk',
        query: {
          id,
          name,
          major
        }
      });
    }

    ,
    getMajor() {
      getMajor().then(res => {
        console.log("开始获取专业")
        console.log(res)
        console.log(res.rows.majorName)
        const majorList = res.rows.map(item => ({
          label: item.majorName,
          value: item.id
        }));

        this.options = majorList;
        this.options = majorList;
      })
    },
    getSizeMaxLength(key) {
      getDicts(key).then(res => {
        console.log(res)
        res.data.forEach(item => {
          if (item.dictLabel === 'scenarioSize') {
            this.sizeMaxLength = Number(item.dictValue)
            console.log(this.sizeMaxLength)
          }
        })
      })
    },
    beforeUpload(file) {
      // 定义允许的文件类型
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      const fileType = file.type;
      console.log(fileType)
      const isValidType = allowedTypes.includes(fileType);

      // 检查文件类型
      if (!isValidType) {
        this.$message.error('上传文件只能是 .png,.jpg,jpeg 格式!');
        return false;
      }
    },
    handleUploadPngSuccess(res, file) {
      this.formData.imagePath = res.data.preview;
      this.formData.fileId = res.data.id;
      console.log(res)
      console.log(this.formData.imagePath)
    },
    handleRemovePng(file, fileList) {
      this.formData.imagePath = "";
    },
    handlePreviewPng(file, fileList) { },
    handleCarouselChange(activeIndex) {
      // 根据激活的索引来设置高度
      this.carouselHeight = activeIndex === 1 ? '300px' : '300px';
    },

    getImage() {
      this.carouselHeight = '500px';
      if (this.imageMake == null || this.imageMake == "") {
        this.$message.warning('请先输入描述')
        return
      }
      this.makeImage = true;
      this.haveImage = false;
      this.makeQuery.make = this.imageMake;
      getImage(this.makeQuery).then(response => {
        console.log(response)
        if (response.data.imagePath.includes("输入内容包含违禁词")) {
          this.$message.warning(response.data.imagePath)
          this.open = false;
          return
        }
        this.imageBase64 = response.data.imageBase64;
        this.formData.imagePath = response.data.imagePath;
        console.log(response.data)
        this.haveImage = true;
      });
    },

    lookImage(row) {
      getScenarioImage(row.id)
        .then(blob => {
          this.imageUrl = URL.createObjectURL(blob); // 将 URL 绑定到 data 属性上
        })
        .catch(error => {
          console.error("图像加载失败", error);
        });
      this.lookImages = true
    },

    /** 查询应用场景列表 */
    getList() {
      this.loading = true;
      listScenario(this.queryParams).then(response => {
        this.scenarioList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    deleteImgByPath() {
      if (this.formData.imagePath == null || this.formData.imagePath == "") {
        return;
      } else {
        deleteImgByPath(this.formData).then(response => {
          console.log(response)
        });
      }

    },
    // 取消按钮
    cancel() {
      this.deleteImgByPath()
      this.open = false;
      this.makeImage = false;
        this.makeImage1 = false;
      this.haveImage = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.formData = {
        id: null,
        applicationName: null,
        imagePath: null,
        createBy: null,
        updateBy: null,
        createTime: null,
        updateTime: null,
        prompt: null,
      };
      this.resetForm("formData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      // 表单重置
      this.reset();
      this.open = true;
      this.title = "添加应用场景";

    },
    /** 修改按钮操作 */
    handleUpdate(row) {

      this.reset();
      const id = row.id || this.ids
      getScenario(id).then(response => {
        this.formData = response.data;
        this.open = true;
        this.title = "修改应用场景";
      });
      
      if (row.imagePath ) {
        console.log("修改按钮操作:row.imagePath ",row.imagePath)
        this.imageUrl = true;
      }
      
     
    },

    //创建知识库
    addNewZsk() {

      const now = new Date().toISOString()

      const queryForm = {
        kbId: 'SCENE1_1720504370000',
        kbName: '场景1知识库',
        isCustomProcessRule: 1,
        customProcessRule: 'xxx规则内容',
        isEnhanced: 0,
        createBy: '王怀煜',
        createTime: '',
        updateBy: '王怀煜',
        updateTime: '',
        menuRouting: '/knowledge/scene1'
      }

      console.log('准备提交：', queryForm)

      addNewZsk(queryForm)
        .then(res => {
          if (res.code === 200) {
            this.$message.success('新增成功')
            this.handleBack?.()
          } else {
            this.$message.error(res.msg || '新增失败')
          }
        })
        .catch(err => {
          console.error('提交异常:', err)
          this.$message.error('网络异常或服务器错误')
        })
        .finally(() => {
          this.loading = false
        })

      console.log('已发起创建知识库请求')
    }
    ,
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除应用场景编号为"' + ids + '"的数据项？').then(function () {
        return delScenario(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },


  }
};
</script>
<style scoped>
.blue-font-color {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  cursor: pointer;
}
</style>
