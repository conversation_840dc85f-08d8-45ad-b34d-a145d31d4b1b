<template>

  <div class="detail-page">
    <div class="main-content">


      <div class="section-title">{{ this.form.courseName || '数据集' }}</div>
      <div class="section-tip">
        <i class="el-icon-warning"></i> 应用场景知识库文件上传
      </div>



      <div class="table-area" style="position: relative;">

        <div class="table-header">
          <el-button type="primary" class="add-file-btn" @click="showUploadDialog = true">+ 新增文件</el-button>
        </div>


        <el-table :data="this.courseList" style="width: 100%">
          <!-- 文件名称 -->
          <el-table-column label="文件名称">
            <template slot-scope="scope">

              <div class="file-name-cell" @click="goToChunkDetail(scope.row)">
                <span>{{ scope.row.fileName.split('.')[0] }}</span>
              </div>
            </template>
          </el-table-column>


          <!-- 文件格式 -->
          <el-table-column label="文件格式">
            <template slot-scope="scope">
              <span>{{ scope.row.fileName.split('.').pop() }}</span>
            </template>
          </el-table-column>


          <!-- 数据量 -->
          <el-table-column label="数据量">
            <template slot-scope="scope">
              {{ scope.row.wordCount || '未知' }}
            </template>
          </el-table-column>



          <!-- 文件标签 -->
          <el-table-column label="创建人">
            <template slot-scope="scope">
              {{ scope.row.createBy || '无标签' }}
            </template>
          </el-table-column>
          <!-- 上传时间 -->
          <el-table-column label="上传时间">
            <template slot-scope="scope">
              {{ scope.row.createTime || '未知' }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" align="center" width="250">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="goToChunkDetail(scope.row)">
                查看切片
              </el-button>
              <el-button size="mini" type="text" @click="appendData(scope.row)">
                追加数据
              </el-button>
              <el-button size="mini" type="text" @click="editConfig(scope.row)">
                修改配置
              </el-button>
              <el-button size="mini" type="text" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList"
          style=" position: absolute;  bottom: -55px; right: 20px;" />

        <!-- 暂无数据提示 -->
        <!-- <div class="empty-tip" v-if="datasetFileList.length === 0">
            <i class="el-icon-box"></i>
            导入文件丰富知识库
          </div> -->

        <!-- 分页组件 -->
        <!-- <div class="pagination-wrapper" v-if="datasetFileList.length > 0">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
          </div> -->
      </div>




    </div>


    <!-- 上传弹窗 -->
    <el-dialog :title="`上传文件至${this.changjingName}` + '知识库'" :visible.sync="showUploadDialog" width="600px"
      @close="closeUploadDialog">

      <el-tabs v-model="uploadTab" type="card" class="upload-type-tabs">
        <el-tab-pane label="本地上传" name="local"></el-tab-pane>
      </el-tabs>

      <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
        <el-form-item label="文件类型" prop="parseFlag">
          <el-radio-group v-model="form.parseFlag" @change="parseFlagChange">
            <el-radio label=1>导入文本文档数据</el-radio>
            <el-radio label=0>导入表格型知识数据</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="上传文件" prop="deiDesc">
          <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers"
            multiple :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" :accept="accept"
            :file-size-limit="20 * 1024 * 1024" :before-upload="beforeUpload" :limit="1">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              {{ tip }}
            </div>
            <el-button slot="tip" type="text" v-if="form.parseFlag === '0'" @click="handleDownload">表格模板</el-button>
          </el-upload>
        </el-form-item>

      </el-form>





      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUploadDialog">取消</el-button>
        <el-button type="primary" @click="handleUploadConfirm">确定</el-button>
      </span>
    </el-dialog>



  </div>

</template>

<script>
import { getToken } from "@/utils/auth";
import { addFile, delKbFile, listFile } from "@/api/dataSet/knowledgeBase.js";
export default {
  name: 'DatasetDetail',
  dicts: ['parse_status'],
  data() {
    return {
      rules: {},
      options: [],
      loading: false,
      accept: '.txt,.doc,.pdf',
      tip: '支持txt，doc，pdf文件上传,txt文件不能超过10MB,doc、pdf文件不能超过200MB目不能超过1000页，仅支持单文件',
      tipMark: "1", // 1表示上传txt文件，0表示上传表格文件

      changjingName: '',
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload",
      uploadData: { modeltype: 'zsk' },
      headers: {
        Authorization: "Bearer " + getToken(),
        timeout: 300000
      },
      // 文件上传参数
      form: {
        id: null,
        fileId: null,
        fileName: "文件名称",
        kbId: null,
        filePath: null,
        createdAt: null,
        indexingStatus: null,
        error: null,
        enabled: 1,
        disabledAt: null,
        disabledBy: null,
        displayStatus: null,
        wordCount: null,
        createBy: null,
        createTime: null,
        majorId: null,
        courseName: "场景名称",
        disciplineId: null,
        parseStatus: null,
        submitStatus: null,
        parseFlag: 1,
        isAllianceCourse: null,
        examineFlag: null,
        fileObjectName: "文件唯一标识id", // 建议用字符串
        examineMessage: null,
      },
      subfix: null,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },

      // fileId: [],
      fileList: [],

      datasetId: this.$route.params.id,
      fileList: [],
      // fileId: [],
      datasetFileList: [],
      total: 0,     // 新增：用于存储总条数
      currentPage: 1,
      pageSize: 10,
      shouldDisableUpload: false,
      showUploadDialog: false,
      uploadTab: 'local',
      fileTab: 'file',
      parseOnCreate: false,
      datasetDetail: {},


      // 课程列表数据
      courseList: [],
      total: 0,
      // 遮罩层
      loading: true,




    };
  },
  created() {
   
    this.form.id = this.$route.query.id;
    this.form.courseName = this.$route.query.name;
    this.form.majorId = this.$route.query.major;
    console.log("场景id", this.form.id);
    console.log("场景name", this.form.courseName);
    console.log("专业id", this.form.majorId);
    this.getList();


  },

  methods: {
    goToChunkDetail(row) {
      // this.$router.push(`/zsk/chunk/${this.$route.params.id}/${row.documentId}`);
      console.log("courselist", this.courseList);
      console.log('展示返回列表参数组成---------------------', row);
      this.$emit('展示返回列表参数组成', row);
    },
    getFileSuffix(fileName) {
      if (!fileName || typeof fileName !== 'string') return '';
      const index = fileName.lastIndexOf('.');
      return index > -1 ? fileName.substr(index + 1).toLowerCase() : '';
    },

    parseFlagChange(parseFlag) {
      if (parseFlag == 1) {
        this.accept = '.txt,.doc,.pdf'
        this.tip = '支持txt，doc，pdf文件上传,txt文件不能超过10MB,doc、pdf文件不能超过50MB目不能超过1000页'
        this.tipMark = "1"
      } else {
        this.accept = '.xlsx'
        this.tip = '支持xlsx文件上传'
        this.tipMark = "0"
      }
    }, beforeUpload(file) {
      if (this.tipMark === "1") {
        // 允许的文件类型
        const allowedTypes = ['text/plain', 'application/pdf', 'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const isAllowedType = allowedTypes.includes(file.type);

        if (!isAllowedType) {
          this.$message.error('仅支持上传txt、doc、pdf文件！');
          return false; // 阻止上传
        }

        // 检查文件大小
        const isTxt = file.type === 'text/plain';
        const isDocOrPdf = file.type === 'application/pdf' ||
          file.type === 'application/msword' ||
          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

        if (isTxt && file.size / 1024 / 1024 > 10) {
          this.$message.error('TXT文件大小不能超过10MB！');
          return false; // 阻止上传
        }

        if (isDocOrPdf && file.size / 1024 / 1024 > 200) {
          this.$message.error('DOC或PDF文件大小不能超过200MB！');
          return false; // 阻止上传
        }
      }
      if (this.tipMark === "0") {
        // 允许的文件类型
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
        const isAllowedType = allowedTypes.includes(file.type);

        if (!isAllowedType) {
          this.$message.error('仅支持上传xslx文件！');
          return false; // 阻止上传
        }

        // 检查文件大小
        const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        if (isXlsx && file.size / 1024 / 1024 > 20) {
          this.$message.error('Xlsx文件大小不能超过20MB！');
          return false; // 阻止上传
        }
      }


      // 校验文件名
      const fileName = file.name;
      const maxLength = 50; // 设置文件名最大长度
      const regex = /^[\u4e00-\u9fa5a-zA-Z0-9_.-]+$/; // 匹配中文、英文、数字、下划线(_)、中划线(-)、英文点(.)

      if (fileName.length > maxLength) {
        this.$message.error(`文件名长度不能超过${maxLength}个字符！`);
        return false; // 阻止上传
      }

      if (!regex.test(fileName)) {
        this.$message.error('文件名仅支持中文、英文、数字、下划线(_)、中划线(-)和英文点(.)！');
        return false; // 阻止上传
      }

      return true; // 文件通过验证，可以上传
    },
    // 表单校验
    // validateForm() {
    //   let validate
    //   this.$refs.form.validate((valid) => {
    //     validate = valid
    //   })
    //   return validate
    // },
    handleUploadConfirm() {
      this.$message.success('点击了确定');
      this.showUploadDialog = false;
      this.handleSubmint();
      this.getList();
      // this.listDatasetFile();
    },      //手动提交文件上传
    handleSubmint() {

      if (this.form.fileObjectName != null) {

        addFile(this.form).then(res => {
          if (res.code === 200) {
            this.$message.success('知识库新增文件成功')
            this.loading = false
            // this.handleBack()
            this.getList(); // 刷新数据
          } else {
            console.log("新增文件失败", res);
            this.loading = false
          }
        })
      } else {
        this.$message.warning('请先上传文件,为获取到文件id')
        this.loading = false
        return
      }


    },

    handleUploadSuccess(res, file,) {
      console.log("res打印项内容：", res);
      if (res.code == 200) {
        // file.id = res.data.id;
        this.form.fileObjectName = res.data.id;
        this.form.fileName = res.data.name;
        this.form.filePath = res.data.preview;
        this.form.wordCount = res.data.size;
        this.subfix = res.data.subfix;
        // console.log("this.fileId后端获取到的id：", this.fileId);
        console.log("this.form上传前的参数：", this.form);
        this.fileList.push(file);
      } else {
        this.$message.error(res.msg);
        // this.$refs.upload.handleRemove(file);
      }
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      const id = this.form.id;
      // 使用 Promise 链式调用替代 async/await
      listFile(id, this.queryParams)
        .then(response => {
          console.log("执行获取文件列表", response);
          this.courseList = response.rows;
          console.log("获取 this.courseList文件列表", this.courseList);
          this.total = response.total;
          console.log("知识库文件列表总条数：", this.total);
          this.loading = false; // 不论成功或失败，都关闭 loading
        })
        .catch(error => {
          console.error("请求失败：", error);
        })

    },
    handleDelete(row) {
      console.log("删除文件的数据", row);

      // 删除文件
      this.$confirm('确认删除该文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        const ids = row.id;
        delKbFile(ids).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功');
            this.getList(); // 刷新数据
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(error => {
          console.error('删除文件失败:', error);
          this.$message.error('删除失败: ' + (error.message || '未知错误'));
        });
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 文件删除，暂时不用
    handleRemove(file, fileList) {
      const findex = this.fileId.map(f => f.indexOf(file.id));
      if (findex > -1) {
        this.fileId.splice(findex, 1);
      }

      const fileIndex = this.fileList.findIndex(item => item.id === file.id)
      if (fileIndex > -1) {
        this.fileList.splice(fileIndex, 1);
      }
    },
    // getDatasetInfo() {
    //   const datasetId = this.$route.params.id;

    //   getDatasets(datasetId).then(res => {
    //     console.log("res========", res);
    //     if (res.code === 200) {
    //       this.datasetDetail = res.data || {};
    //       document.title = `${this.datasetDetail.name || '数据集'} - 知识库`;
    //     } else {
    //       this.$message.error(res.msg || '获取数据集详情失败');
    //       this.datasetDetail = { name: '未知数据集' };
    //     }
    //   }).catch(error => {
    //     console.error('获取数据集详情失败:', error);
    //     this.$message.error('获取数据集详情失败: ' + (error.message || '未知错误'));
    //     this.datasetDetail = { name: '未知数据集' };
    //   });
    // }

    // listDatasetFile(page = 1, limit = 10) {
    //   const query = {
    //     datasetId: this.$route.params.id,
    //     pageNum: page,
    //     pageSize: limit
    //   };

    //   this.currentPage = page;
    //   this.pageSize = limit;

    //   listInfo(query).then(res => {
    //     if (res.code === 200) {
    //       this.datasetFileList = res.rows || [];
    //       this.total = res.total || 0;
    //     } else {
    //       this.$message.error(res.msg || '获取数据失败');
    //       this.datasetFileList = [];
    //       this.total = 0;
    //     }
    //   }).catch(error => {
    //     console.error('获取数据集文件列表失败:', error);
    //     this.$message.error('获取数据失败: ' + (error.message || '未知错误'));
    //     this.datasetFileList = [];
    //     this.total = 0;
    //   });
    // },
    showAddFileDialog() {
      this.showUploadDialog = true;
    },
    closeUploadDialog() {
      this.showUploadDialog = false;
    },



    handleProgress(event, file, fileList) {
      // 可处理进度条
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return dateStr;

        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).replace(/\//g, '-');
      } catch (e) {
        return dateStr;
      }
    },
    getFileIcon(fileName) {
      if (!fileName) return '';
      const ext = fileName.split('.').pop().toLowerCase();

      // 根据文件扩展名返回不同的图标
      const iconMap = {
        pdf: 'https://cdn-icons-png.flaticon.com/512/337/337946.png',
        doc: 'https://cdn-icons-png.flaticon.com/512/281/281760.png',
        docx: 'https://cdn-icons-png.flaticon.com/512/281/281760.png',
        xls: 'https://cdn-icons-png.flaticon.com/512/281/281771.png',
        xlsx: 'https://cdn-icons-png.flaticon.com/512/281/281771.png',
        ppt: 'https://cdn-icons-png.flaticon.com/512/281/281761.png',
        pptx: 'https://cdn-icons-png.flaticon.com/512/281/281761.png',
        txt: 'https://cdn-icons-png.flaticon.com/512/281/281758.png',
        jpg: 'https://cdn-icons-png.flaticon.com/512/337/337948.png',
        jpeg: 'https://cdn-icons-png.flaticon.com/512/337/337948.png',
        png: 'https://cdn-icons-png.flaticon.com/512/337/337948.png',
        zip: 'https://cdn-icons-png.flaticon.com/512/337/337944.png',
        rar: 'https://cdn-icons-png.flaticon.com/512/337/337944.png'
      };

      return iconMap[ext] || 'https://cdn-icons-png.flaticon.com/512/281/281746.png'; // 默认图标
    },
    getStatusType(status) {
      // 根据后端返回的状态值映射到Element UI的Tag类型
      const statusMap = {
        0: 'info',    // 未解析
        1: 'warning', // 解析中
        2: 'success', // 解析成功
        3: 'danger'   // 解析失败
      };
      return statusMap[status] || 'info';
    },
    getStatusText(status) {
      // 根据后端返回的状态值映射到显示文本
      const statusMap = {
        0: '未解析',
        1: '解析中',
        2: '已解析',
        3: '解析失败'
      };
      return statusMap[status] || '未知';
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      // 重新加载数据
      this.listDatasetFile(this.currentPage, val);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      // 重新加载数据
      this.listDatasetFile(val, this.pageSize);
    },
    toggleEnabled(row, value) {
      // 处理启用/禁用逻辑
      this.$message.success(`${value ? '启用' : '禁用'}成功`);
    },
    handleEdit(row) {
      // 编辑文件
      this.$message.info('编辑文件：' + row.name);
    },

    handleDownload(row) {
      // 下载文件
      if (!row || !row.id) {
        this.$message.error('文件信息不完整，无法下载');
        return;
      }

      const downloadUrl = `${process.env.VUE_APP_BASE_API}/ragflow/info/download?fileId=${row.id}`;

      // 创建一个隐藏的a标签用于下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', row.name || 'download');
      link.setAttribute('target', '_blank');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$message.success('开始下载文件：' + row.name);
    },
    handleCommand(command, row) {
      if (command === 'download') {
        this.handleDownload(row);
      } else if (command === 'delete') {
        this.handleDelete(row);
      }
    },


    goToIndex() {
      this.$router.push({ path: '/zsk/index' });
    },
    goToConfig() {
      // 实现跳转到配置页面的逻辑
      console.log('跳转到配置页面');
      this.$router.push({
        path: `/zsk/config/${this.$route.params.id}`,
        query: { datasetId: this.datasetId }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-page {
  display: flex;
  min-height: 100vh;
  background: #fff;
}

.sidebar {
  width: 220px;
  background: #fafafa;
  border-right: 1px solid #eaeaea;
  padding-top: 30px;

  .avatar-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    padding: 0 16px;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: #f5f7fa;
      margin-bottom: 12px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #eaeaea;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      i {
        font-size: 40px;
        color: #bbb;
      }
    }

    .username {
      font-size: 16px;
      color: #333;
      font-weight: bold;
    }

    .description {
      font-size: 14px;
      color: #666;
      margin-top: 8px;
      text-align: center;
      line-height: 1.4;
      word-break: break-word;
    }
  }

  .menu {
    border: none;

    .el-menu-item {
      font-size: 16px;
      height: 48px;
      line-height: 48px;

      i {
        margin-right: 10px;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 32px 40px 80px 40px;
  height: 100%;

  .breadcrumb {
    color: #888;
    font-size: 14px;
    margin-bottom: 12px;

    .breadcrumb-link {
      color: #409EFF;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .breadcrumb-text {
      color: #333;
    }
  }

  .section-title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .section-tip {
    color: #e6a23c;
    font-size: 14px;
    margin-bottom: 18px;

    i {
      margin-right: 6px;
    }
  }

  .table-area {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px #0001;
    padding: 24px;
    height: auto;
    // max-height: 100%-50px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 18px;

      .table-left {
        display: flex;
        align-items: center;
      }

      .table-right {
        display: flex;
        align-items: center;
        gap: 12px;

        .search-input {
          width: 240px;
        }

        .add-file-btn {
          font-size: 15px;
          padding: 6px 22px;
        }
      }
    }

    .file-name-cell {
      display: flex;
      align-items: center;

      .file-icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        object-fit: contain;
      }
    }

    .pagination-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      .pagination-info {
        color: #606266;
        font-size: 14px;
      }
    }

    .empty-tip {
      text-align: center;
      color: #bbb;
      font-size: 18px;
      margin: 60px 0;

      i {
        font-size: 42px;
        display: block;
        margin-bottom: 15px;
      }
    }
  }
}

.upload-type-tabs {
  margin-bottom: 0;
}

.file-type-tabs {
  margin-top: 10px;
}

.upload-area {
  width: 100%;
  min-height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;

  .el-icon-upload {
    font-size: 48px;
    color: #409EFF;
    margin-bottom: 8px;
  }

  .el-upload__text {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
  }

  .upload-desc {
    color: #999;
    font-size: 13px;
    margin-top: 8px;
    text-align: center;
  }
}

.upload-folder-placeholder {
  color: #bbb;
  font-size: 16px;
  text-align: center;
  padding: 40px 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}
</style>
