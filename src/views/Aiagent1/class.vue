<template>
  <div class="learning-status-page">
    <!-- 顶部横幅标题 -->
    <div class="hero">
      <div class="hero-text">
        <h1>班级学情智能分析</h1>
        <p>多维行为数据 · AI风险预警 · 精准画像</p>
      </div>
      <el-button type="primary" icon="el-icon-download" @click="downloadReport">下载对标报告</el-button>
    </div>

    <!-- 瀑布流式网格布局 -->
    <div class="masonry-grid">
      <el-card class="masonry-item wide">
        <h3>学情问数总览</h3>
        <div class="metric-grid">
          <div v-for="(m, i) in metrics" :key="i" class="metric-item">
            <div class="metric-value">{{ m.value }}</div>
            <div class="metric-title">{{ m.title }}</div>
          </div>
        </div>
      </el-card>

      <el-card class="masonry-item">
        <h3>学情风险雷达图</h3>
        <div id="radarChart" class="chart-box"></div>
      </el-card>

      <el-card class="masonry-item">
        <h3>行为趋势折线图</h3>
        <div id="lineChart" class="chart-box"></div>
      </el-card>

      <el-card class="masonry-item">
        <h3>AI智能分析摘要</h3>
        <blockquote class="summary-text">{{ summary }}</blockquote>
      </el-card>

      <el-card class="masonry-item wide">
        <h3>维度分析明细</h3>
        <el-table :data="tableData" stripe border>
          <el-table-column prop="dimension" label="维度" width="150" />
          <el-table-column prop="status" label="当前状态" width="120" />
          <el-table-column prop="trend" label="趋势" width="120" />
          <el-table-column prop="advice" label="AI建议分析" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'LearningStatusAnalysis',
  data() {
    return {
      metrics: [
        { title: '到课率', value: '92%' },
        { title: '作业完成率', value: '85%' },
        { title: '在线活跃度', value: '73%' },
        { title: '测评达标率', value: '68%' },
        { title: '异常学生占比', value: '6%' },
        { title: '缺勤次数高频班级', value: '3个' },
      ],
      summary:
        '整体来看，本班级学生到课率较高，但测评成绩和在线活跃度存在一定下滑，个别学生存在频繁缺勤和作业未交等风险行为，建议重点关注后进学生，并加强学习干预手段。',
      tableData: [
        {
          dimension: '到课情况',
          status: '较好',
          trend: '稳定',
          advice: '保持当前出勤管理制度，关注个别缺勤频繁学生。',
        },
        {
          dimension: '作业完成',
          status: '偏低',
          trend: '下滑',
          advice: '引入作业提醒机制，强化督导反馈。',
        },
        {
          dimension: '测评成绩',
          status: '临界',
          trend: '轻微下降',
          advice: '分析薄弱知识点，提供针对性辅导。',
        },
        {
          dimension: '在线活跃',
          status: '一般',
          trend: '波动',
          advice: '提升教学互动，鼓励学生线上参与。',
        },
      ],
    }
  },
  mounted() {
    this.drawRadar()
    this.drawLine()
  },
  methods: {
    downloadReport() {
      this.$message.success('正在生成并下载分析报告...')
    },
    drawRadar() {
      const radar = echarts.init(document.getElementById('radarChart'))
      radar.setOption({
        tooltip: {},
        radar: {
          indicator: [
            { name: '作业完成', max: 100 },
            { name: '到课情况', max: 100 },
            { name: '活跃度', max: 100 },
            { name: '测评成绩', max: 100 },
            { name: '异常行为', max: 100 },
          ],
        },
        series: [
          {
            name: '学情状态',
            type: 'radar',
            data: [
              {
                value: [85, 92, 73, 68, 40],
                name: '当前分析值',
              },
            ],
          },
        ],
      })
    },
    drawLine() {
      const line = echarts.init(document.getElementById('lineChart'))
      line.setOption({
        tooltip: {},
        xAxis: {
          type: 'category',
          data: ['第1周', '第2周', '第3周', '第4周'],
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '作业完成率',
            type: 'line',
            data: [92, 88, 82, 85],
          },
          {
            name: '在线活跃度',
            type: 'line',
            data: [78, 76, 72, 73],
          },
        ],
      })
    },
  },
}
</script>

<style scoped>
.learning-status-page {
  font-family: 'Segoe UI', sans-serif;
  background: #f5f7fa;
  padding: 0;
}

.hero {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to right, #29323c, #485563);
  color: white;
  padding: 30px 40px;
  border-bottom: 3px solid #409EFF;
}

.hero-text h1 {
  font-size: 28px;
  margin: 0 0 8px 0;
}

.hero-text p {
  font-size: 14px;
  opacity: 0.85;
  margin: 0;
}

.masonry-grid {
  column-count: 2;
  column-gap: 20px;
  padding: 30px;
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.masonry-item.wide {
  width: 100%;
  display: inline-block;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 15px;
}

.metric-item {
  background: #eef3f9;
  border-radius: 8px;
  text-align: center;
  padding: 12px 5px;
  box-shadow: inset 0 1px 1px rgba(0,0,0,0.04);
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.metric-title {
  margin-top: 6px;
  font-size: 13px;
  color: #666;
}

.chart-box {
  height: 300px;
}

.summary-text {
  background: #fff8e6;
  border-left: 5px solid #ff9900;
  padding: 16px;
  font-size: 14px;
  color: #444;
  line-height: 1.6;
  margin: 0;
}
</style>
