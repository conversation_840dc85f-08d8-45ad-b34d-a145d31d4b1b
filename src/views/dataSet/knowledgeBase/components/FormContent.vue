<template>
  <div class="app-container">
    <el-form ref="form" :model="form" class="ck-form" :rules="rules" label-width="160px">
      <el-form-item label="院系" prop="affiliatedUnit">
        <el-cascader class="ck-input" v-model="form.affiliatedUnit" :options="options" disabled></el-cascader>
      </el-form-item>
      <el-form-item label="课程" prop="courseName">
        <el-input class="ck-input" v-model="form.courseName" disabled />
      </el-form-item>
      <el-form-item label="是否联盟课" prop="isAllianceCourse">
        <el-radio-group v-model="form.isAllianceCourse" disabled>
          <el-radio label="N">是</el-radio>
          <el-radio label="F">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="文件类型" prop="parseFlag">
        <el-radio-group v-model="form.parseFlag" @change="parseFlagChange">
          <el-radio label="1">导入文本文档数据</el-radio>
          <el-radio label="0">导入表格型知识数据</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="上传文件" prop="deiDesc">
        <el-upload class="upload-demo ck-input" drag :action="uploadUrl" :data="uploadData" :headers="headers" multiple
          :on-success="handleUploadSuccess" :on-remove="handleRemove" :file-list="fileList" :accept="accept"
          :file-size-limit="20 * 1024 * 1024" :before-upload="beforeUpload" :limit="1">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            {{tip}}
          </div>
          <el-button slot="tip" type="text" v-if="form.parseFlag === '0'" @click="handleDownload">表格模板</el-button>
        </el-upload>
      </el-form-item>

    </el-form>
    <el-row class="step-btn-box">
      <el-button type="primary" @click="handleSubmint" :loading="loading">确定</el-button>
      <el-button @click="handleBack" :loading="loading">取消</el-button>
    </el-row>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
import { addCourse, getUniversity } from "@/api/dataSet/knowledgeBase.js";

export default {
  name: 'FormContent',
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/file/upload", //
      uploadData: { modeltype: 'zsk', disciplineId: this.$route.query && this.$route.query.disciplineId },
      headers: {
        Authorization: "Bearer " + getToken(),
        timeout: 300000
      },
      fileId: [],
      fileList: [],
      form: {
        courseName: this.$route.query && this.$route.query.courseName,
        majorId: this.$route.query && this.$route.query.majorId,
        majorName: '',
        disciplineId: this.$route.query && this.$route.query.disciplineId,
        affiliatedUnit: this.$route.query && this.$route.query.affiliatedUnit ? this.$route.query.affiliatedUnit.map(Number) : [],
        isAllianceCourse: this.$route.query && this.$route.query.isAllianceCourse,
        parseFlag: '1'
      },
      rules: {},
      options: [],
      loading: false,
      accept: '.txt,.doc,.pdf',
      tip: '支持txt，doc，pdf文件上传,txt文件不能超过10MB,doc、pdf文件不能超过200MB目不能超过1000页，仅支持单文件',
      tipMark: "1",
    }
  },
  created() {
    this.getUniversity();
  },
  methods: {
    handleUploadSuccess(res, file,) {
      if (res.code == 200) {
        file.id = res.data.id;
        this.fileId.push(res.data.id);
        this.fileList.push(file);
      } else {
        this.$message.error(res.msg);
        this.$refs.upload.handleRemove(file);
      }
    },
    beforeUpload(file) {
      if (this.tipMark === "1") {
        // 允许的文件类型
        const allowedTypes = ['text/plain', 'application/pdf', 'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const isAllowedType = allowedTypes.includes(file.type);

        if (!isAllowedType) {
          this.$message.error('仅支持上传txt、doc、pdf文件！');
          return false; // 阻止上传
        }

        // 检查文件大小
        const isTxt = file.type === 'text/plain';
        const isDocOrPdf = file.type === 'application/pdf' ||
          file.type === 'application/msword' ||
          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

        if (isTxt && file.size / 1024 / 1024 > 10) {
          this.$message.error('TXT文件大小不能超过10MB！');
          return false; // 阻止上传
        }

        if (isDocOrPdf && file.size / 1024 / 1024 > 200) {
          this.$message.error('DOC或PDF文件大小不能超过200MB！');
          return false; // 阻止上传
        }
      }
      if (this.tipMark === "0") {
        // 允许的文件类型
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
        const isAllowedType = allowedTypes.includes(file.type);

        if (!isAllowedType) {
          this.$message.error('仅支持上传xslx文件！');
          return false; // 阻止上传
        }

        // 检查文件大小
        const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        if (isXlsx && file.size / 1024 / 1024 > 20) {
          this.$message.error('Xlsx文件大小不能超过20MB！');
          return false; // 阻止上传
        }
      }


      // 校验文件名
      const fileName = file.name;
      const maxLength = 50; // 设置文件名最大长度
      const regex = /^[\u4e00-\u9fa5a-zA-Z0-9_.-]+$/; // 匹配中文、英文、数字、下划线(_)、中划线(-)、英文点(.)

      if (fileName.length > maxLength) {
        this.$message.error(`文件名长度不能超过${maxLength}个字符！`);
        return false; // 阻止上传
      }

      if (!regex.test(fileName)) {
        this.$message.error('文件名仅支持中文、英文、数字、下划线(_)、中划线(-)和英文点(.)！');
        return false; // 阻止上传
      }

      return true; // 文件通过验证，可以上传
    },


    handleRemove(file, fileList) {
      const findex = this.fileId.map(f => f.indexOf(file.id));
      if (findex > -1) {
        this.fileId.splice(findex, 1);
      }

      const fileIndex = this.fileList.findIndex(item => item.id === file.id)
      if (fileIndex > -1) {
        this.fileList.splice(fileIndex, 1);
      }
    },
    handleSubmint() {
      this.loading = true
      if (this.validateForm()) {
        if (this.fileId && this.fileId.length > 0) {
          const queryForm = {
            ...this.form,
            fileIds: this.fileId
          }
          addCourse(queryForm).then(res => {
            if (res.code === 200) {
              this.$message.success('新增成功')
              this.loading = false
              this.handleBack()
            } else {
              this.loading = false

            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          this.$message.warning('请先上传文件')
          this.loading = false
          return
        }
      } else {
        this.loading = false
      }
    },

    validateForm() {
      let validate
      this.$refs.form.validate((valid) => {
        validate = valid
      })
      return validate
    },
    handleBack() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({
        path: "/knowledgeBase/reviewKnowledgeBase",
        query: {
          courseName: this.form.courseName,
          disciplineId: this.form.disciplineId,
          majorId: this.form.majorId,
          affiliatedUnit: this.$route.query && this.$route.query.affiliatedUnit,
          isAllianceCourse: this.form.isAllianceCourse,
          isPersonCharge: this.$route.query && this.$route.query.isPersonCharge
        },

      });
    },
    getUniversity() {
      getUniversity().then((res) => {
        this.options = res.data.map((item) => {
          return {
            value: item.id,
            label: item.name,
            children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
              return {
                value: item.id,
                label: item.name,
                children: (item.children.length && item.children.length != 0) ? item.children.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                  };
                }) : []
              };
            }) : []
          };
        });
      })
    },
    parseFlagChange(parseFlag) {
      if (parseFlag == 1) {
        this.accept = '.txt,.doc,.pdf'
        this.tip = '支持txt，doc，pdf文件上传,txt文件不能超过10MB,doc、pdf文件不能超过50MB目不能超过1000页'
        this.tipMark = "1"
      } else {
        this.accept = '.xlsx'
        this.tip = '支持xlsx文件上传'
        this.tipMark = "0"
      }
    },
    handleDownload() {
      let a = document.createElement('a');
      let evt = document.createEvent('MouseEvents');
      a.download = '表格模板';
      a.href = '/知识库模板.xlsx';
      evt.initEvent('click', true, true);
      a.dispatchEvent(evt);
      window.URL.revokeObjectURL(a.href);
    }
  }
}
</script>
<style lang="scss" scoped>
.ck-form {
  width: 80%;
  margin: auto;
}
.ck-input {
  width: 50%;
}
.step-btn-box {
  text-align: center;
  height: 67px;
  line-height: 67px;
}
.is-choose {
  color: #1890ff;
}
</style>

