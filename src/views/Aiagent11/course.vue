<template>
  <div class="course-portrait-page">
    <el-container>
      <el-header height="64px" class="header">
        <h2>智能课程画像与资源评估系统</h2>
        <el-button type="primary" icon="el-icon-download" @click="downloadReport">
          下载对标分析报告
        </el-button>
      </el-header>

      <el-main>
        <!-- 指标卡片 -->
        <section class="metrics-cards">
          <el-row :gutter="20">
            <el-col :span="6" v-for="item in metrics" :key="item.key">
              <el-card shadow="hover" class="metric-card">
                <div class="metric-title">{{ item.title }}</div>
                <div class="metric-value">{{ item.value }}</div>
                <div class="metric-desc">{{ item.desc }}</div>
              </el-card>
            </el-col>
          </el-row>
        </section>

        <!-- 图表 -->
        <section class="charts-section">
          <el-row :gutter="30">
            <el-col :span="12">
              <el-card shadow>
                <div class="chart-title">资源有效性评估（雷达图）</div>
                <div ref="radarChart" class="chart-container"></div>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card shadow>
                <div class="chart-title">教学节奏与难度趋势（折线图）</div>
                <div ref="lineChart" class="chart-container"></div>
              </el-card>
            </el-col>
          </el-row>
        </section>

        <!-- 表格 -->
        <section class="table-section">
          <el-card shadow>
            <div class="table-title">课程质量与学习行为数据表</div>
            <el-table :data="tableData" stripe style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: '600' }">
              <el-table-column prop="indicator" label="指标" width="180" />
              <el-table-column prop="score" label="得分" width="100" />
              <el-table-column prop="evaluation" label="评估" />
              <el-table-column prop="suggestion" label="建议" />
            </el-table>
          </el-card>
        </section>

        <!-- 反馈报告 -->
        <section class="report-section">
          <el-card shadow>
            <div class="report-title">智能化反馈报告</div>
            <p v-for="(paragraph, idx) in reportParagraphs" :key="idx" class="report-paragraph">
              {{ paragraph }}
            </p>
          </el-card>
        </section>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import * as echarts from 'echarts'


export default {
  name: 'CoursePortraitPage',
  data() {
    return {
      metrics: [
        { key: 'validity', title: '资源有效性', value: '88%', desc: '课程资源质量综合得分' },
        { key: 'paceWarning', title: '教学节奏预警', value: '中', desc: '教学节奏偏快，建议调整' },
        { key: 'difficultyWarning', title: '难度预警', value: '低', desc: '课程难度适中' },
        { key: 'engagement', title: '学习行为活跃度', value: '75%', desc: '学生参与度较高' }
      ],
      tableData: [
        { indicator: '教学计划完成率', score: 92, evaluation: '良好', suggestion: '保持进度' },
        { indicator: '课件覆盖度', score: 85, evaluation: '合格', suggestion: '补充部分内容' },
        { indicator: '学生课后作业完成率', score: 78, evaluation: '需加强', suggestion: '鼓励及时提交' },
        { indicator: '课堂互动次数', score: 60, evaluation: '一般', suggestion: '增加互动环节' },
        { indicator: '课程反馈满意度', score: 90, evaluation: '较好', suggestion: '继续优化' }
      ],
      reportParagraphs: [
        '本课程整体资源有效性较高，达到了88%的综合得分，说明教学材料质量较好。',
        '教学节奏有中等预警，建议教师适当放缓讲授速度，保证学生吸收效果。',
        '课程难度整体适中，难度预警较低，符合学生的学习能力范围。',
        '学生学习行为活跃，参与度良好，但课堂互动次数还有提升空间。',
        '建议定期更新课程内容，并根据学生反馈灵活调整教学节奏与难度。'
      ],
      radarChart: null,
      lineChart: null,
      timer: null
    }
  },
  methods: {
    initRadarChart() {
      this.radarChart = echarts.init(this.$refs.radarChart)
      const option = {
        tooltip: {},
        radar: {
          indicator: [
            { name: '课件完整度', max: 100 },
            { name: '教学内容质量', max: 100 },
            { name: '学习资源多样性', max: 100 },
            { name: '教学互动性', max: 100 },
            { name: '学生满意度', max: 100 }
          ],
          radius: '65%',
          shape: 'circle',
          splitNumber: 5,
          name: {
            textStyle: { color: '#333', fontSize: 14 }
          },
          splitLine: {
            lineStyle: { color: '#ddd' }
          },
          splitArea: {
            areaStyle: { color: ['#fafafa', '#f0f0f0'] }
          }
        },
        series: [{
          name: '资源有效性',
          type: 'radar',
          data: [
            {
              value: [90, 85, 75, 70, 88],
              name: '本课程'
            }
          ],
          areaStyle: { color: 'rgba(34, 144, 255, 0.3)' },
          lineStyle: { color: '#2196f3' },
          itemStyle: { color: '#2196f3' }
        }]
      }
      this.radarChart.setOption(option)
    },
    initLineChart() {
      this.lineChart = echarts.init(this.$refs.lineChart)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['教学节奏', '难度指数'],
          textStyle: { color: '#333' }
        },
        grid: { left: '10%', right: '10%', bottom: '15%' },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周1', '周2', '周3', '周4', '周5', '周6', '周7'],
          axisLine: { lineStyle: { color: '#ccc' } }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLine: { lineStyle: { color: '#ccc' } },
          splitLine: { lineStyle: { type: 'dashed' } }
        },
        series: [
          {
            name: '教学节奏',
            type: 'line',
            data: [70, 75, 80, 78, 74, 72, 70],
            smooth: true,
            lineStyle: { color: '#42b983' },
            itemStyle: { color: '#42b983' }
          },
          {
            name: '难度指数',
            type: 'line',
            data: [50, 52, 54, 53, 55, 57, 56],
            smooth: true,
            lineStyle: { color: '#f56c6c' },
            itemStyle: { color: '#f56c6c' }
          }
        ]
      }
      this.lineChart.setOption(option)
    },
    downloadReport() {
      const content = `
智能课程对标分析报告

资源有效性: 88%
教学节奏预警: 中
难度预警: 低
学习行为活跃度: 75%

建议：定期更新内容，调整节奏，增强互动。
      `
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = '课程对标分析报告.txt'
      a.click()
      URL.revokeObjectURL(url)
    },
    refreshData() {
      this.metrics.forEach(item => {
        if (item.key === 'validity') {
          item.value = (85 + Math.floor(Math.random() * 10)) + '%'
        } else if (item.key === 'engagement') {
          item.value = (70 + Math.floor(Math.random() * 15)) + '%'
        } else if (item.key === 'paceWarning') {
          const levels = ['低', '中', '高']
          item.value = levels[Math.floor(Math.random() * 3)]
        } else if (item.key === 'difficultyWarning') {
          const levels = ['低', '中', '高']
          item.value = levels[Math.floor(Math.random() * 3)]
        }
      })
    },
    resizeCharts() {
      if (this.radarChart) this.radarChart.resize()
      if (this.lineChart) this.lineChart.resize()
    }
  },
  mounted() {
    this.initRadarChart()
    this.initLineChart()
    this.timer = setInterval(() => {
      this.refreshData()
    }, 5000)
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    clearInterval(this.timer)
    window.removeEventListener('resize', this.resizeCharts)
  }
}
</script>

<style scoped>
.course-portrait-page {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #f0f2f5;
  min-height: 100vh;
  padding: 20px 40px;
  color: #333;
}

.header {
  background: #1e88e5;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  box-shadow: 0 2px 8px rgba(30, 136, 229, .2);
  font-weight: 600;
  font-size: 1.3rem;
  border-radius: 4px;
}

.metrics-cards {
  margin: 25px 0;
}

.metric-card {
  text-align: center;
  background: white;
  border-radius: 6px;
  cursor: default;
  transition: box-shadow 0.3s ease;
}

.metric-card:hover {
  box-shadow: 0 8px 16px rgba(30, 136, 229, 0.3);
}

.metric-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1e88e5;
}

.metric-value {
  font-size: 2.4rem;
  font-weight: 700;
  margin-bottom: 6px;
  color: #2196f3;
}

.metric-desc {
  font-size: 0.9rem;
  color: #666;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-title {
  font-weight: 600;
  font-size: 1.15rem;
  margin-bottom: 12px;
  color: #2c3e50;
}

.chart-container {
  width: 100%;
  height: 320px;
}

.table-section {
  margin-bottom: 30px;
}

.table-title {
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 12px;
  color: #2c3e50;
}

.report-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 6px;
  padding: 20px;
}

.report-title {
  font-weight: 700;
  font-size: 1.3rem;
  margin-bottom: 16px;
  color: #1e88e5;
}

.report-paragraph {
  font-size: 1rem;
  line-height: 1.6;
  color: #444;
  margin-bottom: 12px;
}
</style>
