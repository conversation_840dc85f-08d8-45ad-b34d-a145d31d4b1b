<template>
  <div class="pose-editor-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-section">
        <el-button-group>
          <el-button
            :type="currentMode === 'pose' ? 'primary' : 'default'"
            @click="setMode('pose')"
            size="small"
          >
            姿态编辑
          </el-button>
          <el-button
            :type="currentMode === 'hand' ? 'primary' : 'default'"
            @click="setMode('hand')"
            size="small"
          >
            手部编辑
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-section">
        <el-button @click="resetPose" size="small" icon="el-icon-refresh">重置姿态</el-button>
        <el-button @click="savePose" size="small" icon="el-icon-download">保存场景</el-button>
        <el-button @click="loadPose" size="small" icon="el-icon-upload2">加载场景</el-button>
      </div>

      <div class="toolbar-section">
        <el-button @click="generateMaps" size="small" icon="el-icon-picture">生成AI地图</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 3D视图区域 -->
      <div class="viewport-container">
        <div ref="threeContainer" class="three-container"></div>

        <!-- 视图控制 -->
        <div class="viewport-controls">
          <div class="control-item">
            <span>视图模式:</span>
            <el-radio-group v-model="viewMode" size="mini" @change="onViewModeChange">
              <el-radio-button label="perspective">透视</el-radio-button>
              <el-radio-button label="orthographic">正交</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>

      <!-- 右侧控制面板 -->
      <div class="control-panel">
        <!-- 身体参数调节 -->
        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="身体参数" name="body">
            <div class="parameter-group">
              <div class="parameter-item">
                <label>身高</label>
                <el-slider
                  v-model="bodyParams.height"
                  :min="0.5"
                  :max="2.0"
                  :step="0.01"
                  @change="updateBodyParams"
                />
                <span class="param-value">{{ bodyParams.height.toFixed(2) }}m</span>
              </div>

              <div class="parameter-item">
                <label>体重系数</label>
                <el-slider
                  v-model="bodyParams.weight"
                  :min="0.5"
                  :max="2.0"
                  :step="0.01"
                  @change="updateBodyParams"
                />
                <span class="param-value">{{ bodyParams.weight.toFixed(2) }}</span>
              </div>

              <div class="parameter-item">
                <label>头部大小</label>
                <el-slider
                  v-model="bodyParams.headSize"
                  :min="0.5"
                  :max="1.5"
                  :step="0.01"
                  @change="updateBodyParams"
                />
                <span class="param-value">{{ bodyParams.headSize.toFixed(2) }}</span>
              </div>
            </div>
          </el-collapse-item>

          <el-collapse-item title="关节控制" name="joints">
            <div class="joint-controls" v-if="selectedJoint">
              <h4>{{ selectedJoint.name }}</h4>
              <div class="rotation-controls">
                <div class="rotation-item">
                  <label>X轴旋转</label>
                  <el-slider
                    v-model="jointRotation.x"
                    :min="-180"
                    :max="180"
                    @change="updateJointRotation"
                  />
                  <span>{{ jointRotation.x }}°</span>
                </div>
                <div class="rotation-item">
                  <label>Y轴旋转</label>
                  <el-slider
                    v-model="jointRotation.y"
                    :min="-180"
                    :max="180"
                    @change="updateJointRotation"
                  />
                  <span>{{ jointRotation.y }}°</span>
                </div>
                <div class="rotation-item">
                  <label>Z轴旋转</label>
                  <el-slider
                    v-model="jointRotation.z"
                    :min="-180"
                    :max="180"
                    @change="updateJointRotation"
                  />
                  <span>{{ jointRotation.z }}°</span>
                </div>
              </div>
            </div>
            <div v-else class="no-selection">
              <p>请选择一个关节进行编辑</p>
            </div>
          </el-collapse-item>

          <el-collapse-item title="手势预设" name="gestures" v-if="currentMode === 'hand'">
            <HandGesturePanel :pose-editor="poseEditor" />
          </el-collapse-item>

          <el-collapse-item title="AI地图生成" name="maps">
            <div class="map-controls">
              <el-button @click="generateDepthMap" size="small" style="width: 100%; margin-bottom: 10px;">
                生成深度图
              </el-button>
              <el-button @click="generateNormalMap" size="small" style="width: 100%; margin-bottom: 10px;">
                生成法线图
              </el-button>
              <el-button @click="generateCannyMap" size="small" style="width: 100%; margin-bottom: 10px;">
                生成Canny边缘图
              </el-button>
            </div>

            <!-- 地图预览 -->
            <div class="map-preview" v-if="generatedMaps.depth || generatedMaps.normal || generatedMaps.canny">
              <div v-if="generatedMaps.depth" class="map-item">
                <h5>深度图</h5>
                <canvas ref="depthCanvas" width="256" height="256"></canvas>
              </div>
              <div v-if="generatedMaps.normal" class="map-item">
                <h5>法线图</h5>
                <canvas ref="normalCanvas" width="256" height="256"></canvas>
              </div>
              <div v-if="generatedMaps.canny" class="map-item">
                <h5>Canny边缘图</h5>
                <canvas ref="cannyCanvas" width="256" height="256"></canvas>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 文件上传对话框 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileLoad"
    />
  </div>
</template>

<script>
import PoseEditor3D from './components/PoseEditor3D'
import HandGesturePanel from './components/HandGesturePanel'

export default {
  name: 'Pose3DEditor',
  components: {
    PoseEditor3D,
    HandGesturePanel
  },
  data() {
    return {
      // 编辑模式
      currentMode: 'pose', // 'pose' | 'hand'
      viewMode: 'perspective', // 'perspective' | 'orthographic'

      // UI状态
      activeCollapse: ['body', 'joints', 'gestures'],

      // 身体参数
      bodyParams: {
        height: 1.7,
        weight: 1.0,
        headSize: 1.0
      },

      // 关节控制
      selectedJoint: null,
      jointRotation: {
        x: 0,
        y: 0,
        z: 0
      },

      // AI地图生成
      generatedMaps: {
        depth: null,
        normal: null,
        canny: null
      },

      // 3D编辑器实例
      poseEditor: null
    }
  },
  mounted() {
    this.initPoseEditor()
  },
  beforeDestroy() {
    if (this.poseEditor) {
      this.poseEditor.dispose()
    }
  },
  methods: {
    // 初始化3D编辑器
    async initPoseEditor() {
      try {
        this.poseEditor = new PoseEditor3D(this.$refs.threeContainer)
        await this.poseEditor.init()

        // 监听关节选择事件
        this.poseEditor.on('jointSelected', this.onJointSelected)
        this.poseEditor.on('jointDeselected', this.onJointDeselected)
      } catch (error) {
        console.error('初始化3D编辑器失败:', error)
        this.$message.error('初始化3D编辑器失败')
      }
    },

    // 设置编辑模式
    setMode(mode) {
      this.currentMode = mode
      if (this.poseEditor) {
        this.poseEditor.setMode(mode)
      }
    },

    // 视图模式切换
    onViewModeChange(mode) {
      if (this.poseEditor) {
        this.poseEditor.setCameraMode(mode)
      }
    },

    // 关节选择事件处理
    onJointSelected(joint) {
      this.selectedJoint = joint
      this.jointRotation = {
        x: joint.rotation.x * 180 / Math.PI,
        y: joint.rotation.y * 180 / Math.PI,
        z: joint.rotation.z * 180 / Math.PI
      }
    },

    onJointDeselected() {
      this.selectedJoint = null
      this.jointRotation = { x: 0, y: 0, z: 0 }
    },

    // 更新关节旋转
    updateJointRotation() {
      if (this.selectedJoint && this.poseEditor) {
        this.poseEditor.updateJointRotation(this.selectedJoint.id, {
          x: this.jointRotation.x * Math.PI / 180,
          y: this.jointRotation.y * Math.PI / 180,
          z: this.jointRotation.z * Math.PI / 180
        })
      }
    },

    // 更新身体参数
    updateBodyParams() {
      if (this.poseEditor) {
        this.poseEditor.updateBodyParams(this.bodyParams)
      }
    },

    // 重置姿态
    resetPose() {
      if (this.poseEditor) {
        this.poseEditor.resetPose()
        this.selectedJoint = null
        this.jointRotation = { x: 0, y: 0, z: 0 }
      }
    },

    // 保存场景
    savePose() {
      if (this.poseEditor) {
        const poseData = this.poseEditor.exportPose()
        const dataStr = JSON.stringify(poseData, null, 2)
        const dataBlob = new Blob([dataStr], { type: 'application/json' })

        const link = document.createElement('a')
        link.href = URL.createObjectURL(dataBlob)
        link.download = `pose_${Date.now()}.json`
        link.click()

        this.$message.success('场景已保存')
      }
    },

    // 加载场景
    loadPose() {
      this.$refs.fileInput.click()
    },

    // 处理文件加载
    handleFileLoad(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const poseData = JSON.parse(e.target.result)
          if (this.poseEditor) {
            this.poseEditor.importPose(poseData)
            this.$message.success('场景加载成功')
          }
        } catch (error) {
          console.error('加载场景失败:', error)
          this.$message.error('场景文件格式错误')
        }
      }
      reader.readAsText(file)
    },

    // 生成AI地图
    generateMaps() {
      this.generateDepthMap()
      this.generateNormalMap()
      this.generateCannyMap()
    },

    // 生成深度图
    generateDepthMap() {
      if (this.poseEditor) {
        const depthData = this.poseEditor.generateDepthMap()
        this.generatedMaps.depth = depthData
        this.$nextTick(() => {
          this.renderMapToCanvas(depthData, this.$refs.depthCanvas)
        })
      }
    },

    // 生成法线图
    generateNormalMap() {
      if (this.poseEditor) {
        const normalData = this.poseEditor.generateNormalMap()
        this.generatedMaps.normal = normalData
        this.$nextTick(() => {
          this.renderMapToCanvas(normalData, this.$refs.normalCanvas)
        })
      }
    },

    // 生成Canny边缘图
    generateCannyMap() {
      if (this.poseEditor) {
        const cannyData = this.poseEditor.generateCannyMap()
        this.generatedMaps.canny = cannyData
        this.$nextTick(() => {
          this.renderMapToCanvas(cannyData, this.$refs.cannyCanvas)
        })
      }
    },

    // 将地图数据渲染到canvas
    renderMapToCanvas(imageData, canvas) {
      if (!canvas || !imageData) return

      const ctx = canvas.getContext('2d')
      const imgData = ctx.createImageData(256, 256)

      for (let i = 0; i < imageData.length; i++) {
        imgData.data[i] = imageData[i]
      }

      ctx.putImageData(imgData, 0, 0)
    }
  }
}
</script>

<style lang="scss" scoped>
.pose-editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  gap: 20px;

  .toolbar-section {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.viewport-container {
  flex: 1;
  position: relative;
  background: #2c2c2c;

  .three-container {
    width: 100%;
    height: 100%;
  }

  .viewport-controls {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 4px;
    color: white;

    .control-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }

      span {
        font-size: 12px;
        white-space: nowrap;
      }
    }
  }
}

.control-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e6e6e6;
  overflow-y: auto;

  .parameter-group {
    padding: 10px;
  }

  .parameter-item {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
    }

    .param-value {
      display: inline-block;
      margin-left: 10px;
      font-size: 12px;
      color: #666;
      min-width: 60px;
    }
  }

  .joint-controls {
    padding: 10px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }

    .rotation-item {
      margin-bottom: 15px;

      label {
        display: block;
        margin-bottom: 5px;
        font-size: 12px;
        color: #666;
      }

      span {
        display: inline-block;
        margin-left: 10px;
        font-size: 12px;
        color: #666;
        min-width: 40px;
      }
    }
  }

  .no-selection {
    padding: 20px;
    text-align: center;
    color: #999;
  }

  .map-controls {
    padding: 10px;
  }

  .map-preview {
    padding: 10px;

    .map-item {
      margin-bottom: 15px;

      h5 {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: #666;
      }

      canvas {
        width: 100%;
        height: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
    }
  }
}
</style>
