import * as THREE from 'three'

/**
 * AI地图生成器
 * 用于生成深度图、法线图和Canny边缘检测图
 */
class MapGenerator {
  constructor(scene, camera, renderer) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    
    // 渲染目标
    this.depthRenderTarget = null
    this.normalRenderTarget = null
    this.colorRenderTarget = null
    
    // 材质
    this.depthMaterial = null
    this.normalMaterial = null
    
    // 输出尺寸
    this.outputSize = 512
    
    this.init()
  }
  
  /**
   * 初始化地图生成器
   */
  init() {
    this.createRenderTargets()
    this.createMaterials()
  }
  
  /**
   * 创建渲染目标
   */
  createRenderTargets() {
    // 深度渲染目标
    this.depthRenderTarget = new THREE.WebGLRenderTarget(this.outputSize, this.outputSize, {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      type: THREE.FloatType
    })
    
    // 法线渲染目标
    this.normalRenderTarget = new THREE.WebGLRenderTarget(this.outputSize, this.outputSize, {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      type: THREE.FloatType
    })
    
    // 颜色渲染目标
    this.colorRenderTarget = new THREE.WebGLRenderTarget(this.outputSize, this.outputSize, {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      type: THREE.UnsignedByteType
    })
  }
  
  /**
   * 创建特殊材质
   */
  createMaterials() {
    // 深度材质
    this.depthMaterial = new THREE.ShaderMaterial({
      vertexShader: `
        varying float vDepth;
        
        void main() {
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          gl_Position = projectionMatrix * mvPosition;
          vDepth = -mvPosition.z;
        }
      `,
      fragmentShader: `
        uniform float near;
        uniform float far;
        varying float vDepth;
        
        void main() {
          float depth = (vDepth - near) / (far - near);
          gl_FragColor = vec4(depth, depth, depth, 1.0);
        }
      `,
      uniforms: {
        near: { value: this.camera.near },
        far: { value: this.camera.far }
      }
    })
    
    // 法线材质
    this.normalMaterial = new THREE.ShaderMaterial({
      vertexShader: `
        varying vec3 vNormal;
        varying vec3 vViewNormal;
        
        void main() {
          vNormal = normalize(normalMatrix * normal);
          vViewNormal = normalize((modelViewMatrix * vec4(normal, 0.0)).xyz);
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        varying vec3 vNormal;
        varying vec3 vViewNormal;
        
        void main() {
          vec3 normal = normalize(vViewNormal);
          // 将法线从[-1,1]映射到[0,1]
          normal = normal * 0.5 + 0.5;
          gl_FragColor = vec4(normal, 1.0);
        }
      `
    })
  }
  
  /**
   * 生成深度图
   */
  generateDepthMap() {
    // 保存原始材质
    const originalMaterials = new Map()
    
    // 遍历场景中的所有网格，替换为深度材质
    this.scene.traverse((object) => {
      if (object.isMesh && object.material) {
        originalMaterials.set(object, object.material)
        object.material = this.depthMaterial
      }
    })
    
    // 更新深度材质的uniform
    this.depthMaterial.uniforms.near.value = this.camera.near
    this.depthMaterial.uniforms.far.value = this.camera.far
    
    // 渲染到深度目标
    const originalRenderTarget = this.renderer.getRenderTarget()
    this.renderer.setRenderTarget(this.depthRenderTarget)
    this.renderer.render(this.scene, this.camera)
    
    // 读取像素数据
    const pixels = new Float32Array(this.outputSize * this.outputSize * 4)
    this.renderer.readRenderTargetPixels(
      this.depthRenderTarget, 
      0, 0, 
      this.outputSize, this.outputSize, 
      pixels
    )
    
    // 恢复原始渲染目标和材质
    this.renderer.setRenderTarget(originalRenderTarget)
    originalMaterials.forEach((material, object) => {
      object.material = material
    })
    
    // 转换为8位图像数据
    return this.convertToImageData(pixels, 'depth')
  }
  
  /**
   * 生成法线图
   */
  generateNormalMap() {
    // 保存原始材质
    const originalMaterials = new Map()
    
    // 遍历场景中的所有网格，替换为法线材质
    this.scene.traverse((object) => {
      if (object.isMesh && object.material) {
        originalMaterials.set(object, object.material)
        object.material = this.normalMaterial
      }
    })
    
    // 渲染到法线目标
    const originalRenderTarget = this.renderer.getRenderTarget()
    this.renderer.setRenderTarget(this.normalRenderTarget)
    this.renderer.render(this.scene, this.camera)
    
    // 读取像素数据
    const pixels = new Float32Array(this.outputSize * this.outputSize * 4)
    this.renderer.readRenderTargetPixels(
      this.normalRenderTarget, 
      0, 0, 
      this.outputSize, this.outputSize, 
      pixels
    )
    
    // 恢复原始渲染目标和材质
    this.renderer.setRenderTarget(originalRenderTarget)
    originalMaterials.forEach((material, object) => {
      object.material = material
    })
    
    // 转换为8位图像数据
    return this.convertToImageData(pixels, 'normal')
  }
  
  /**
   * 生成Canny边缘检测图
   */
  generateCannyMap() {
    // 首先渲染普通的颜色图
    const originalRenderTarget = this.renderer.getRenderTarget()
    this.renderer.setRenderTarget(this.colorRenderTarget)
    this.renderer.render(this.scene, this.camera)
    
    // 读取颜色数据
    const pixels = new Uint8Array(this.outputSize * this.outputSize * 4)
    this.renderer.readRenderTargetPixels(
      this.colorRenderTarget, 
      0, 0, 
      this.outputSize, this.outputSize, 
      pixels
    )
    
    this.renderer.setRenderTarget(originalRenderTarget)
    
    // 应用Canny边缘检测算法
    return this.applyCanny(pixels)
  }
  
  /**
   * 应用Canny边缘检测算法
   */
  applyCanny(pixels) {
    const width = this.outputSize
    const height = this.outputSize
    
    // 转换为灰度图
    const grayData = new Float32Array(width * height)
    for (let i = 0; i < pixels.length; i += 4) {
      const gray = 0.299 * pixels[i] + 0.587 * pixels[i + 1] + 0.114 * pixels[i + 2]
      grayData[i / 4] = gray / 255.0
    }
    
    // 高斯模糊
    const blurredData = this.gaussianBlur(grayData, width, height)
    
    // 计算梯度
    const { magnitude, direction } = this.computeGradient(blurredData, width, height)
    
    // 非极大值抑制
    const suppressedData = this.nonMaximumSuppression(magnitude, direction, width, height)
    
    // 双阈值检测和边缘连接
    const edgeData = this.doubleThreshold(suppressedData, width, height, 0.1, 0.2)
    
    // 转换为RGBA格式
    const result = new Uint8Array(width * height * 4)
    for (let i = 0; i < edgeData.length; i++) {
      const value = edgeData[i] * 255
      result[i * 4] = value     // R
      result[i * 4 + 1] = value // G
      result[i * 4 + 2] = value // B
      result[i * 4 + 3] = 255   // A
    }
    
    return result
  }
  
  /**
   * 高斯模糊
   */
  gaussianBlur(data, width, height) {
    const kernel = [
      1/16, 2/16, 1/16,
      2/16, 4/16, 2/16,
      1/16, 2/16, 1/16
    ]
    
    const result = new Float32Array(width * height)
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let sum = 0
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = (y + ky) * width + (x + kx)
            const kernelIdx = (ky + 1) * 3 + (kx + 1)
            sum += data[idx] * kernel[kernelIdx]
          }
        }
        result[y * width + x] = sum
      }
    }
    
    return result
  }
  
  /**
   * 计算梯度
   */
  computeGradient(data, width, height) {
    const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1]
    const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1]
    
    const magnitude = new Float32Array(width * height)
    const direction = new Float32Array(width * height)
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let gx = 0, gy = 0
        
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = (y + ky) * width + (x + kx)
            const kernelIdx = (ky + 1) * 3 + (kx + 1)
            gx += data[idx] * sobelX[kernelIdx]
            gy += data[idx] * sobelY[kernelIdx]
          }
        }
        
        const idx = y * width + x
        magnitude[idx] = Math.sqrt(gx * gx + gy * gy)
        direction[idx] = Math.atan2(gy, gx)
      }
    }
    
    return { magnitude, direction }
  }
  
  /**
   * 非极大值抑制
   */
  nonMaximumSuppression(magnitude, direction, width, height) {
    const result = new Float32Array(width * height)
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x
        const angle = direction[idx]
        const mag = magnitude[idx]
        
        // 将角度量化为4个方向
        let angle_deg = (angle * 180 / Math.PI + 180) % 180
        
        let neighbor1, neighbor2
        
        if ((angle_deg >= 0 && angle_deg < 22.5) || (angle_deg >= 157.5 && angle_deg <= 180)) {
          // 水平方向
          neighbor1 = magnitude[idx - 1]
          neighbor2 = magnitude[idx + 1]
        } else if (angle_deg >= 22.5 && angle_deg < 67.5) {
          // 对角线方向 /
          neighbor1 = magnitude[(y - 1) * width + (x + 1)]
          neighbor2 = magnitude[(y + 1) * width + (x - 1)]
        } else if (angle_deg >= 67.5 && angle_deg < 112.5) {
          // 垂直方向
          neighbor1 = magnitude[(y - 1) * width + x]
          neighbor2 = magnitude[(y + 1) * width + x]
        } else {
          // 对角线方向 \
          neighbor1 = magnitude[(y - 1) * width + (x - 1)]
          neighbor2 = magnitude[(y + 1) * width + (x + 1)]
        }
        
        if (mag >= neighbor1 && mag >= neighbor2) {
          result[idx] = mag
        } else {
          result[idx] = 0
        }
      }
    }
    
    return result
  }
  
  /**
   * 双阈值检测
   */
  doubleThreshold(data, width, height, lowThreshold, highThreshold) {
    const result = new Float32Array(width * height)
    
    // 找到最大值用于归一化
    let maxVal = 0
    for (let i = 0; i < data.length; i++) {
      if (data[i] > maxVal) maxVal = data[i]
    }
    
    const lowThresh = lowThreshold * maxVal
    const highThresh = highThreshold * maxVal
    
    // 分类像素
    for (let i = 0; i < data.length; i++) {
      if (data[i] >= highThresh) {
        result[i] = 1.0 // 强边缘
      } else if (data[i] >= lowThresh) {
        result[i] = 0.5 // 弱边缘
      } else {
        result[i] = 0.0 // 非边缘
      }
    }
    
    // 边缘连接：将与强边缘相连的弱边缘保留
    const visited = new Array(width * height).fill(false)
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x
        if (result[idx] === 1.0 && !visited[idx]) {
          this.edgeTracing(result, visited, x, y, width, height)
        }
      }
    }
    
    // 清除未连接的弱边缘
    for (let i = 0; i < result.length; i++) {
      if (result[i] === 0.5) {
        result[i] = 0.0
      }
    }
    
    return result
  }
  
  /**
   * 边缘追踪
   */
  edgeTracing(data, visited, x, y, width, height) {
    const stack = [[x, y]]
    
    while (stack.length > 0) {
      const [cx, cy] = stack.pop()
      const idx = cy * width + cx
      
      if (visited[idx]) continue
      visited[idx] = true
      
      // 检查8邻域
      for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
          if (dx === 0 && dy === 0) continue
          
          const nx = cx + dx
          const ny = cy + dy
          
          if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
            const nidx = ny * width + nx
            if (!visited[nidx] && data[nidx] >= 0.5) {
              if (data[nidx] === 0.5) {
                data[nidx] = 1.0 // 将弱边缘提升为强边缘
              }
              stack.push([nx, ny])
            }
          }
        }
      }
    }
  }
  
  /**
   * 转换为图像数据
   */
  convertToImageData(pixels, type) {
    const result = new Uint8Array(this.outputSize * this.outputSize * 4)
    
    if (type === 'depth') {
      // 深度图：使用灰度值
      for (let i = 0; i < pixels.length; i += 4) {
        const depth = Math.min(1.0, Math.max(0.0, pixels[i]))
        const value = Math.floor(depth * 255)
        
        result[i] = value     // R
        result[i + 1] = value // G
        result[i + 2] = value // B
        result[i + 3] = 255   // A
      }
    } else if (type === 'normal') {
      // 法线图：RGB分别对应XYZ
      for (let i = 0; i < pixels.length; i += 4) {
        result[i] = Math.floor(Math.min(1.0, Math.max(0.0, pixels[i])) * 255)     // R (X)
        result[i + 1] = Math.floor(Math.min(1.0, Math.max(0.0, pixels[i + 1])) * 255) // G (Y)
        result[i + 2] = Math.floor(Math.min(1.0, Math.max(0.0, pixels[i + 2])) * 255) // B (Z)
        result[i + 3] = 255   // A
      }
    }
    
    return result
  }
  
  /**
   * 设置输出尺寸
   */
  setOutputSize(size) {
    this.outputSize = size
    
    // 重新创建渲染目标
    this.dispose()
    this.createRenderTargets()
  }
  
  /**
   * 销毁地图生成器
   */
  dispose() {
    if (this.depthRenderTarget) {
      this.depthRenderTarget.dispose()
    }
    if (this.normalRenderTarget) {
      this.normalRenderTarget.dispose()
    }
    if (this.colorRenderTarget) {
      this.colorRenderTarget.dispose()
    }
    
    if (this.depthMaterial) {
      this.depthMaterial.dispose()
    }
    if (this.normalMaterial) {
      this.normalMaterial.dispose()
    }
  }
}

export default MapGenerator
