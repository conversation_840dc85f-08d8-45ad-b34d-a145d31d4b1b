<template>
  <div class="hand-gesture-panel">
    <div class="panel-header">
      <h4>手势预设</h4>
      <el-button-group size="mini">
        <el-button 
          :type="selectedHand === 'left' ? 'primary' : 'default'"
          @click="selectedHand = 'left'"
        >
          左手
        </el-button>
        <el-button 
          :type="selectedHand === 'right' ? 'primary' : 'default'"
          @click="selectedHand = 'right'"
        >
          右手
        </el-button>
        <el-button 
          :type="selectedHand === 'both' ? 'primary' : 'default'"
          @click="selectedHand = 'both'"
        >
          双手
        </el-button>
      </el-button-group>
    </div>
    
    <div class="gesture-grid">
      <div 
        v-for="gesture in gestures" 
        :key="gesture.id"
        class="gesture-item"
        @click="applyGesture(gesture.id)"
      >
        <div class="gesture-preview">
          <i :class="gesture.icon"></i>
        </div>
        <div class="gesture-name">{{ gesture.name }}</div>
        <div class="gesture-description">{{ gesture.description }}</div>
      </div>
    </div>
    
    <div class="panel-actions">
      <el-button size="small" @click="resetHandPose" icon="el-icon-refresh">
        重置手部
      </el-button>
      <el-button size="small" @click="mirrorHandPose" icon="el-icon-copy-document">
        镜像复制
      </el-button>
    </div>
    
    <!-- 自定义手势对话框 -->
    <el-dialog
      title="保存自定义手势"
      :visible.sync="saveGestureDialogVisible"
      width="400px"
    >
      <el-form :model="customGesture" label-width="80px">
        <el-form-item label="手势名称">
          <el-input v-model="customGesture.name" placeholder="请输入手势名称"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="customGesture.description" 
            type="textarea" 
            placeholder="请输入手势描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="应用到">
          <el-radio-group v-model="customGesture.applyTo">
            <el-radio label="left">左手</el-radio>
            <el-radio label="right">右手</el-radio>
            <el-radio label="both">双手</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="saveGestureDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCustomGesture">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'HandGesturePanel',
  props: {
    poseEditor: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedHand: 'left',
      saveGestureDialogVisible: false,
      customGesture: {
        name: '',
        description: '',
        applyTo: 'left'
      },
      gestures: [
        {
          id: 'open',
          name: '张开',
          description: '五指张开',
          icon: 'el-icon-s-open'
        },
        {
          id: 'fist',
          name: '握拳',
          description: '紧握拳头',
          icon: 'el-icon-s-cooperation'
        },
        {
          id: 'point',
          name: '指向',
          description: '食指指向',
          icon: 'el-icon-position'
        },
        {
          id: 'peace',
          name: '胜利',
          description: 'V字手势',
          icon: 'el-icon-trophy'
        },
        {
          id: 'ok',
          name: 'OK',
          description: 'OK手势',
          icon: 'el-icon-circle-check'
        },
        {
          id: 'thumbsUp',
          name: '点赞',
          description: '拇指向上',
          icon: 'el-icon-thumb'
        },
        {
          id: 'rock',
          name: '摇滚',
          description: '摇滚手势',
          icon: 'el-icon-headset'
        },
        {
          id: 'gun',
          name: '手枪',
          description: '手枪手势',
          icon: 'el-icon-aim'
        },
        {
          id: 'call',
          name: '打电话',
          description: '打电话手势',
          icon: 'el-icon-phone'
        },
        {
          id: 'relax',
          name: '放松',
          description: '自然放松',
          icon: 'el-icon-sunny'
        }
      ]
    }
  },
  methods: {
    /**
     * 应用手势
     */
    applyGesture(gestureId) {
      if (!this.poseEditor || !this.poseEditor.handEditor) {
        this.$message.warning('手部编辑器未初始化')
        return
      }
      
      const applyToHands = this.selectedHand === 'both' ? ['left', 'right'] : [this.selectedHand]
      
      applyToHands.forEach(hand => {
        this.poseEditor.handEditor.applyHandGesture(hand, gestureId)
      })
      
      this.$message.success(`已应用${this.getGestureName(gestureId)}手势`)
    },
    
    /**
     * 获取手势名称
     */
    getGestureName(gestureId) {
      const gesture = this.gestures.find(g => g.id === gestureId)
      return gesture ? gesture.name : gestureId
    },
    
    /**
     * 重置手部姿态
     */
    resetHandPose() {
      if (!this.poseEditor || !this.poseEditor.handEditor) {
        this.$message.warning('手部编辑器未初始化')
        return
      }
      
      const resetHands = this.selectedHand === 'both' ? 'both' : this.selectedHand
      this.poseEditor.handEditor.resetHandPose(resetHands)
      
      this.$message.success('手部姿态已重置')
    },
    
    /**
     * 镜像复制手势
     */
    mirrorHandPose() {
      if (!this.poseEditor || !this.poseEditor.handEditor) {
        this.$message.warning('手部编辑器未初始化')
        return
      }
      
      if (this.selectedHand === 'both') {
        this.$message.warning('请选择单独的左手或右手进行镜像复制')
        return
      }
      
      // 导出当前选中手的姿态
      const handPoseData = this.poseEditor.handEditor.exportHandPose()
      const sourceHand = this.selectedHand
      const targetHand = sourceHand === 'left' ? 'right' : 'left'
      
      if (!handPoseData[sourceHand]) {
        this.$message.warning('源手部没有姿态数据')
        return
      }
      
      // 创建镜像数据
      const mirroredData = {}
      mirroredData[targetHand] = {}
      
      Object.keys(handPoseData[sourceHand]).forEach(jointName => {
        const jointData = handPoseData[sourceHand][jointName]
        mirroredData[targetHand][jointName] = {
          position: [...jointData.position],
          rotation: [
            -jointData.rotation[0], // X轴旋转镜像
            jointData.rotation[1],  // Y轴旋转保持
            -jointData.rotation[2]  // Z轴旋转镜像
          ],
          scale: [...jointData.scale]
        }
      })
      
      // 应用镜像数据
      this.poseEditor.handEditor.importHandPose(mirroredData)
      
      this.$message.success(`已将${sourceHand === 'left' ? '左' : '右'}手姿态镜像到${targetHand === 'left' ? '左' : '右'}手`)
    },
    
    /**
     * 保存自定义手势
     */
    saveCustomGesture() {
      if (!this.customGesture.name.trim()) {
        this.$message.warning('请输入手势名称')
        return
      }
      
      if (!this.poseEditor || !this.poseEditor.handEditor) {
        this.$message.warning('手部编辑器未初始化')
        return
      }
      
      // 导出当前手势数据
      const handPoseData = this.poseEditor.handEditor.exportHandPose()
      
      // 保存到本地存储
      const customGestures = JSON.parse(localStorage.getItem('customHandGestures') || '[]')
      
      const newGesture = {
        id: `custom_${Date.now()}`,
        name: this.customGesture.name,
        description: this.customGesture.description,
        applyTo: this.customGesture.applyTo,
        data: handPoseData,
        timestamp: Date.now()
      }
      
      customGestures.push(newGesture)
      localStorage.setItem('customHandGestures', JSON.stringify(customGestures))
      
      // 添加到手势列表
      this.gestures.push({
        id: newGesture.id,
        name: newGesture.name,
        description: newGesture.description,
        icon: 'el-icon-star-on',
        custom: true
      })
      
      this.saveGestureDialogVisible = false
      this.customGesture = { name: '', description: '', applyTo: 'left' }
      
      this.$message.success('自定义手势保存成功')
    },
    
    /**
     * 加载自定义手势
     */
    loadCustomGestures() {
      const customGestures = JSON.parse(localStorage.getItem('customHandGestures') || '[]')
      
      customGestures.forEach(gesture => {
        if (!this.gestures.find(g => g.id === gesture.id)) {
          this.gestures.push({
            id: gesture.id,
            name: gesture.name,
            description: gesture.description,
            icon: 'el-icon-star-on',
            custom: true
          })
        }
      })
    },
    
    /**
     * 删除自定义手势
     */
    deleteCustomGesture(gestureId) {
      this.$confirm('确定要删除这个自定义手势吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从本地存储删除
        const customGestures = JSON.parse(localStorage.getItem('customHandGestures') || '[]')
        const filteredGestures = customGestures.filter(g => g.id !== gestureId)
        localStorage.setItem('customHandGestures', JSON.stringify(filteredGestures))
        
        // 从列表删除
        const index = this.gestures.findIndex(g => g.id === gestureId)
        if (index > -1) {
          this.gestures.splice(index, 1)
        }
        
        this.$message.success('自定义手势已删除')
      }).catch(() => {
        // 用户取消删除
      })
    }
  },
  mounted() {
    this.loadCustomGestures()
  }
}
</script>

<style lang="scss" scoped>
.hand-gesture-panel {
  padding: 15px;
  background: white;
  border-radius: 4px;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    h4 {
      margin: 0;
      color: #333;
    }
  }
  
  .gesture-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
    
    .gesture-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
        transform: translateY(-2px);
      }
      
      .gesture-preview {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 50%;
        margin-bottom: 8px;
        
        i {
          font-size: 20px;
          color: #666;
        }
      }
      
      .gesture-name {
        font-size: 12px;
        font-weight: bold;
        color: #333;
        margin-bottom: 4px;
        text-align: center;
      }
      
      .gesture-description {
        font-size: 10px;
        color: #999;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
  
  .panel-actions {
    display: flex;
    gap: 10px;
    
    .el-button {
      flex: 1;
    }
  }
}

// 自定义手势样式
.gesture-item.custom {
  .gesture-preview {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    
    i {
      color: #b8860b;
    }
  }
}
</style>
