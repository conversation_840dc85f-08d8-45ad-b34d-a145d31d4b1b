import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { TransformControls } from 'three/examples/jsm/controls/TransformControls'
import EventEmitter from 'events'
import HandEditor from './HandEditor'
import MapGenerator from './MapGenerator'

/**
 * 3D姿态编辑器核心类
 * 基于Three.js实现3D人体模型的姿态编辑功能
 */
class PoseEditor3D extends EventEmitter {
  constructor(container) {
    super()
    this.container = container

    // Three.js核心对象
    this.scene = null
    this.camera = null
    this.renderer = null
    this.orbitControls = null
    this.transformControls = null

    // 编辑状态
    this.currentMode = 'pose' // 'pose' | 'hand'
    this.selectedJoint = null
    this.isInitialized = false

    // 人体模型相关
    this.humanModel = null
    this.joints = new Map()
    this.bones = new Map()
    this.jointHelpers = new Map()

    // 身体参数
    this.bodyParams = {
      height: 1.7,
      weight: 1.0,
      headSize: 1.0
    }

    // 手部编辑器
    this.handEditor = null

    // 地图生成器
    this.mapGenerator = null

    // 渲染相关
    this.animationId = null

    // 绑定方法
    this.animate = this.animate.bind(this)
    this.onWindowResize = this.onWindowResize.bind(this)
    this.onMouseClick = this.onMouseClick.bind(this)
  }

  /**
   * 初始化3D编辑器
   */
  async init() {
    try {
      this.initScene()
      this.initCamera()
      this.initRenderer()
      this.initControls()
      this.initLights()
      await this.initHumanModel()
      this.initHandEditor()
      this.initMapGenerator()
      this.initEventListeners()

      this.isInitialized = true
      this.animate()

      console.log('3D姿态编辑器初始化完成')
    } catch (error) {
      console.error('3D姿态编辑器初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化场景
   */
  initScene() {
    this.scene = new THREE.Scene()

    // 创建渐变背景
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 512
    const context = canvas.getContext('2d')

    const gradient = context.createLinearGradient(0, 0, 0, 512)
    gradient.addColorStop(0, '#87CEEB') // 天空蓝
    gradient.addColorStop(1, '#F0F8FF') // 爱丽丝蓝

    context.fillStyle = gradient
    context.fillRect(0, 0, 512, 512)

    const texture = new THREE.CanvasTexture(canvas)
    this.scene.background = texture

    // 添加美化的网格辅助线
    const gridHelper = new THREE.GridHelper(10, 20, 0x888888, 0xcccccc)
    gridHelper.position.y = -0.01 // 稍微下沉避免z-fighting
    this.scene.add(gridHelper)

    // 添加坐标轴辅助线
    const axesHelper = new THREE.AxesHelper(1.5)
    axesHelper.position.set(-4, 0, -4) // 移到角落
    this.scene.add(axesHelper)
  }

  /**
   * 初始化相机
   */
  initCamera() {
    const aspect = this.container.clientWidth / this.container.clientHeight

    // 透视相机
    this.perspectiveCamera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000)
    this.perspectiveCamera.position.set(0, 1.6, 3)
    this.perspectiveCamera.lookAt(0, 1, 0)

    // 正交相机
    const frustumSize = 3
    this.orthographicCamera = new THREE.OrthographicCamera(
      -frustumSize * aspect, frustumSize * aspect,
      frustumSize, -frustumSize,
      0.1, 1000
    )
    this.orthographicCamera.position.set(0, 1.6, 3)
    this.orthographicCamera.lookAt(0, 1, 0)

    // 默认使用透视相机
    this.camera = this.perspectiveCamera
    this.currentCameraType = 'perspective'
  }

  /**
   * 初始化渲染器
   */
  initRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true
    })
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    this.renderer.outputEncoding = THREE.sRGBEncoding

    this.container.appendChild(this.renderer.domElement)
  }

  /**
   * 初始化控制器
   */
  initControls() {
    // 轨道控制器
    this.orbitControls = new OrbitControls(this.camera, this.renderer.domElement)
    this.orbitControls.enableDamping = true
    this.orbitControls.dampingFactor = 0.05
    this.orbitControls.target.set(0, 1, 0)
    this.orbitControls.maxDistance = 10
    this.orbitControls.minDistance = 1

    // 变换控制器
    this.transformControls = new TransformControls(this.camera, this.renderer.domElement)
    this.transformControls.setMode('rotate')
    this.transformControls.setSpace('local')
    this.scene.add(this.transformControls)

    // 当变换控制器激活时禁用轨道控制器
    this.transformControls.addEventListener('dragging-changed', (event) => {
      this.orbitControls.enabled = !event.value
    })

    // 监听变换控制器变化
    this.transformControls.addEventListener('change', () => {
      if (this.selectedJoint) {
        this.emit('jointChanged', this.selectedJoint)
      }
    })
  }

  /**
   * 初始化灯光
   */
  initLights() {
    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4)
    this.scene.add(ambientLight)

    // 主方向光 - 模拟太阳光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(5, 10, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    directionalLight.shadow.camera.near = 0.5
    directionalLight.shadow.camera.far = 50
    directionalLight.shadow.camera.left = -5
    directionalLight.shadow.camera.right = 5
    directionalLight.shadow.camera.top = 5
    directionalLight.shadow.camera.bottom = -5
    this.scene.add(directionalLight)

    // 补充光源 - 从另一个角度照亮
    const fillLight = new THREE.DirectionalLight(0x87CEEB, 0.3)
    fillLight.position.set(-5, 5, -5)
    this.scene.add(fillLight)

    // 顶部光源 - 增强头部照明
    const topLight = new THREE.PointLight(0xffffff, 0.5, 10)
    topLight.position.set(0, 8, 0)
    this.scene.add(topLight)

    // 添加地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20)
    const groundMaterial = new THREE.MeshLambertMaterial({
      color: 0xf0f0f0,
      transparent: true,
      opacity: 0.3
    })
    const ground = new THREE.Mesh(groundGeometry, groundMaterial)
    ground.rotation.x = -Math.PI / 2
    ground.position.y = -0.02
    ground.receiveShadow = true
    this.scene.add(ground)
  }

  /**
   * 初始化人体模型
   */
  async initHumanModel() {
    // 创建简化的人体模型
    this.humanModel = new THREE.Group()
    this.humanModel.name = 'HumanModel'

    // 定义人体关节结构
    const jointStructure = this.getJointStructure()

    // 创建关节和骨骼
    this.createJointsAndBones(jointStructure)

    // 添加到场景
    this.scene.add(this.humanModel)

    // 创建关节辅助显示
    this.createJointHelpers()
  }

  /**
   * 初始化手部编辑器
   */
  initHandEditor() {
    this.handEditor = new HandEditor(this.scene, this.camera, this.renderer)

    // 获取左右手的位置
    const leftHandJoint = this.joints.get('left_hand')
    const rightHandJoint = this.joints.get('right_hand')

    if (leftHandJoint && rightHandJoint) {
      this.handEditor.init(leftHandJoint.position, rightHandJoint.position)
      // 初始时隐藏手部编辑器
      this.handEditor.setVisible(false)
    }
  }

  /**
   * 初始化地图生成器
   */
  initMapGenerator() {
    this.mapGenerator = new MapGenerator(this.scene, this.camera, this.renderer)
  }

  /**
   * 获取人体关节结构定义
   */
  getJointStructure() {
    return {
      root: { position: [0, 0, 0], children: ['spine_01', 'left_hip', 'right_hip'] },
      spine_01: { position: [0, 0.1, 0], children: ['spine_02'] },
      spine_02: { position: [0, 0.2, 0], children: ['spine_03'] },
      spine_03: { position: [0, 0.3, 0], children: ['neck', 'left_shoulder', 'right_shoulder'] },

      // 头部
      neck: { position: [0, 0.4, 0], children: ['head'] },
      head: { position: [0, 0.5, 0], children: [] },

      // 左臂
      left_shoulder: { position: [-0.15, 0.3, 0], children: ['left_upper_arm'] },
      left_upper_arm: { position: [-0.3, 0.3, 0], children: ['left_lower_arm'] },
      left_lower_arm: { position: [-0.5, 0.3, 0], children: ['left_hand'] },
      left_hand: { position: [-0.65, 0.3, 0], children: [] },

      // 右臂
      right_shoulder: { position: [0.15, 0.3, 0], children: ['right_upper_arm'] },
      right_upper_arm: { position: [0.3, 0.3, 0], children: ['right_lower_arm'] },
      right_lower_arm: { position: [0.5, 0.3, 0], children: ['right_hand'] },
      right_hand: { position: [0.65, 0.3, 0], children: [] },

      // 左腿
      left_hip: { position: [-0.1, 0, 0], children: ['left_upper_leg'] },
      left_upper_leg: { position: [-0.1, -0.2, 0], children: ['left_lower_leg'] },
      left_lower_leg: { position: [-0.1, -0.5, 0], children: ['left_foot'] },
      left_foot: { position: [-0.1, -0.8, 0], children: [] },

      // 右腿
      right_hip: { position: [0.1, 0, 0], children: ['right_upper_leg'] },
      right_upper_leg: { position: [0.1, -0.2, 0], children: ['right_lower_leg'] },
      right_lower_leg: { position: [0.1, -0.5, 0], children: ['right_foot'] },
      right_foot: { position: [0.1, -0.8, 0], children: [] }
    }
  }

  /**
   * 创建关节和骨骼
   */
  createJointsAndBones(structure) {
    // 创建关节
    Object.keys(structure).forEach(jointName => {
      const jointData = structure[jointName]
      const joint = new THREE.Object3D()
      joint.name = jointName
      joint.position.set(...jointData.position)
      joint.userData = { type: 'joint', originalPosition: [...jointData.position] }

      this.joints.set(jointName, joint)
      this.humanModel.add(joint)
    })

    // 建立父子关系并创建骨骼
    Object.keys(structure).forEach(jointName => {
      const jointData = structure[jointName]
      const parentJoint = this.joints.get(jointName)

      jointData.children.forEach(childName => {
        const childJoint = this.joints.get(childName)
        if (childJoint) {
          parentJoint.add(childJoint)

          // 创建骨骼连接线
          this.createBone(parentJoint, childJoint, `${jointName}_to_${childName}`)
        }
      })
    })
  }

  /**
   * 创建骨骼连接线
   */
  createBone(startJoint, endJoint, boneName) {
    // 计算骨骼长度和方向
    const direction = new THREE.Vector3()
    direction.copy(endJoint.position).normalize()
    const length = endJoint.position.length()

    // 创建圆柱体几何体作为骨骼
    const geometry = new THREE.CylinderGeometry(0.02, 0.02, length, 8)

    // 根据骨骼类型设置不同颜色
    let color = 0x4a90e2 // 默认蓝色
    if (boneName.includes('spine')) color = 0xe74c3c // 脊椎红色
    else if (boneName.includes('arm') || boneName.includes('hand')) color = 0x2ecc71 // 手臂绿色
    else if (boneName.includes('leg') || boneName.includes('foot')) color = 0xf39c12 // 腿部橙色
    else if (boneName.includes('neck') || boneName.includes('head')) color = 0x9b59b6 // 头颈紫色

    const material = new THREE.MeshPhongMaterial({
      color: color,
      shininess: 30,
      transparent: true,
      opacity: 0.8
    })

    const bone = new THREE.Mesh(geometry, material)
    bone.name = boneName
    bone.userData = { type: 'bone' }
    bone.castShadow = true
    bone.receiveShadow = true

    // 调整骨骼位置和旋转
    bone.position.copy(endJoint.position).multiplyScalar(0.5)
    bone.lookAt(endJoint.position)
    bone.rotateX(Math.PI / 2)

    startJoint.add(bone)
    this.bones.set(boneName, bone)
  }

  /**
   * 创建关节辅助显示
   */
  createJointHelpers() {
    this.joints.forEach((joint, name) => {
      // 根据关节类型设置不同大小和颜色
      let radius = 0.03
      let color = 0xff6b6b // 默认红色

      if (name === 'head') {
        radius = 0.08
        color = 0xfeca57 // 头部金色
      } else if (name.includes('spine')) {
        radius = 0.04
        color = 0xff9ff3 // 脊椎粉色
      } else if (name.includes('shoulder')) {
        radius = 0.035
        color = 0x48dbfb // 肩膀蓝色
      } else if (name.includes('hand')) {
        radius = 0.025
        color = 0x0abde3 // 手部青色
      } else if (name.includes('foot')) {
        radius = 0.03
        color = 0x006ba6 // 脚部深蓝
      }

      const geometry = new THREE.SphereGeometry(radius, 12, 12)
      const material = new THREE.MeshPhongMaterial({
        color: color,
        transparent: true,
        opacity: 0.9,
        shininess: 50
      })

      const helper = new THREE.Mesh(geometry, material)
      helper.name = `${name}_helper`
      helper.userData = {
        type: 'jointHelper',
        jointName: name,
        joint: joint
      }
      helper.castShadow = true
      helper.receiveShadow = true

      joint.add(helper)
      this.jointHelpers.set(name, helper)
    })
  }

  /**
   * 初始化事件监听
   */
  initEventListeners() {
    window.addEventListener('resize', this.onWindowResize)
    this.renderer.domElement.addEventListener('click', this.onMouseClick)
  }

  /**
   * 窗口大小改变处理
   */
  onWindowResize() {
    if (!this.isInitialized) return

    const width = this.container.clientWidth
    const height = this.container.clientHeight
    const aspect = width / height

    // 更新透视相机
    this.perspectiveCamera.aspect = aspect
    this.perspectiveCamera.updateProjectionMatrix()

    // 更新正交相机
    const frustumSize = 3
    this.orthographicCamera.left = -frustumSize * aspect
    this.orthographicCamera.right = frustumSize * aspect
    this.orthographicCamera.top = frustumSize
    this.orthographicCamera.bottom = -frustumSize
    this.orthographicCamera.updateProjectionMatrix()

    this.renderer.setSize(width, height)
  }

  /**
   * 切换相机模式
   */
  setCameraMode(mode) {
    if (mode === 'perspective') {
      this.camera = this.perspectiveCamera
      this.currentCameraType = 'perspective'
    } else if (mode === 'orthographic') {
      this.camera = this.orthographicCamera
      this.currentCameraType = 'orthographic'
    }

    // 更新控制器
    this.orbitControls.object = this.camera
    this.transformControls.camera = this.camera

    // 触发窗口大小更新
    this.onWindowResize()
  }

  /**
   * 鼠标点击处理
   */
  onMouseClick(event) {
    const rect = this.renderer.domElement.getBoundingClientRect()
    const mouse = new THREE.Vector2()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    const raycaster = new THREE.Raycaster()
    raycaster.setFromCamera(mouse, this.camera)

    // 如果是手部编辑模式，优先检测手部关节
    if (this.currentMode === 'hand' && this.handEditor) {
      const handJointInfo = this.handEditor.checkHandJointIntersection(raycaster)
      if (handJointInfo) {
        const selectedJoint = this.handEditor.selectHandJoint(handJointInfo)
        this.selectedJoint = selectedJoint
        this.transformControls.attach(selectedJoint.object)
        this.emit('jointSelected', selectedJoint)
        return
      }
    }

    // 检测普通关节辅助器的交互
    const helpers = Array.from(this.jointHelpers.values())
    const intersects = raycaster.intersectObjects(helpers)

    if (intersects.length > 0) {
      const helper = intersects[0].object
      const jointName = helper.userData.jointName
      const joint = helper.userData.joint

      this.selectJoint(jointName, joint)
    } else {
      this.deselectJoint()
    }
  }

  /**
   * 选择关节
   */
  selectJoint(jointName, joint) {
    // 取消之前的选择
    this.deselectJoint()

    this.selectedJoint = {
      id: jointName,
      name: this.getJointDisplayName(jointName),
      object: joint,
      rotation: {
        x: joint.rotation.x,
        y: joint.rotation.y,
        z: joint.rotation.z
      }
    }

    // 高亮显示选中的关节
    const helper = this.jointHelpers.get(jointName)
    if (helper) {
      helper.material.color.setHex(0xffff00)
      helper.material.opacity = 1.0
    }

    // 附加变换控制器
    this.transformControls.attach(joint)

    this.emit('jointSelected', this.selectedJoint)
  }

  /**
   * 取消选择关节
   */
  deselectJoint() {
    if (this.selectedJoint) {
      // 如果是手部关节，使用手部编辑器取消选择
      if (this.selectedJoint.side && this.handEditor) {
        this.handEditor.deselectHandJoint()
      } else {
        // 恢复普通关节辅助器颜色
        const helper = this.jointHelpers.get(this.selectedJoint.id)
        if (helper) {
          helper.material.color.setHex(0xff0000)
          helper.material.opacity = 0.8
        }
      }

      this.selectedJoint = null
      this.transformControls.detach()

      this.emit('jointDeselected')
    }
  }

  /**
   * 获取关节显示名称
   */
  getJointDisplayName(jointName) {
    const nameMap = {
      root: '根部',
      spine_01: '脊椎下段',
      spine_02: '脊椎中段',
      spine_03: '脊椎上段',
      neck: '颈部',
      head: '头部',
      left_shoulder: '左肩',
      left_upper_arm: '左上臂',
      left_lower_arm: '左前臂',
      left_hand: '左手',
      right_shoulder: '右肩',
      right_upper_arm: '右上臂',
      right_lower_arm: '右前臂',
      right_hand: '右手',
      left_hip: '左髋',
      left_upper_leg: '左大腿',
      left_lower_leg: '左小腿',
      left_foot: '左脚',
      right_hip: '右髋',
      right_upper_leg: '右大腿',
      right_lower_leg: '右小腿',
      right_foot: '右脚'
    }

    return nameMap[jointName] || jointName
  }

  /**
   * 动画循环
   */
  animate() {
    if (!this.isInitialized) return

    this.animationId = requestAnimationFrame(this.animate)

    this.orbitControls.update()
    this.renderer.render(this.scene, this.camera)
  }

  /**
   * 设置编辑模式
   */
  setMode(mode) {
    this.currentMode = mode

    if (mode === 'hand') {
      // 手部编辑模式：显示手部详细关节，隐藏普通关节
      this.setHandEditMode(true)
      if (this.handEditor) {
        this.handEditor.setVisible(true)
      }
    } else {
      // 姿态编辑模式：显示所有普通关节，隐藏手部详细关节
      this.setHandEditMode(false)
      if (this.handEditor) {
        this.handEditor.setVisible(false)
      }
    }

    // 取消当前选择
    this.deselectJoint()
  }

  /**
   * 设置手部编辑模式
   */
  setHandEditMode(enabled) {
    const handJoints = ['left_hand', 'right_hand']

    this.jointHelpers.forEach((helper, name) => {
      if (enabled) {
        // 手部编辑模式：只显示手部关节
        helper.visible = handJoints.includes(name)
        if (handJoints.includes(name)) {
          helper.material.color.setHex(0x00ffff) // 青色表示手部关节
        }
      } else {
        // 普通模式：显示所有关节
        helper.visible = true
        helper.material.color.setHex(0xff0000) // 红色
      }
    })
  }

  /**
   * 更新关节旋转
   */
  updateJointRotation(jointId, rotation) {
    // 检查是否是手部关节
    if (jointId.includes('_hand_') && this.handEditor) {
      this.handEditor.updateHandJointRotation(jointId, rotation)
      return
    }

    const joint = this.joints.get(jointId)
    if (joint) {
      joint.rotation.set(rotation.x, rotation.y, rotation.z)

      // 更新骨骼连接线
      this.updateBoneConnections(jointId)
    }
  }

  /**
   * 更新骨骼连接线
   */
  updateBoneConnections(jointId) {
    // 更新从该关节出发的所有骨骼
    this.bones.forEach((bone, boneName) => {
      if (boneName.startsWith(jointId + '_to_')) {
        // 重新计算骨骼的终点位置
        const childJointName = boneName.split('_to_')[1]
        const childJoint = this.joints.get(childJointName)

        if (childJoint && bone.isMesh) {
          // 对于圆柱体骨骼，重新计算位置和旋转
          const direction = new THREE.Vector3()
          direction.copy(childJoint.position).normalize()
          const length = childJoint.position.length()

          // 更新几何体
          bone.geometry.dispose()
          bone.geometry = new THREE.CylinderGeometry(0.02, 0.02, length, 8)

          // 更新位置和旋转
          bone.position.copy(childJoint.position).multiplyScalar(0.5)
          bone.lookAt(childJoint.position)
          bone.rotateX(Math.PI / 2)
        }
      }
    })
  }

  /**
   * 更新身体参数
   */
  updateBodyParams(params) {
    this.bodyParams = { ...this.bodyParams, ...params }

    // 应用身高缩放
    if (params.height !== undefined) {
      const scale = params.height / 1.7 // 基准身高1.7m
      this.humanModel.scale.setScalar(scale)
    }

    // 应用体重系数（影响骨骼粗细）
    if (params.weight !== undefined) {
      this.bones.forEach(bone => {
        bone.material.linewidth = Math.max(1, 3 * params.weight)
      })
    }

    // 应用头部大小
    if (params.headSize !== undefined) {
      const headJoint = this.joints.get('head')
      if (headJoint) {
        headJoint.scale.setScalar(params.headSize)
      }
    }
  }

  /**
   * 重置姿态
   */
  resetPose() {
    this.joints.forEach(joint => {
      joint.rotation.set(0, 0, 0)
      joint.position.copy(new THREE.Vector3(...joint.userData.originalPosition))
    })

    // 重置手部姿态
    if (this.handEditor) {
      this.handEditor.resetHandPose()
    }

    this.deselectJoint()
    this.updateAllBoneConnections()
  }

  /**
   * 更新所有骨骼连接
   */
  updateAllBoneConnections() {
    this.joints.forEach((joint, jointId) => {
      this.updateBoneConnections(jointId)
    })
  }

  /**
   * 导出姿态数据
   */
  exportPose() {
    const poseData = {
      version: '1.0',
      timestamp: Date.now(),
      bodyParams: { ...this.bodyParams },
      joints: {},
      handPose: null
    }

    this.joints.forEach((joint, name) => {
      poseData.joints[name] = {
        position: joint.position.toArray(),
        rotation: joint.rotation.toArray(),
        scale: joint.scale.toArray()
      }
    })

    // 导出手部姿态数据
    if (this.handEditor) {
      poseData.handPose = this.handEditor.exportHandPose()
    }

    return poseData
  }

  /**
   * 导入姿态数据
   */
  importPose(poseData) {
    if (!poseData || !poseData.joints) {
      throw new Error('无效的姿态数据')
    }

    // 应用身体参数
    if (poseData.bodyParams) {
      this.updateBodyParams(poseData.bodyParams)
    }

    // 应用关节数据
    Object.keys(poseData.joints).forEach(jointName => {
      const joint = this.joints.get(jointName)
      const jointData = poseData.joints[jointName]

      if (joint && jointData) {
        if (jointData.position) {
          joint.position.fromArray(jointData.position)
        }
        if (jointData.rotation) {
          joint.rotation.fromArray(jointData.rotation)
        }
        if (jointData.scale) {
          joint.scale.fromArray(jointData.scale)
        }
      }
    })

    // 导入手部姿态数据
    if (poseData.handPose && this.handEditor) {
      this.handEditor.importHandPose(poseData.handPose)
    }

    this.updateAllBoneConnections()
    this.deselectJoint()
  }

  /**
   * 生成深度图
   */
  generateDepthMap() {
    if (!this.mapGenerator) {
      console.warn('地图生成器未初始化')
      return new Uint8Array(256 * 256 * 4)
    }

    return this.mapGenerator.generateDepthMap()
  }

  /**
   * 生成法线图
   */
  generateNormalMap() {
    if (!this.mapGenerator) {
      console.warn('地图生成器未初始化')
      return new Uint8Array(256 * 256 * 4)
    }

    return this.mapGenerator.generateNormalMap()
  }

  /**
   * 生成Canny边缘图
   */
  generateCannyMap() {
    if (!this.mapGenerator) {
      console.warn('地图生成器未初始化')
      return new Uint8Array(256 * 256 * 4)
    }

    return this.mapGenerator.generateCannyMap()
  }

  /**
   * 销毁编辑器
   */
  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }

    window.removeEventListener('resize', this.onWindowResize)

    if (this.renderer) {
      this.renderer.domElement.removeEventListener('click', this.onMouseClick)
      this.container.removeChild(this.renderer.domElement)
      this.renderer.dispose()
    }

    if (this.orbitControls) {
      this.orbitControls.dispose()
    }

    if (this.transformControls) {
      this.transformControls.dispose()
    }

    // 清理手部编辑器
    if (this.handEditor) {
      this.handEditor.dispose()
    }

    // 清理地图生成器
    if (this.mapGenerator) {
      this.mapGenerator.dispose()
    }

    // 清理Three.js对象
    this.scene?.clear()
    this.joints.clear()
    this.bones.clear()
    this.jointHelpers.clear()

    this.isInitialized = false
  }
}

export default PoseEditor3D
