import * as THREE from 'three'

/**
 * 手部精细编辑器
 * 专门处理手部骨骼的精细编辑功能
 */
class HandEditor {
  constructor(scene, camera, renderer) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer

    // 手部模型
    this.leftHand = null
    this.rightHand = null

    // 手部关节
    this.leftHandJoints = new Map()
    this.rightHandJoints = new Map()

    // 手部关节辅助器
    this.leftHandHelpers = new Map()
    this.rightHandHelpers = new Map()

    // 选中状态
    this.selectedHandJoint = null

    // 手部关节结构定义
    this.handStructure = this.getHandStructure()
  }

  /**
   * 获取手部关节结构
   */
  getHandStructure() {
    return {
      // 拇指
      thumb_01: { position: [0.02, 0, 0.01], children: ['thumb_02'] },
      thumb_02: { position: [0.04, 0, 0.02], children: ['thumb_03'] },
      thumb_03: { position: [0.06, 0, 0.03], children: [] },

      // 食指
      index_01: { position: [0.08, 0, 0], children: ['index_02'] },
      index_02: { position: [0.12, 0, 0], children: ['index_03'] },
      index_03: { position: [0.16, 0, 0], children: [] },

      // 中指
      middle_01: { position: [0.08, 0, -0.02], children: ['middle_02'] },
      middle_02: { position: [0.13, 0, -0.02], children: ['middle_03'] },
      middle_03: { position: [0.18, 0, -0.02], children: [] },

      // 无名指
      ring_01: { position: [0.08, 0, -0.04], children: ['ring_02'] },
      ring_02: { position: [0.12, 0, -0.04], children: ['ring_03'] },
      ring_03: { position: [0.16, 0, -0.04], children: [] },

      // 小指
      pinky_01: { position: [0.06, 0, -0.06], children: ['pinky_02'] },
      pinky_02: { position: [0.09, 0, -0.06], children: ['pinky_03'] },
      pinky_03: { position: [0.12, 0, -0.06], children: [] }
    }
  }

  /**
   * 初始化手部编辑器
   */
  init(leftHandPosition, rightHandPosition) {
    this.createHandModel('left', leftHandPosition)
    this.createHandModel('right', rightHandPosition)
    this.createHandHelpers()
  }

  /**
   * 创建手部模型
   */
  createHandModel(side, position) {
    const handGroup = new THREE.Group()
    handGroup.name = `${side}_hand_detailed`
    handGroup.position.copy(position)

    const joints = side === 'left' ? this.leftHandJoints : this.rightHandJoints

    // 创建手部关节
    Object.keys(this.handStructure).forEach(jointName => {
      const jointData = this.handStructure[jointName]
      const joint = new THREE.Object3D()
      joint.name = `${side}_${jointName}`

      // 对于右手，需要镜像X坐标
      const pos = [...jointData.position]
      if (side === 'right') {
        pos[0] = -pos[0]
      }

      joint.position.set(...pos)
      joint.userData = {
        type: 'handJoint',
        side: side,
        originalPosition: [...pos]
      }

      joints.set(jointName, joint)
      handGroup.add(joint)
    })

    // 建立父子关系并创建骨骼
    Object.keys(this.handStructure).forEach(jointName => {
      const jointData = this.handStructure[jointName]
      const parentJoint = joints.get(jointName)

      jointData.children.forEach(childName => {
        const childJoint = joints.get(childName)
        if (childJoint) {
          parentJoint.add(childJoint)
          this.createHandBone(parentJoint, childJoint, `${side}_${jointName}_to_${childName}`)
        }
      })
    })

    if (side === 'left') {
      this.leftHand = handGroup
    } else {
      this.rightHand = handGroup
    }

    this.scene.add(handGroup)
  }

  /**
   * 创建手部骨骼连接
   */
  createHandBone(startJoint, endJoint, boneName) {
    const geometry = new THREE.BufferGeometry()
    const positions = new Float32Array([
      0, 0, 0,
      endJoint.position.x, endJoint.position.y, endJoint.position.z
    ])
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))

    const material = new THREE.LineBasicMaterial({
      color: 0x00ffff, // 青色表示手部骨骼
      linewidth: 2
    })

    const bone = new THREE.Line(geometry, material)
    bone.name = boneName
    bone.userData = { type: 'handBone' }

    startJoint.add(bone)
  }

  /**
   * 创建手部关节辅助器
   */
  createHandHelpers() {
    // 左手辅助器
    this.leftHandJoints.forEach((joint, name) => {
      const helper = this.createJointHelper(joint, name, 'left')
      this.leftHandHelpers.set(name, helper)
    })

    // 右手辅助器
    this.rightHandJoints.forEach((joint, name) => {
      const helper = this.createJointHelper(joint, name, 'right')
      this.rightHandHelpers.set(name, helper)
    })
  }

  /**
   * 创建关节辅助器
   */
  createJointHelper(joint, name, side) {
    const geometry = new THREE.SphereGeometry(0.008, 8, 8)

    // 根据手指类型使用不同颜色
    let color = 0xff0000 // 默认红色
    if (name.startsWith('thumb')) color = 0xff6600    // 橙色 - 拇指
    else if (name.startsWith('index')) color = 0xffff00    // 黄色 - 食指
    else if (name.startsWith('middle')) color = 0x00ff00   // 绿色 - 中指
    else if (name.startsWith('ring')) color = 0x0000ff     // 蓝色 - 无名指
    else if (name.startsWith('pinky')) color = 0xff00ff    // 紫色 - 小指

    const material = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.8
    })

    const helper = new THREE.Mesh(geometry, material)
    helper.name = `${side}_${name}_helper`
    helper.userData = {
      type: 'handJointHelper',
      jointName: name,
      side: side,
      joint: joint
    }

    joint.add(helper)
    return helper
  }

  /**
   * 显示/隐藏手部编辑器
   */
  setVisible(visible) {
    if (this.leftHand) {
      this.leftHand.visible = visible
    }
    if (this.rightHand) {
      this.rightHand.visible = visible
    }
  }

  /**
   * 检测手部关节点击
   */
  checkHandJointIntersection(raycaster) {
    const allHelpers = [
      ...Array.from(this.leftHandHelpers.values()),
      ...Array.from(this.rightHandHelpers.values())
    ]

    const intersects = raycaster.intersectObjects(allHelpers)

    if (intersects.length > 0) {
      const helper = intersects[0].object
      return {
        helper: helper,
        jointName: helper.userData.jointName,
        side: helper.userData.side,
        joint: helper.userData.joint
      }
    }

    return null
  }

  /**
   * 选择手部关节
   */
  selectHandJoint(jointInfo) {
    // 取消之前的选择
    this.deselectHandJoint()

    this.selectedHandJoint = jointInfo

    // 高亮显示
    jointInfo.helper.material.color.setHex(0xffffff) // 白色高亮
    jointInfo.helper.material.opacity = 1.0

    return {
      id: `${jointInfo.side}_${jointInfo.jointName}`,
      name: this.getHandJointDisplayName(jointInfo.jointName, jointInfo.side),
      object: jointInfo.joint,
      side: jointInfo.side,
      rotation: {
        x: jointInfo.joint.rotation.x,
        y: jointInfo.joint.rotation.y,
        z: jointInfo.joint.rotation.z
      }
    }
  }

  /**
   * 取消选择手部关节
   */
  deselectHandJoint() {
    if (this.selectedHandJoint) {
      // 恢复原始颜色
      const helper = this.selectedHandJoint.helper
      const jointName = this.selectedHandJoint.jointName

      let color = 0xff0000
      if (jointName.startsWith('thumb')) color = 0xff6600
      else if (jointName.startsWith('index')) color = 0xffff00
      else if (jointName.startsWith('middle')) color = 0x00ff00
      else if (jointName.startsWith('ring')) color = 0x0000ff
      else if (jointName.startsWith('pinky')) color = 0xff00ff

      helper.material.color.setHex(color)
      helper.material.opacity = 0.8

      this.selectedHandJoint = null
    }
  }

  /**
   * 获取手部关节显示名称
   */
  getHandJointDisplayName(jointName, side) {
    const sideText = side === 'left' ? '左' : '右'

    const nameMap = {
      thumb_01: '拇指根部',
      thumb_02: '拇指中部',
      thumb_03: '拇指指尖',
      index_01: '食指根部',
      index_02: '食指中部',
      index_03: '食指指尖',
      middle_01: '中指根部',
      middle_02: '中指中部',
      middle_03: '中指指尖',
      ring_01: '无名指根部',
      ring_02: '无名指中部',
      ring_03: '无名指指尖',
      pinky_01: '小指根部',
      pinky_02: '小指中部',
      pinky_03: '小指指尖'
    }

    return `${sideText}${nameMap[jointName] || jointName}`
  }

  /**
   * 更新手部关节旋转
   */
  updateHandJointRotation(jointId, rotation) {
    const [side, ...nameParts] = jointId.split('_')
    const jointName = nameParts.join('_')

    const joints = side === 'left' ? this.leftHandJoints : this.rightHandJoints
    const joint = joints.get(jointName)

    if (joint) {
      joint.rotation.set(rotation.x, rotation.y, rotation.z)
      this.updateHandBoneConnections(side, jointName)
    }
  }

  /**
   * 更新手部骨骼连接
   */
  updateHandBoneConnections(side, jointName) {
    const joints = side === 'left' ? this.leftHandJoints : this.rightHandJoints
    const joint = joints.get(jointName)

    if (!joint) return

    // 更新该关节的子骨骼
    joint.children.forEach(child => {
      if (child.userData.type === 'handBone') {
        const positions = child.geometry.attributes.position.array
        // 骨骼的终点应该是子关节的相对位置
        const childJoint = child.parent.children.find(c => c !== child && c.userData.type === 'handJoint')
        if (childJoint) {
          positions[3] = childJoint.position.x
          positions[4] = childJoint.position.y
          positions[5] = childJoint.position.z
          child.geometry.attributes.position.needsUpdate = true
        }
      }
    })
  }

  /**
   * 重置手部姿态
   */
  resetHandPose(side = 'both') {
    if (side === 'both' || side === 'left') {
      this.leftHandJoints.forEach(joint => {
        joint.rotation.set(0, 0, 0)
        joint.position.copy(new THREE.Vector3(...joint.userData.originalPosition))
      })
    }

    if (side === 'both' || side === 'right') {
      this.rightHandJoints.forEach(joint => {
        joint.rotation.set(0, 0, 0)
        joint.position.copy(new THREE.Vector3(...joint.userData.originalPosition))
      })
    }

    this.deselectHandJoint()
    this.updateAllHandBoneConnections()
  }

  /**
   * 更新所有手部骨骼连接
   */
  updateAllHandBoneConnections() {
    // 更新左手
    this.leftHandJoints.forEach((joint, jointName) => {
      this.updateHandBoneConnections('left', jointName)
    })

    // 更新右手
    this.rightHandJoints.forEach((joint, jointName) => {
      this.updateHandBoneConnections('right', jointName)
    })
  }

  /**
   * 导出手部姿态数据
   */
  exportHandPose() {
    const handPoseData = {
      left: {},
      right: {}
    }

    // 导出左手数据
    this.leftHandJoints.forEach((joint, name) => {
      handPoseData.left[name] = {
        position: joint.position.toArray(),
        rotation: joint.rotation.toArray(),
        scale: joint.scale.toArray()
      }
    })

    // 导出右手数据
    this.rightHandJoints.forEach((joint, name) => {
      handPoseData.right[name] = {
        position: joint.position.toArray(),
        rotation: joint.rotation.toArray(),
        scale: joint.scale.toArray()
      }
    })

    return handPoseData
  }

  /**
   * 导入手部姿态数据
   */
  importHandPose(handPoseData) {
    if (!handPoseData) return

    // 导入左手数据
    if (handPoseData.left) {
      Object.keys(handPoseData.left).forEach(jointName => {
        const joint = this.leftHandJoints.get(jointName)
        const jointData = handPoseData.left[jointName]

        if (joint && jointData) {
          if (jointData.position) joint.position.fromArray(jointData.position)
          if (jointData.rotation) joint.rotation.fromArray(jointData.rotation)
          if (jointData.scale) joint.scale.fromArray(jointData.scale)
        }
      })
    }

    // 导入右手数据
    if (handPoseData.right) {
      Object.keys(handPoseData.right).forEach(jointName => {
        const joint = this.rightHandJoints.get(jointName)
        const jointData = handPoseData.right[jointName]

        if (joint && jointData) {
          if (jointData.position) joint.position.fromArray(jointData.position)
          if (jointData.rotation) joint.rotation.fromArray(jointData.rotation)
          if (jointData.scale) joint.scale.fromArray(jointData.scale)
        }
      })
    }

    this.updateAllHandBoneConnections()
    this.deselectHandJoint()
  }

  /**
   * 应用预设手势
   */
  applyHandGesture(side, gestureName) {
    const gestures = this.getHandGestures()
    const gesture = gestures[gestureName]

    if (!gesture) return

    const joints = side === 'left' ? this.leftHandJoints : this.rightHandJoints

    Object.keys(gesture).forEach(jointName => {
      const joint = joints.get(jointName)
      if (joint && gesture[jointName]) {
        joint.rotation.fromArray(gesture[jointName])
      }
    })

    this.updateAllHandBoneConnections()
  }

  /**
   * 获取预设手势
   */
  getHandGestures() {
    return {
      // 握拳
      fist: {
        index_01: [0, 0, -1.2],
        index_02: [0, 0, -1.5],
        index_03: [0, 0, -1.2],
        middle_01: [0, 0, -1.2],
        middle_02: [0, 0, -1.5],
        middle_03: [0, 0, -1.2],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, -1.2],
        pinky_02: [0, 0, -1.5],
        pinky_03: [0, 0, -1.2],
        thumb_01: [0, 0, 0.5],
        thumb_02: [0, 0, 0.8],
        thumb_03: [0, 0, 0.5]
      },

      // 张开手掌
      open: {
        index_01: [0, 0, 0],
        index_02: [0, 0, 0],
        index_03: [0, 0, 0],
        middle_01: [0, 0, 0],
        middle_02: [0, 0, 0],
        middle_03: [0, 0, 0],
        ring_01: [0, 0, 0],
        ring_02: [0, 0, 0],
        ring_03: [0, 0, 0],
        pinky_01: [0, 0, 0],
        pinky_02: [0, 0, 0],
        pinky_03: [0, 0, 0],
        thumb_01: [0, 0, 0],
        thumb_02: [0, 0, 0],
        thumb_03: [0, 0, 0]
      },

      // 指向手势
      point: {
        index_01: [0, 0, 0],
        index_02: [0, 0, 0],
        index_03: [0, 0, 0],
        middle_01: [0, 0, -1.2],
        middle_02: [0, 0, -1.5],
        middle_03: [0, 0, -1.2],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, -1.2],
        pinky_02: [0, 0, -1.5],
        pinky_03: [0, 0, -1.2],
        thumb_01: [0, 0, 0.5],
        thumb_02: [0, 0, 0.8],
        thumb_03: [0, 0, 0.5]
      },

      // V字手势（胜利）
      peace: {
        index_01: [0, 0, 0],
        index_02: [0, 0, 0],
        index_03: [0, 0, 0],
        middle_01: [0, 0, 0],
        middle_02: [0, 0, 0],
        middle_03: [0, 0, 0],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, -1.2],
        pinky_02: [0, 0, -1.5],
        pinky_03: [0, 0, -1.2],
        thumb_01: [0, 0, 0.5],
        thumb_02: [0, 0, 0.8],
        thumb_03: [0, 0, 0.5]
      },

      // OK手势
      ok: {
        index_01: [0, 0, -0.8],
        index_02: [0, 0, -1.2],
        index_03: [0, 0, -1.0],
        middle_01: [0, 0, 0],
        middle_02: [0, 0, 0],
        middle_03: [0, 0, 0],
        ring_01: [0, 0, 0],
        ring_02: [0, 0, 0],
        ring_03: [0, 0, 0],
        pinky_01: [0, 0, 0],
        pinky_02: [0, 0, 0],
        pinky_03: [0, 0, 0],
        thumb_01: [0, 0, 0.8],
        thumb_02: [0, 0, 1.2],
        thumb_03: [0, 0, 0.8]
      },

      // 点赞手势
      thumbsUp: {
        index_01: [0, 0, -1.2],
        index_02: [0, 0, -1.5],
        index_03: [0, 0, -1.2],
        middle_01: [0, 0, -1.2],
        middle_02: [0, 0, -1.5],
        middle_03: [0, 0, -1.2],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, -1.2],
        pinky_02: [0, 0, -1.5],
        pinky_03: [0, 0, -1.2],
        thumb_01: [0, 0, 0],
        thumb_02: [0, 0, 0],
        thumb_03: [0, 0, 0]
      },

      // 摇滚手势
      rock: {
        index_01: [0, 0, 0],
        index_02: [0, 0, 0],
        index_03: [0, 0, 0],
        middle_01: [0, 0, -1.2],
        middle_02: [0, 0, -1.5],
        middle_03: [0, 0, -1.2],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, 0],
        pinky_02: [0, 0, 0],
        pinky_03: [0, 0, 0],
        thumb_01: [0, 0, 0.5],
        thumb_02: [0, 0, 0.8],
        thumb_03: [0, 0, 0.5]
      },

      // 手枪手势
      gun: {
        index_01: [0, 0, 0],
        index_02: [0, 0, 0],
        index_03: [0, 0, 0],
        middle_01: [0, 0, -1.2],
        middle_02: [0, 0, -1.5],
        middle_03: [0, 0, -1.2],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, -1.2],
        pinky_02: [0, 0, -1.5],
        pinky_03: [0, 0, -1.2],
        thumb_01: [0, 0, 0],
        thumb_02: [0, 0, 0],
        thumb_03: [0, 0, 0]
      },

      // 打电话手势
      call: {
        index_01: [0, 0, -1.2],
        index_02: [0, 0, -1.5],
        index_03: [0, 0, -1.2],
        middle_01: [0, 0, -1.2],
        middle_02: [0, 0, -1.5],
        middle_03: [0, 0, -1.2],
        ring_01: [0, 0, -1.2],
        ring_02: [0, 0, -1.5],
        ring_03: [0, 0, -1.2],
        pinky_01: [0, 0, 0],
        pinky_02: [0, 0, 0],
        pinky_03: [0, 0, 0],
        thumb_01: [0, 0, 0],
        thumb_02: [0, 0, 0],
        thumb_03: [0, 0, 0]
      },

      // 放松手势
      relax: {
        index_01: [0, 0, -0.3],
        index_02: [0, 0, -0.5],
        index_03: [0, 0, -0.3],
        middle_01: [0, 0, -0.3],
        middle_02: [0, 0, -0.5],
        middle_03: [0, 0, -0.3],
        ring_01: [0, 0, -0.3],
        ring_02: [0, 0, -0.5],
        ring_03: [0, 0, -0.3],
        pinky_01: [0, 0, -0.3],
        pinky_02: [0, 0, -0.5],
        pinky_03: [0, 0, -0.3],
        thumb_01: [0, 0, 0.2],
        thumb_02: [0, 0, 0.3],
        thumb_03: [0, 0, 0.2]
      }
    }
  }

  /**
   * 销毁手部编辑器
   */
  dispose() {
    if (this.leftHand) {
      this.scene.remove(this.leftHand)
    }
    if (this.rightHand) {
      this.scene.remove(this.rightHand)
    }

    this.leftHandJoints.clear()
    this.rightHandJoints.clear()
    this.leftHandHelpers.clear()
    this.rightHandHelpers.clear()

    this.selectedHandJoint = null
  }
}

export default HandEditor
