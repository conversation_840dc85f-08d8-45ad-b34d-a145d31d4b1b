<template>
  <div class="assistant-button-top">
    <!-- 右侧栏 -->
    <div class="sidebarRight">
      <el-button type="primary" id="startButton" class="start-button" @click="toggleDialog">
        <span class="button-text">
          <i class="el-icon-magic-stick"></i>
        </span>
      </el-button>
    </div>

    <div v-if="isDialogVisible" class="dialog">
      <!-- 标题区域 -->
      <div class="dialog-title" style="  border-bottom: 1px solid #ddd;">
        <span style="font-size: 20px; color: #333;">智能助手</span>
        <!-- 关闭按钮 -->
        <button @click="isDialogVisible = false"
          style="position: absolute; top: 10px; right: 10px; background: none; border: none; cursor: pointer;">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="feather feather-x">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="dialog-content" style="height: calc(80vh - 140px); overflow-y: auto;">
        <!-- 开场白部分 -->
        <el-row type="flex" justify="center" style="margin-top: 20px;">
          <img :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />
          <div style="width:100%;">
            <div
              style="float:left;background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              <div>
                <div class="markdown-content" v-html="markedContent('**您好，我是您的智能助手！**\n如果您对本课程有任何疑问，您可以直接向我提问。')"></div>
              </div>
            </div>
          </div>
        </el-row>

        <!-- 对话内容区域 -->
        <el-row v-for="(item, index) in dialogueList" :key="index" type="flex" justify="center">
          <img v-if="item.issue == 'assistant'" :src="asks" style="width: 33px; height: 33px; margin-right: 10px;" />

          <div v-if="item.issue == 'assistant'" style="width:100%;">
            <div
              style="float:left;background:#d9eaf9;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              <div>
                <div class="markdown-content" v-html="markedContent(item.content)"></div>
                <div style="position:absolute; bottom:-23px; left:5px;">
                  <!-- 重新生成按钮 -->
                  <el-button v-if="!loadSendBtn && index == dialogueList.length-1" size="mini" type="text"
                    class="copy-button" @click="handleRegen(index)"
                    style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-left: 40px;">重新生成</el-button>
                  <el-button v-if="loadSendBtn && index == dialogueList.length-1" size="mini" type="text"
                    class="copy-button" @click="handleStopGeneration()"
                    style="background-color: transparent; border: none; cursor: pointer; color: #007bff; margin-left: 40px;">停止生成</el-button>
                </div>
              </div>
            </div>
          </div>

          <div v-if="item.issue == 'user'" style="width:100%;">
            <div
              style="float:right;background:#efefff;border-radius:5px;padding:5px;line-height:30px;white-space: pre-wrap">
              {{item.content}}</div>
          </div>
        </el-row>
      </div>

      <!-- 输入框和其他交互元素 -->
      <div class="right-container-bottom" style="margin-top: -10px;">
        <div
          style="border:1px solid #efefff;margin-top: 10px;padding: 10px 10px 2px 10px;background-color: #fff;border-radius: 12px ;">
          <el-input v-model="content" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="2000"
            show-word-limit @keydown.native="handleKeyCode($event)" @blur="saveEditedContent"
            placeholder="请输入您想问的内容，按 Enter 发送， Ctrl+Enter 换行" />
        </div>
        <el-button style="float: right;margin: 5px;" type="primary" size="mini" round icon="el-icon-s-promotion"
          @click="ask" :loading="loadSendBtn" />
      </div>
    </div>
  </div>
</template>

<script>
import marked from "marked";
import Cookies from "js-cookie";
import axios from "axios";
import qs from "qs";
import asks from "@/assets/logo/asks.png";
import user from "@/assets/logo/user.png";
import { getToken } from "@/utils/auth";
import {
  getId,
  likeOrStomp,
  stopGeneration,
  relatedIssues
} from "@/api/explorationCenter/experience.js";
export default {
  name: 'Assistant',
  dicts: ['hot_questions',],

  data() {
    return {
      isDialogVisible: false,
      showRightSidebar: false,
      dialogueList: [],
      loadSendBtn: false,
      isClickable: false,
      dialogueNum: true,
      content: "",
      id: "",
      asks: asks,
      user: user,
      invocation: "knowledgeBase",
      popularQuestions: [
        '发布作业是什么流程？',
        '论文研读有什么作用。',
        '如何创建知识库。'
      ]
    };
  },
  watch: {
    showRightSidebar(newValue) {
      const sidebar = document.querySelector('.sidebarRight');
      if (newValue) {
        sidebar.classList.add('expanded');
      } else {
        sidebar.classList.remove('expanded');
      }
    }
  },
  methods: {
    toggleDialog() {
      this.isDialogVisible = !this.isDialogVisible;
    },
    closeDialog() {
      this.isDialogVisible = false;
    },
    askPopularQuestion(question) {
      // 将选中的热门问题作为用户输入的内容
      this.content = question;
      // 调用你的ask方法来发送这个问题
      this.ask();
    },


    markedContent(content) {
      const htmlContent = marked.parse(content);
      // 创建一个临时的DOM元素来处理HTML内容
      const tempElement = document.createElement('div');
      tempElement.innerHTML = htmlContent;
      // 递归函数来移除空白文本节点
      function removeEmptyTextNodes(node) {
        if (node.nodeType === Node.TEXT_NODE && !node.textContent.trim()) {
          node.parentNode.removeChild(node);
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          for (let i = node.childNodes.length - 1; i >= 0; i--) {
            removeEmptyTextNodes(node.childNodes[i]);
          }
        }
      }
      // 遍历所有块级元素并移除空白文本节点
      const blockElements = ['p', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'blockquote', 'hr'];
      blockElements.forEach(tagName => {
        const elements = tempElement.querySelectorAll(tagName);
        elements.forEach(element => {
          removeEmptyTextNodes(element);
        });
      });
      // 返回处理后的HTML内容
      return tempElement.innerHTML;
    },
    async addDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recordingdirect/addLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      this.isClickable = true;
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        let imageUrls = []; // 新增数组用于存储图片URL

        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            if (this.playFlag && !this.txtToImage) {
              this.playAudio(s);
            }
            this.isClickable = false;
            this.getHeight();
          } else {
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.dialogueList[this.dialogueList.length - 1].imageUrls = imageUrls;
              }
            });
            this.getHeight();
          }
        }
      } finally {
        reader.releaseLock();
      }
    },

    getImgUrl(pptPath) {
      let baseUrl = window.location.origin
      var imgData;
      if (pptPath.includes('/ruoyi/')) {
        // 替换路径中的 ruoyi/ 之前的部分
        const finalPath = pptPath.replace(/.*?\/ruoyi\//, '/home/<USER>/')

        if (baseUrl.includes('192.168') || baseUrl.includes('localhost') || baseUrl.includes('127.0')) {
          imgData = 'http://127.0.0.1:9215' + finalPath
          // console.log('Final baseUrl:', baseUrl)
        } else {
          imgData = baseUrl + finalPath
        }
      }
      return imgData;

    },

    saveEditedContent() {
      // 用户编辑完成后同步最终内容
      this.finalText = this.content;
      console.log("保存用户编辑后的结果:", this.finalText);
    },
    async updateDialogue2(data) {
      const response = await fetch(process.env.VUE_APP_BASE_API + '/intelligent/recording/updateLiu', {
        method: 'POST',
        headers: {
          Authorization: "Bearer " + getToken(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      this.isClickable = true;
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      let num1 = 1;
      let imageUrls = [];
      // 使用ReadableStream处理流式数据
      const reader = response.body.getReader();
      let done = false;
      try {
        this.dialogueList[this.dialogueList.length - 1].content = "";
        let s = "";
        while (!done) {
          const { value, done: isDone } = await reader.read();
          if (isDone) {
            done = true;
            this.loadSendBtn = false;
            this.dialogueNum = false;
            if (this.playFlag && !this.txtToImage) {
              this.playAudio(s);
            }
            this.handleReview({ id: this.id })
            this.isClickable = false;
            this.getHeight()
          } else {
            // 将接收到的数据转换为字符串
            const str = new TextDecoder('utf-8').decode(value);
            s += str;

            const imageRegex = /:([^<>]*)<<<|:([^<>]*)>>>/g;
            let match;
            while ((match = imageRegex.exec(str)) !== null) {
              const imagePath = match[2] || match[1]; // 获取匹配的图片路径
              imageUrls.push(this.getImgUrl(imagePath)); // 将所有匹配的图片路径添加到数组中
            }

            // 更新对话内容，排除图片路径
            const contentWithoutImages = s.replace(imageRegex, (match, p1, p2) => {
              return ''; // 替换匹配的部分为空字符串
            });

            // 移除多余的符号
            const cleanedContent = contentWithoutImages.replace(/::|<<<|>>>|:/g, '');
            this.$nextTick(() => {
              this.dialogueList[this.dialogueList.length - 1].content = cleanedContent;
              // 如果有图片URL，则更新对话项的imageUrls属性
              if (imageUrls.length > 0) {
                this.dialogueList[this.dialogueList.length - 1].imageUrls = imageUrls;
              }
            });
            this.getHeight()
          }
        }
      } finally {
        reader.releaseLock(); // 确保在循环结束时释放锁
      }
    },


    handlePlay() {
      if (this.playFlag) {
        this.playFlag = !this.playFlag
        this.pause()
      } else {
        this.playFlag = !this.playFlag
      }
    },


    pause() {
      ttsRecorder.stop();
    },


    handleProgress(event, file, fileList) {
      // 更新进度信息
      this.progress.visible = true;
      this.progress.percentage = parseInt(event.percent);
      // 可以根据需要设置进度条状态，例如上传失败时设置为 'exception'
    },

    handleAdd() {
      this.dialogueNum = true;
      this.dialogueList = [];
      this.fileList = [];
      this.content = "";
      this.id = "";
      this.relatedIssuesList = [];
    },
    async getId() {
      await getId()
        .then((res) => {
          if (res.code === 200) {
            this.id = res.data;
          }
        })
        .catch((err) => {
          this.loadSendBtn = false;
        });
    },
    //请求后端
    async ask() {
      if (this.translationFlag) {
        this.closeASR();
      } // 关闭语音输入
      if (!this.content || this.content == '') {
        this.$message.error('请先输入您的问题');
        return false;
      }
      this.isClickable = true;
      this.loadSendBtn = true;
      this.dialogueList.push({ content: this.content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.haveImage = false
      // this.dialogueList.push({content: '', issue: "assistant"});
      this.getHeight()
      //首次发请求
      console.log(this.dialogueNum);
      if (this.dialogueNum) {
        //后端ai询问
        await this.getId();
        console.log("========" + this.id);
        const param = {
          invocation: this.invocation,
          content: this.content,
          menuRouting: "zhushou",
          id: this.id,
          language: Cookies.get("voiceType"),
        };
        console.log(param);
        //置空content
        this.content = '';
        this.relatedIssuesList = [];
        //发送请求
        await this.addDialogue2(param).then(() => {
          // 确保对话列表中至少有两个元素
          if (this.dialogueList.length >= 2) {
            const quae = {
              content: this.dialogueList[this.dialogueList.length - 2].content,
            };
          }
        }).catch(error => {
          this.isClickable = false;
          console.error('添加对话时出错:', error);
        });
        this.haveImage = true
        this.handleReview({ id: this.id })
      } else {
        //
        console.log("************" + this.id);
        const param = {
          content: this.content,
          invocation: this.invocation,
          id: this.id,
          menuRouting: "zhushou",
          language: Cookies.get("voiceType"),
        };

        this.content = ''
        this.relatedIssuesList = [];
        //
        this.updateDialogue2(param).then(() => {
          // 确保对话列表中至少有两个元素
          if (this.dialogueList.length >= 2) {
            const quae = {
              content: this.dialogueList[this.dialogueList.length - 2].content,
            };
          }
        })
          .catch(error => {
            this.isClickable = false;
            console.error('Error:', error)
          });
      }

    },


    markdownToPlainText(markdown) {
      if (!markdown) return '';

      // 移除 Markdown 标题
      markdown = markdown.replace(/^#+\s(.+)/gm, '$1');

      // 移除 Markdown 图片和链接
      markdown = markdown.replace(/!\[.*?\]\(.*?\)/g, '');
      markdown = markdown.replace(/\[.*?\]\(.*?\)/g, '');

      // 移除 Markdown 粗体和斜体
      markdown = markdown.replace(/\*\*(.*?)\*\*/g, '$1'); // 粗体
      markdown = markdown.replace(/\*(.*?)\*/g, '$1');     // 斜体
      markdown = markdown.replace(/__(.*?)__/g, '$1');     // 粗体
      markdown = markdown.replace(/_(.*?)_/g, '$1');       // 斜体

      // 移除 Markdown 代码块和行内代码
      markdown = markdown.replace(/```[\s\S]*?```/g, '');
      markdown = markdown.replace(/`(.*?)`/g, '$1');

      // 移除 Markdown 分割线
      markdown = markdown.replace(/-{3,}/g, '');

      // 移除 Markdown 列表
      markdown = markdown.replace(/^\s*[-*+]\s+/gm, '');
      markdown = markdown.replace(/^\d+\.\s+/gm, '');

      // 移除 Markdown 引用
      markdown = markdown.replace(/^>\s+/gm, '');

      // 移除 Markdown 表格
      markdown = markdown.replace(/\|.*?\|/g, '');
      markdown = markdown.replace(/-\|/g, '');

      // 移除多余的换行和空格
      markdown = markdown.replace(/\n{2,}/g, '\n');
      markdown = markdown.trim();

      return markdown;
    },

    handleKeyCode(event) {
      if (event.keyCode == 13 && event.ctrlKey) {
        this.content += "\n";
      } else if (event.keyCode == 13) {
        event.preventDefault();
        this.ask()
      }
    },
    // 重新生成
    handleRegen(index) {
      this.isClickable = true;
      this.loadSendBtn = true;
      this.dialogueList.push({ content: this.dialogueList[index - 1].content, issue: "user" });
      this.dialogueList.push({ content: '正在生成，请稍等···', issue: "assistant" });
      this.haveImage = false
      this.getHeight()
      const param = {
        invocation: this.invocation,
        content: this.dialogueList[index - 1].content,
        id: this.id,
        menuRouting: "zhushou",
        language: Cookies.get("voiceType"),
      };

      this.content = '';
      this.relatedIssuesList = [];
      //
      this.updateDialogue2(param).then(() => {
        // 确保对话列表中至少有两个元素
        if (this.dialogueList.length >= 2) {
          const quae = {
            content: this.dialogueList[this.dialogueList.length - 2].content,
          };

          // 调用 relatedIssues 函数并传递 quae 对象
          relatedIssues(quae).then((res) => {
            if (res.code === 200) {
              this.relatedIssuesList = res.data;
            }
          });
        }
      })
        .catch(error => console.error('Error:', error));
    },
    // 点赞/点踩
    likeOrStomp(item, index, type) {
      const param = {
        id: item.id,
        likeStomp: type
      }
      likeOrStomp(param).then(res => {
        if (res.code == 200) {
          this.dialogueList[index].likeStomp = type
        }
      })
    },
    getHeight() {
      this.$nextTick(() => {
        var container = document.querySelector('.right-container-top');
        container.scrollTop = container.scrollHeight;
      })
    },
    handleStopGeneration() {
      console.log("---------" + this.id);
      stopGeneration(this.id).then(res => {

      })
    },
    changeTxtToImage() {
      this.txtToImage = !this.txtToImage
      this.isActive = !this.isActive;
    }
  },
};
</script>

<style scoped>
.assistant-button-top {
  position: fixed;
  right: 30px;
  top: 80px;
  z-index: 9990;
}

.dialog {
  position: fixed;
  bottom: 70px;
  right: 20px;
  background-color: white;
  padding: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 9990;
  width: 90vw; /* 宽度为视口宽度的90% */
  max-width: 600px; /* 最大宽度为600px */
  height: auto; /* 自动高度 */
  max-height: 80vh; /* 最大高度为视口高度的80% */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 8px; /* 增加圆角 */
}

/* 设置对话内容区域的高度和滚动条 */
.dialog-content {
  flex-grow: 1; /* 让对话内容区域扩展以填充可用空间 */
  overflow-y: auto; /* 如果内容超出最大高度，则启用垂直滚动条 */
  max-height: calc(80vh - 140px); /* 固定最大高度 */
}

.right-container-bottom {
  margin-top: auto; /* 使输入框部分始终位于底部 */
}

.sidebarRight {
  position: fixed;
  right: 2vh;
  top: 11vh;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgb(102, 177, 255);
  transition: width 0.3s ease, background-color 0.3s ease, transform 0.3s ease;
  overflow: hidden;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: stretch;
}

.sidebarRight:hover {
  background-color: rgb(179, 216, 255);
}

.start-button {
  width: 100%;
  flex-grow: 1;
  border: none;
  background-color: rgb(102, 177, 255);
  color: white;
  box-shadow: none;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.button-text {
  font-size: 24px;
  opacity: 1; /* 默认情况下隐藏文字 */
  transition: opacity 0.3s ease;
}

.sidebarRight.expanded .button-text {
  opacity: 1; /* 当侧边栏展开时显示文字 */
}

.start-button:hover {
  background-color: rgb(121, 187, 255); /* 鼠标悬停时背景色变深 */
}

.start-button:active {
  background-color: rgb(64, 158, 255); /* 按钮被按下时背景色更深 */
  transform: scale(0.95); /* 按钮被按下时轻微缩小 */
}

.issue-text {
  display: inline-block;
  padding: 5px 10px;
  border: 1px solid #ebebeb;
  background-color: #fff;
  /*color: #7d83c5;*/
  border-radius: 8px;
  transition: border-color 0.3s ease;
  margin-bottom: 5px;
}

.issue-text:hover {
  border-color: blue;
}
</style>
