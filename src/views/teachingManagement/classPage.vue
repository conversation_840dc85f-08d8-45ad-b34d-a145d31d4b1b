<template>
  <div class="app-container">
    <el-form size="small" :inline="true">
      <el-form-item label="课程单元" prop="unitName">
        <el-select v-model="unitName" placeholder="请选择课程单元" @change="changeUnit">
          <el-option v-for="item in unitList" :key="item.id" :label="item.name" :value="item.name">
          </el-option>
        </el-select> </el-form-item>
    </el-form>

    <div class="assistant-button-top">
      <Assistant />
    </div>

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="教学资料" name="first">
        <el-table :data="resourcesList" style="width: 100%">
          <el-table-column prop="name" label="资料" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="作业" name="second">
        <el-table :data="homeWorkList" style="width: 100%">
          <el-table-column prop="title" label="试题" />
          <el-table-column prop="startTime" label="开始时间" />
          <el-table-column prop="endTime" label="截止时间" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleDoHomeWork(scope.row)">写作业</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="写作业" :visible.sync="dialogVisible" width="80%" :before-close="handleClose">
      <div class="app-container ck-container">
        <div class="questions_box" v-if="testPaperQuestions.length !== 0">
          <h1 style="text-align: center">{{ testPaperName }}</h1>
          <el-form :model="form" ref="form">
            <div v-for="(item, index) in testPaperQuestions" :key="item.id">
              <div>
                <p style="font-weight: 500">
                  第{{ index + 1 }}题：{{ item.question }}
                  <span v-if="item.questionType == 'single'">（单选）</span>
                  <span v-if="item.questionType == 'multiple'">（多选）</span>
                  <span v-if="item.questionType == 'blank'">（填空）</span>
                  <span v-if="item.questionType == 'shortAnswer'">（简答）</span>
                </p>
              </div>
              <div v-if="item.questionType == 'single'">
                <el-form-item label="">
                  <el-radio-group v-model="item.userAnswer">
                    <el-radio v-for="items in item.questionbankQuestionOptions" :key="items.id"
                      :label="items.optionMark">{{ items.optionMark + "." + items.optionText }}</el-radio>
                  </el-radio-group>

                </el-form-item>
              </div>
              <div v-if="item.questionType == 'multiple'">
                <el-form-item label="">
                  <el-checkbox-group v-model="item.userAnswer">
                    <el-checkbox v-for="items in item.questionbankQuestionOptions" :key="items.id"
                      :label="items.optionMark">{{ items.optionMark + "." + items.optionText }}</el-checkbox>
                  </el-checkbox-group>

                </el-form-item>
              </div>
              <div v-if="item.questionType == 'blank'">
                <el-form-item label="">
                  <el-input v-model="item.userAnswer"></el-input>
                </el-form-item>
              </div>
              <div v-if="item.questionType == 'shortAnswer'">
                <el-form-item label="">
                  <el-input type="textarea" v-model="item.userAnswer"></el-input>
                </el-form-item>
              </div>
            </div>
            <div></div>
          </el-form>
        </div>
        <div v-else>
          <div class="none" style="margin-left: 0px">
            <div class="none_img"></div>
            <h3>暂无试题</h3>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Assistant from './components/Assistant.vue'

export default {
  name: 'ClassPage',
  components: { Assistant },
  data() {
    return {
      form: {},
      unitName: '',
      activeName: 'first',
      activeNames: 'first',
      dialogVisible: false,
      courseInfo: {},
      resourcesList: [],
      questionsList: [],
      aiList: [
        {
          "createTime": "2025-05-26 19:43:58",
          "id": 20980,
          "orderIn": 1,
          "issue": "user",
          "content": "指向函数为什么不可以储存任意函数的地址",
          "score": 0,
        },
        {
          "createTime": "2025-05-26 19:44:21",
          "id": 20982,
          "orderIn": 2,
          "issue": "assistant",
          "content": "知识点：函数指针\n\n在C语言中，指向函数的指针（函数指针）确实可以存储函数的地址，但并不是可以存储**任意**函数的地址。这是因为函数指针的类型必须与它所指向的函数的类型相匹配。\n\n每个函数在内存中都有一个唯一的入口地址，函数指针就是用来指向这个地址的。但是，不同的函数可能有不同的参数列表和返回值类型。函数指针的类型定义了它所指向的函数的参数类型和返回值类型。因此，一个特定类型的函数指针只能指向与其类型相匹配的函数。\n\n例如，如果你有一个函数指针，它的类型是`int (*)(int, int)`，这意味着它可以指向任何接受两个int参数并返回一个int值的函数。如果你试图让这个指针指向一个不接受两个int参数，或者不返回一个int值的函数，那么编译器就会报错。\n\n总的来说，虽然函数指针可以存储函数的地址，但它的类型限制了它可以指向哪些函数。所以，它并不能存储任意函数的地址。\n\n<b>请同学复习以下知识点：: 指向函数的指针、: 返回指针值的函数、: 用指针变量作为函数参数。</b>",
          "score": 3,
        },
        {
          "createTime": "2025-05-26 19:52:11",
          "id": 21011,
          "orderIn": 1,
          "issue": "user",
          "content": "函数的调用形式有哪些",
          "score": 0,
        },
        {
          "createTime": "2025-05-26 19:52:31",
          "id": 21013,
          "dialogueId": 2274117734334976,
          "orderIn": 2,
          "issue": "assistant",
          "content": "知识点：函数调用形式\n\n**函数的调用形式主要有三种：传值调用、引用调用和传地址调用**。\n\n1. 传值调用：指的是在调用函数时将实际参数复制一份传递到函数中，这样如果在函数中对值进行修改，将不会影响到实际参数。\n2. 引用调用：是C++特有的，通过在函数定义中使用引用参数实现。在调用时将引用绑定到实际参数，对引用的修改将影响到实际对象。这种方式的数据传递要求参数类型匹配。\n3. 传地址调用：是通过将参数的地址传递到函数，然后在函数中使用指针来间接访问参数。因为可以直接对内存地址进行操作，所以任何修改都会直接影响原始数据。\n\n<b>请同学复习以下知识点：: 函数调用的形式、: 函数的嵌套调用、: 函数的递归调用。</b>",
          "score": 3,
        }
      ],
      homeWorkList: [
        {
          title: "Python基础语法",
          startTime: "2023-09-08 08:00:00",
          endTime: "2023-09-14 23:59:59",
        },
        {
          title: "Python基础",
          startTime: "2023-09-08 09:00:00",
          endTime: "2023-09-14 23:59:59",
        },
      ],
      testPaperName: "Python基础语法",
      testpaperScore: 100,
      score: 75,
      testPaperQuestions: [
        {
          "id": "*********8875905026",
          "knowledgepointId": 3144,
          "knowledgepointName": "计算机语言",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 0,
          "correctAnswer": "C",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": null,
          "userAnswerIsCorrectFlag": null,
          "questionScore": 25,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "第二代计算机语言是（）",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8955596801",
              "questionId": "*********8875905026",
              "optionMark": "A",
              "optionText": "高级语言"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8963985409",
              "questionId": "*********8875905026",
              "optionMark": "B",
              "optionText": "机器语言"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8963985410",
              "questionId": "*********8875905026",
              "optionMark": "C",
              "optionText": "汇编语言"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8963985411",
              "questionId": "*********8875905026",
              "optionMark": "D",
              "optionText": "C语言"
            }
          ]
        },
        {
          "id": "*********8980762626",
          "knowledgepointId": 3144,
          "knowledgepointName": "计算机语言",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 1,
          "correctAnswer": "B",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": null,
          "userAnswerIsCorrectFlag": null,
          "questionScore": 25,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "ANSI C是哪一年制定的（）",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8993345537",
              "questionId": "*********8980762626",
              "optionMark": "A",
              "optionText": "1969"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8993345538",
              "questionId": "*********8980762626",
              "optionMark": "B",
              "optionText": "1983"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8993345539",
              "questionId": "*********8980762626",
              "optionMark": "C",
              "optionText": "1999"
            }
          ]
        },
        {
          "id": "*********9010122753",
          "knowledgepointId": 3145,
          "knowledgepointName": "C语言特点",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 2,
          "correctAnswer": "D",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": null,
          "userAnswerIsCorrectFlag": null,
          "questionScore": 25,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "下列各项中，不是C语言的特点是()",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705666",
              "questionId": "*********9010122753",
              "optionMark": "A",
              "optionText": "语言简洁、紧凑，使用方便"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705667",
              "questionId": "*********9010122753",
              "optionMark": "B",
              "optionText": "数据类型丰富，可移植性好"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705668",
              "questionId": "*********9010122753",
              "optionMark": "C",
              "optionText": "能实现汇编语言的大部分功能"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705669",
              "questionId": "*********9010122753",
              "optionMark": "D",
              "optionText": "有较强的网络操作功能"
            }
          ]
        },
        {
          "id": "*********9035288577",
          "knowledgepointId": 3147,
          "knowledgepointName": "c程序编写步骤",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 3,
          "correctAnswer": "A",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": null,
          "userAnswerIsCorrectFlag": null,
          "questionScore": null,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "一个C程序的执行是从（  ）",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871489",
              "questionId": "*********9035288577",
              "optionMark": "A",
              "optionText": "本程序的main函数开始，到main函数结束"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871490",
              "questionId": "*********9035288577",
              "optionMark": "B",
              "optionText": "本程序文件的第一个函数开始，到本程序文件的最后一个函数结束"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871491",
              "questionId": "*********9035288577",
              "optionMark": "C",
              "optionText": "本程序文件的第一个函数开始，到本程序main函数结束"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871492",
              "questionId": "*********9035288577",
              "optionMark": "D",
              "optionText": "本程序的main函数开始，到本程序文件的最后一个函数结束"
            }
          ]
        },

      ],
      unitList1: [{
        id: 102,
        name: "Python基础语法",
        type: "text",
        startTime: "2023-09-08 08:00:00",
        endTime: "2023-09-14 23:59:59",
        userNumber: 5,
        duration: 120,
        answerPublish: "after_review",
        analysisPublish: "after_review",
        scorePublish: "after_review",
        status: "1",
        tools: ["ai_assistant", "ai_lab"],
        resources: [
          { id: 1003, name: "Python语法速查表", type: "doc", url: "https://example.com/resources/cheatsheet.pdf" }
        ],
        questions: [
          { id: 2003, title: "Python变量命名规则", type: "single", difficulty: "1", score: 5 },
          { id: 2004, title: "Python数据类型有哪些？", type: "multiple", difficulty: "2", score: 10 },
          { id: 2005, title: "编写一个计算圆面积的函数", type: "program", difficulty: "3", score: 20 }
        ],
        hasPython: true,
        maxSubmitTimes: 5,
        maxRunTimes: 20
      },
      {
        id: 103,
        name: "Python流程控制",
        type: "practice",
        startTime: "2023-09-15 08:00:00",
        endTime: "2023-09-21 23:59:59",
        userNumber: 5,
        duration: 180,
        answerPublish: "after_end",
        analysisPublish: "after_end",
        scorePublish: "after_end",
        status: "0",
        tools: ["ai_assistant", "virtual_machine"],
        resources: [
          { id: 1004, name: "流程控制练习题", type: "doc", url: "https://example.com/resources/control.pdf" },
          { id: 1005, name: "示例代码", type: "code", url: "https://example.com/resources/examples.zip" }
        ],
        questions: [
          { id: 2006, title: "if-else语句的使用场景", type: "short", difficulty: "2", score: 10 },
          { id: 2007, title: "编写一个猜数字游戏", type: "program", difficulty: "4", score: 30 }
        ],
        hasPython: true,
        maxSubmitTimes: 3,
        maxRunTimes: 15
      }
      ],
      unitList2: [
        {
          id: 201,
          name: "机器学习概述",
          type: "video",
          startTime: "2023-10-01 08:00:00",
          endTime: "2023-10-07 23:59:59",
          userNumber: 5,
          duration: 90,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "ai_lab"],
          resources: [
            { id: 2001, name: "机器学习发展史", type: "doc", url: "https://example.com/ml/history.pdf" },
            { id: 2002, name: "基础概念讲解视频", type: "video", url: "https://example.com/ml/concepts.mp4" }
          ],
          questions: [
            { id: 3001, title: "什么是监督学习？", type: "single", difficulty: "1", score: 5 },
            { id: 3002, title: "机器学习的三大类型", type: "multiple", difficulty: "2", score: 10 }
          ],
          hasPython: false
        },
        {
          id: 202,
          name: "线性回归实践",
          type: "practice",
          startTime: "2023-10-08 08:00:00",
          endTime: "2023-10-14 23:59:59",
          userNumber: 5,
          duration: 180,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "virtual_machine", "toolkit"],
          resources: [
            { id: 2003, name: "Scikit-learn使用指南", type: "doc", url: "https://example.com/ml/sklearn.pdf" },
            { id: 2004, name: "房价数据集", type: "dataset", url: "https://example.com/ml/housing.csv" }
          ],
          questions: [
            { id: 3003, title: "线性回归的损失函数", type: "short", difficulty: "3", score: 15 },
            { id: 3004, title: "实现波士顿房价预测", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: true,
          maxSubmitTimes: 5,
          maxRunTimes: 25
        },
        {
          id: 203,
          name: "分类算法",
          type: "text",
          startTime: "2023-10-15 08:00:00",
          endTime: "2023-10-21 23:59:59",
          userNumber: 5,
          duration: 150,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["ai_assistant", "ai_lab"],
          resources: [
            { id: 2005, name: "逻辑回归原理", type: "doc", url: "https://example.com/ml/logistic.pdf" },
            { id: 2006, name: "鸢尾花数据集", type: "dataset", url: "https://example.com/ml/iris.csv" }
          ],
          questions: [
            { id: 3005, title: "SVM的核心思想", type: "short", difficulty: "3", score: 15 },
            { id: 3006, title: "实现手写数字识别", type: "program", difficulty: "5", score: 40 }
          ],
          hasPython: true,
          maxSubmitTimes: 4,
          maxRunTimes: 20
        }
      ],

      unitList3: [
        {
          id: 301,
          name: "HTML5基础",
          type: "video",
          startTime: "2023-11-01 08:00:00",
          endTime: "2023-11-07 23:59:59",
          userNumber: 5,
          duration: 80,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["material_lib", "virtual_machine"],
          resources: [
            { id: 3001, name: "HTML5标签手册", type: "doc", url: "https://example.com/web/html5.pdf" },
            { id: 3002, name: "基础布局教学", type: "video", url: "https://example.com/web/layout.mp4" }
          ],
          questions: [
            { id: 4001, title: "HTML5新特性", type: "multiple", difficulty: "2", score: 10 },
            { id: 4002, title: "构建个人简介页面", type: "program", difficulty: "3", score: 20 }
          ],
          hasPython: false
        },
        {
          id: 302,
          name: "CSS3样式设计",
          type: "practice",
          startTime: "2023-11-08 08:00:00",
          endTime: "2023-11-14 23:59:59",
          userNumber: 5,
          duration: 120,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["material_lib", "pose_editor"],
          resources: [
            { id: 3003, name: "CSS3动画指南", type: "doc", url: "https://example.com/web/css3.pdf" },
            { id: 3004, name: "Flex布局示例", type: "code", url: "https://example.com/web/flex.zip" }
          ],
          questions: [
            { id: 4003, title: "BFC是什么？", type: "short", difficulty: "3", score: 15 },
            { id: 4004, title: "实现响应式导航栏", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: false
        },
        {
          id: 303,
          name: "JavaScript交互",
          type: "text",
          startTime: "2023-11-15 08:00:00",
          endTime: "2023-11-21 23:59:59",
          userNumber: 5,
          duration: 180,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["virtual_machine", "toolkit"],
          resources: [
            { id: 3005, name: "ES6特性速查", type: "doc", url: "https://example.com/web/es6.pdf" },
            { id: 3006, name: "DOM操作示例", type: "code", url: "https://example.com/web/dom.zip" }
          ],
          questions: [
            { id: 4005, title: "闭包的概念", type: "short", difficulty: "4", score: 20 },
            { id: 4006, title: "实现购物车功能", type: "program", difficulty: "5", score: 40 }
          ],
          hasPython: false
        }
      ],

      unitList4: [
        {
          id: 401,
          name: "Pandas基础",
          type: "video",
          startTime: "2023-12-01 08:00:00",
          endTime: "2023-12-07 23:59:59",
          userNumber: 5,
          duration: 90,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "virtual_machine"],
          resources: [
            { id: 4001, name: "Pandas速查表", type: "doc", url: "https://example.com/da/pandas.pdf" },
            { id: 4002, name: "数据清洗演示", type: "video", url: "https://example.com/da/cleaning.mp4" }
          ],
          questions: [
            { id: 5001, title: "Series和DataFrame区别", type: "short", difficulty: "2", score: 10 },
            { id: 5002, title: "数据去重操作", type: "program", difficulty: "3", score: 20 }
          ],
          hasPython: true,
          maxSubmitTimes: 5,
          maxRunTimes: 20
        },
        {
          id: 402,
          name: "数据可视化",
          type: "practice",
          startTime: "2023-12-08 08:00:00",
          endTime: "2023-12-14 23:59:59",
          userNumber: 5,
          duration: 120,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "ai_image"],
          resources: [
            { id: 4003, name: "Matplotlib指南", type: "doc", url: "https://example.com/da/matplotlib.pdf" },
            { id: 4004, name: "销售数据集", type: "dataset", url: "https://example.com/da/sales.csv" }
          ],
          questions: [
            { id: 5003, title: "折线图和柱状图适用场景", type: "short", difficulty: "3", score: 15 },
            { id: 5004, title: "绘制销售趋势图", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: true,
          maxSubmitTimes: 4,
          maxRunTimes: 15
        },
        {
          id: 403,
          name: "实战项目",
          type: "project",
          startTime: "2023-12-15 08:00:00",
          endTime: "2023-12-21 23:59:59",
          userNumber: 5,
          duration: 240,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["ai_assistant", "toolkit", "material_lib"],
          resources: [
            { id: 4005, name: "电商用户行为数据", type: "dataset", url: "https://example.com/da/ecommerce.csv" },
            { id: 4006, name: "分析报告模板", type: "doc", url: "https://example.com/da/template.pdf" }
          ],
          questions: [
            { id: 5005, title: "用户购买行为分析", type: "program", difficulty: "5", score: 50 }
          ],
          hasPython: true,
          maxSubmitTimes: 3,
          maxRunTimes: 10
        }
      ],
      unitList: [],
      toolOptions: [
        { value: "ai_assistant", label: "AI助手" },
        { value: "ai_image", label: "AI生图" },
        { value: "ai_lab", label: "AI对话实验室" },
        { value: "virtual_machine", label: "虚拟机" },
        { value: "toolkit", label: "工具包" },
        { value: "material_lib", label: "素材库" },
        { value: "pose_editor", label: "姿态编辑器" }
      ],
    }
  },
  created() {
    this.courseInfo = this.$router.history.current.params.data
    this.getUnitList()
  },
  activated() {
    this.courseInfo = this.$router.history.current.params.data
  },
  methods: {
    getUnitList() {
      this.unitList = this.courseInfo.courseCode == 1000001 ? this.unitList1 : this.courseInfo.courseCode == 1000002 ? this.unitList2 : this.courseInfo.courseCode == 1000003 ? this.unitList3 : this.unitList4
      this.unitName = this.unitList[0].name
      this.resourcesList = this.unitList[0].resources
      this.questionsList = this.unitList[0].questions
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    handleClicks(tab, event) {
      console.log(tab, event);
    },
    changeUnit(val) {
      const obj = this.unitList.find(i => i.name == val)
      this.unitName = obj.name
      this.resourcesList = obj.resources
      this.questionsList = obj.questions
    },
    handleDoHomeWork(row) {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },

    handleChangeInput(item, index) {
      item.inputShow = !item.inputShow;
      this.$set(this.testPaperQuestions, index, item);
    },
    handleChanegScore(item, index) {
      var reg = new RegExp("^[0-9]*$");
      if (!reg.test(item.questionScore)) {
        this.$message.error("请输入数字值");
        item.questionScore = undefined;
        this.$set(this.testPaperQuestions, index, item);
      } else if (Number(item.questionScore) > Number(item.questionConfigScore)) {
        this.$message.error("请输入小于本题分数的数字值");
        item.questionScore = undefined;
        this.$set(this.testPaperQuestions, index, item);
      } else {
        item.inputShow = !item.inputShow;
        this.$set(this.testPaperQuestions, index, item);
        let score = 0
        this.testPaperQuestions.map(item => {
          score += Number(item.questionScore)
        })
        this.score = score
        this.$message.success("修改成功");

      }

    },


  }
}
</script>
<style lang="scss" scoped >
.imgBox {
  width: 100%;
  height: 100%;
  position: relative;
  .devImg {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
    max-height: 100%;
    border: 1px solid #ccc;
  }
}
.content-cell {
  max-height: 60px;
  overflow: hidden;
  line-height: 1.5;
}
</style>
