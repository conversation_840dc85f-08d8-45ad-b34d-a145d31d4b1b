<template>
  <div class="app-container">
    <el-form size="small" :inline="true">
      <el-form-item label="课程单元" prop="unitName">
        <el-select v-model="unitName" placeholder="请选择课程单元" @change="changeUnit">
          <el-option v-for="item in unitList" :key="item.id" :label="item.name" :value="item.name">
          </el-option>
        </el-select> </el-form-item>
    </el-form>

    <div class="assistant-button-top">
      <Assistant />
    </div>

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="统计" name="first" style="margin:0 auto;">
        <el-descriptions title="资源数据" :column="3" :labelStyle='labelStyle' :contentStyle='contentStyle'
          style="margin-top: 10px;">
          <el-descriptions-item label="技能点"> 20</el-descriptions-item>
          <el-descriptions-item label="知识点"> 38</el-descriptions-item>
          <el-descriptions-item label="案例"> 6</el-descriptions-item>
          <el-descriptions-item label="题目"> 15</el-descriptions-item>
          <el-descriptions-item label="视频"> 3</el-descriptions-item>
          <el-descriptions-item label="ppt"> 2</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="学习数据" :column="3" :labelStyle='labelStyle' :contentStyle='contentStyle'
          style="margin-top: 10px;">
          <el-descriptions-item label="出勤率"> 98%</el-descriptions-item>
          <el-descriptions-item label="完成率"> 96%</el-descriptions-item>
          <el-descriptions-item label="通过率"> 89%</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="智能问答" :column="3" :labelStyle='labelStyle' :contentStyle='contentStyle'
          style="margin-top: 10px;">
          <el-descriptions-item label="AI对话次数"> 23</el-descriptions-item>
          <el-descriptions-item label="有效AI对话次数"> 18</el-descriptions-item>
        </el-descriptions>
        <div style="width: 100%;margin:10px auto;border: 1px solid #ccc;padding:10px 20% ;text-align: center;">
          <div ref="scoreChart" style="width: 100%; height: 300px;"></div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="学生" name="second">
        <el-table :data="stuentList" style="width: 100%">
          <el-table-column prop="userId" label="学号" width="120" />
          <el-table-column prop="realName" label="姓名" width="180"> <template slot-scope="scope">
              <span class="blue-font-color" @click="handleReview(scope.row)">
                {{ scope.row.realName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="major" label="专业" />
          <el-table-column prop="class" label="班级" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="joinTime" label="加入时间" width="180" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="教学资料" name="third">
        <el-table :data="resourcesList" style="width: 100%">
          <el-table-column prop="name" label="资料" />
          <el-table-column prop="email" label="完成率">
            <template>
              <span>93%</span>
            </template>
          </el-table-column>
          <el-table-column prop="joinTime" label="通过率" width="180">
            <template>
              <span>89%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="试题" name="fourth">
        <el-table :data="questionsList" style="width: 100%">
          <el-table-column prop="title" label="试题" />
          <el-table-column prop="email" label="完成率">
            <template>
              <span>93%</span>
            </template>
          </el-table-column>
          <el-table-column prop="joinTime" label="通过率" width="180">
            <template>
              <span>89%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="学生信息" :visible.sync="dialogVisible" width="80%" :before-close="handleClose">
      <el-tabs v-model="activeNames" @tab-click="handleClicks">
        <el-tab-pane label="基本信息" name="first">
          <el-descriptions title="基本信息" :column="3" :labelStyle='labelStyle' :contentStyle='contentStyle'
            style="margin-top: 10px;">
            <el-descriptions-item label="姓名">{{ realName }} </el-descriptions-item>
            <el-descriptions-item label="课程"> {{this.courseInfo.courseName}}</el-descriptions-item>
            <el-descriptions-item label="单元"> {{unitName}}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions title="学情分析" :column="3" :labelStyle='labelStyle' :contentStyle='contentStyle'
            style="margin-top: 10px;">
            <el-descriptions-item label="出勤率"> 98%</el-descriptions-item>
            <el-descriptions-item label="完成率"> 93%</el-descriptions-item>
            <el-descriptions-item label="通过率"> 80%</el-descriptions-item>
            <el-descriptions-item label="AI对话次数"> 6</el-descriptions-item>
            <el-descriptions-item label="总分"> 90</el-descriptions-item>
            <el-descriptions-item label="评语"> 该生认真</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="AI问答记录" name="second">
          <el-table :data="aiList" style="width: 100%">
            <el-table-column label="发送人" prop="issue" min-width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.issue === 'user' ? 'primary' : 'success'" size="small">
                  {{ scope.row.issue === 'user' ? '用户' : '模型' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="内容" prop="content" min-width="300" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="content-cell">
                  {{ scope.row.content }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="得分" prop="score" min-width="100" align="center" />
            <el-table-column label="创建时间" prop="createTime" min-width="160" align="center" sortable>
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="作业" name="third">
          <el-table :data="homeWorkList" style="width: 100%">
            <el-table-column prop="title" label="试题" />
            <el-table-column prop="startTime" label="开始时间" />
            <el-table-column prop="endTime" label="完成时间" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleAudit(scope.row)">批改</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="作业批改" :visible.sync="auditDialogVisible" width="80%" :before-close="handleCloseAuditDialog">
      <div class="app-container ck-container">
        <div class="questions_box" v-if="testPaperQuestions.length !== 0">
          <h1 style="text-align: center">{{ testPaperName }}</h1>
          <div class="testPaper-title">
            <span class="testPaper-score">总分：{{ testpaperScore }}</span>
            <span class="testPaper-score">得分：{{ score ? score : "--" }} </span>
          </div>
          <el-form :model="form" ref="form">
            <div v-for="(item, index) in testPaperQuestions" :key="item.id">
              <div>
                <p style="font-weight: 500">
                  第{{ index + 1 }}题：{{ item.question }}
                  <span v-if="item.questionType == 'single'">（单选）</span>
                  <span v-if="item.questionType == 'multiple'">（多选）</span>
                  <span v-if="item.questionType == 'blank'">（填空）</span>
                  <span v-if="item.questionType == 'shortAnswer'">（简答）</span>
                </p>
              </div>
              <div v-if="item.questionType == 'single'">
                <el-form-item label="">
                  <el-radio-group v-model="item.userAnswer">
                    <el-radio disabled v-for="items in item.questionbankQuestionOptions" :key="items.id"
                      :label="items.optionMark">{{ items.optionMark + "." + items.optionText }}</el-radio>
                  </el-radio-group>
                  <div class="testPaper-questionScore">
                    <div class="testPaper-score">
                      本题得分：
                      <span v-if="!item.inputShow">{{
                    item.questionScore !== null ? item.questionScore : "--"
                  }}</span>
                    </div>
                  </div>
                </el-form-item>
              </div>
              <div v-if="item.questionType == 'multiple'">
                <el-form-item label="">
                  <el-checkbox-group v-model="item.userAnswer">
                    <el-checkbox disabled v-for="items in item.questionbankQuestionOptions" :key="items.id"
                      :label="items.optionMark">{{ items.optionMark + "." + items.optionText }}</el-checkbox>
                  </el-checkbox-group>
                  <div class="testPaper-questionScore">
                    <div class="testPaper-score">
                      本题得分：
                      <span v-if="!item.inputShow">{{
                    item.questionScore !== null ? item.questionScore : "--"
                  }}</span>
                    </div>
                  </div>
                </el-form-item>
              </div>
              <div v-if="item.questionType == 'blank'">
                <el-form-item label="">
                  <el-input v-model="item.userAnswer" disabled></el-input>
                  <div class="testPaper-questionScore">
                    <div class="testPaper-score">
                      本题得分：
                      <span v-if="!item.inputShow">{{
                    item.questionScore !== null ? item.questionScore : "--"
                  }}</span>
                      <el-input class="score-input" v-model="item.questionScore" v-if="item.inputShow"
                        @blur="handleChanegScore(item,index)"></el-input>
                      <i v-if="!item.inputShow &&!flag" class="icon-class el-icon-edit"
                        @click="handleChangeInput(item, index)"></i>
                    </div>
                    >
                  </div>
                </el-form-item>
              </div>
              <div v-if="item.questionType == 'shortAnswer'">
                <el-form-item label="">
                  <el-input type="textarea" disabled></el-input>
                  <div class="testPaper-questionScore">
                    <div class="testPaper-score">
                      本题得分：
                      <span v-if="!item.inputShow">{{
                    item.questionScore !== null ? item.questionScore : "--"
                  }}</span>
                      <el-input class="score-input" v-model="item.questionScore" v-if="item.inputShow"
                        @blur="handleChanegScore(item,index)"></el-input>
                      <i v-if="!item.inputShow &&!flag" class="icon-class el-icon-edit"
                        @click="handleChangeInput(item, index)"></i>
                    </div>
                    >
                  </div>
                </el-form-item>
              </div>
            </div>
            <div></div>
          </el-form>
        </div>
        <div v-else>
          <div class="none" style="margin-left: 0px">
            <div class="none_img"></div>
            <h3>暂无试题</h3>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Assistant from './components/Assistant.vue'
import * as echarts from 'echarts';
export default {
  name: 'TeachingPage',
  components: { Assistant },
  data() {
    return {
      unitName: '',
      activeName: 'first',
      activeNames: 'first',
      dialogVisible: false,
      auditDialogVisible: false,
      stuentList: [
        {
          userId: 2022001,
          username: 'student1',
          realName: '张丽雅',
          major: '计算机科学与技术',
          class: '2022级1班',
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          email: '<EMAIL>',
          joinTime: '2023-05-10 09:30:00',
          isHidden: false
        },
        {
          userId: 20220002,
          username: 'student2',
          realName: '李伟',
          major: '计算机科学与技术',
          class: '2022级1班',
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          email: '<EMAIL>',
          joinTime: '2023-05-11 14:15:00',
          isHidden: true
        },
        {
          userId: 20220003,
          username: 'student3',
          realName: '王娜',
          major: '软件工程',
          class: '2022级1班',
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          email: '<EMAIL>',
          joinTime: '2023-05-12 10:45:00',
          isHidden: false
        },
        {
          userId: 20220004,
          username: 'student4',
          realName: '赵强',
          major: '计算机科学与技术',
          class: '2022级2班',
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          email: '<EMAIL>',
          joinTime: '2023-05-13 16:20:00',
          isHidden: false
        },
        {
          userId: 20220005,
          username: 'student5',
          realName: '钱敏',
          major: '计算机科学与技术',
          class: '2022级2班',
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          email: '<EMAIL>',
          joinTime: '2023-05-14 11:10:00',
          isHidden: false
        }
      ],
      contentStyle: {
        fontSize: '14px'
      },
      labelStyle: {
        fontWeight: 'bold',
        fontSize: '14px'
      },
      courseInfo: {},
      resourcesList: [],
      questionsList: [],
      aiList: [
        {
          "createTime": "2025-05-26 19:43:58",
          "id": 20980,
          "orderIn": 1,
          "issue": "user",
          "content": "指向函数为什么不可以储存任意函数的地址",
          "score": 0,
        },
        {
          "createTime": "2025-05-26 19:44:21",
          "id": 20982,
          "orderIn": 2,
          "issue": "assistant",
          "content": "知识点：函数指针\n\n在C语言中，指向函数的指针（函数指针）确实可以存储函数的地址，但并不是可以存储**任意**函数的地址。这是因为函数指针的类型必须与它所指向的函数的类型相匹配。\n\n每个函数在内存中都有一个唯一的入口地址，函数指针就是用来指向这个地址的。但是，不同的函数可能有不同的参数列表和返回值类型。函数指针的类型定义了它所指向的函数的参数类型和返回值类型。因此，一个特定类型的函数指针只能指向与其类型相匹配的函数。\n\n例如，如果你有一个函数指针，它的类型是`int (*)(int, int)`，这意味着它可以指向任何接受两个int参数并返回一个int值的函数。如果你试图让这个指针指向一个不接受两个int参数，或者不返回一个int值的函数，那么编译器就会报错。\n\n总的来说，虽然函数指针可以存储函数的地址，但它的类型限制了它可以指向哪些函数。所以，它并不能存储任意函数的地址。\n\n<b>请同学复习以下知识点：: 指向函数的指针、: 返回指针值的函数、: 用指针变量作为函数参数。</b>",
          "score": 3,
        },
        {
          "createTime": "2025-05-26 19:52:11",
          "id": 21011,
          "orderIn": 1,
          "issue": "user",
          "content": "函数的调用形式有哪些",
          "score": 0,
        },
        {
          "createTime": "2025-05-26 19:52:31",
          "id": 21013,
          "dialogueId": 2274117734334976,
          "orderIn": 2,
          "issue": "assistant",
          "content": "知识点：函数调用形式\n\n**函数的调用形式主要有三种：传值调用、引用调用和传地址调用**。\n\n1. 传值调用：指的是在调用函数时将实际参数复制一份传递到函数中，这样如果在函数中对值进行修改，将不会影响到实际参数。\n2. 引用调用：是C++特有的，通过在函数定义中使用引用参数实现。在调用时将引用绑定到实际参数，对引用的修改将影响到实际对象。这种方式的数据传递要求参数类型匹配。\n3. 传地址调用：是通过将参数的地址传递到函数，然后在函数中使用指针来间接访问参数。因为可以直接对内存地址进行操作，所以任何修改都会直接影响原始数据。\n\n<b>请同学复习以下知识点：: 函数调用的形式、: 函数的嵌套调用、: 函数的递归调用。</b>",
          "score": 3,
        }
      ],
      homeWorkList: [
        {
          title: "Python基础语法",
          startTime: "2023-09-08 08:00:00",
          endTime: "2023-09-14 23:59:59",
        },
        {
          title: "Python基础",
          startTime: "2023-09-08 09:00:00",
          endTime: "2023-09-14 23:59:59",
        },
      ],
      testPaperName: "Python基础语法",
      testpaperScore: 100,
      score: 75,
      testPaperQuestions: [
        {
          "id": "*********8875905026",
          "knowledgepointId": 3144,
          "knowledgepointName": "计算机语言",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 0,
          "correctAnswer": "C",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": "C",
          "userAnswerIsCorrectFlag": null,
          "questionScore": 25,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "第二代计算机语言是（）",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8955596801",
              "questionId": "*********8875905026",
              "optionMark": "A",
              "optionText": "高级语言"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8963985409",
              "questionId": "*********8875905026",
              "optionMark": "B",
              "optionText": "机器语言"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8963985410",
              "questionId": "*********8875905026",
              "optionMark": "C",
              "optionText": "汇编语言"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8963985411",
              "questionId": "*********8875905026",
              "optionMark": "D",
              "optionText": "C语言"
            }
          ]
        },
        {
          "id": "*********8980762626",
          "knowledgepointId": 3144,
          "knowledgepointName": "计算机语言",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 1,
          "correctAnswer": "B",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": "B",
          "userAnswerIsCorrectFlag": null,
          "questionScore": 25,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "ANSI C是哪一年制定的（）",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8993345537",
              "questionId": "*********8980762626",
              "optionMark": "A",
              "optionText": "1969"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8993345538",
              "questionId": "*********8980762626",
              "optionMark": "B",
              "optionText": "1983"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********8993345539",
              "questionId": "*********8980762626",
              "optionMark": "C",
              "optionText": "1999"
            }
          ]
        },
        {
          "id": "*********9010122753",
          "knowledgepointId": 3145,
          "knowledgepointName": "C语言特点",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 2,
          "correctAnswer": "D",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": "D",
          "userAnswerIsCorrectFlag": null,
          "questionScore": 25,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "下列各项中，不是C语言的特点是()",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705666",
              "questionId": "*********9010122753",
              "optionMark": "A",
              "optionText": "语言简洁、紧凑，使用方便"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705667",
              "questionId": "*********9010122753",
              "optionMark": "B",
              "optionText": "数据类型丰富，可移植性好"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705668",
              "questionId": "*********9010122753",
              "optionMark": "C",
              "optionText": "能实现汇编语言的大部分功能"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9022705669",
              "questionId": "*********9010122753",
              "optionMark": "D",
              "optionText": "有较强的网络操作功能"
            }
          ]
        },
        {
          "id": "*********9035288577",
          "knowledgepointId": 3147,
          "knowledgepointName": "c程序编写步骤",
          "questionType": "single",
          "questionTypeName": null,
          "questionOrder": 3,
          "correctAnswer": "A",
          "textbookId": ****************,
          "textbookName": null,
          "chapter": "初识C语言",
          "importType": "P",
          "chapterList": null,
          "chapterDomainList": null,
          "questionTypeMap": null,
          "scoreitem": null,
          "userId": null,
          "isMaster": null,
          "questionbankQuestionScoreitems": [],
          "questionNumber": 0,
          "userAnswer": "D",
          "userAnswerIsCorrectFlag": null,
          "questionScore": null,
          "questionConfigScore": null,
          "testpaperStuDetailId": null,
          "question": "一个C程序的执行是从（  ）",
          "questionbankQuestionOptions": [
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871489",
              "questionId": "*********9035288577",
              "optionMark": "A",
              "optionText": "本程序的main函数开始，到main函数结束"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871490",
              "questionId": "*********9035288577",
              "optionMark": "B",
              "optionText": "本程序文件的第一个函数开始，到本程序文件的最后一个函数结束"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871491",
              "questionId": "*********9035288577",
              "optionMark": "C",
              "optionText": "本程序文件的第一个函数开始，到本程序main函数结束"
            },
            {
              "createBy": null,
              "createTime": null,
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "id": "*********9047871492",
              "questionId": "*********9035288577",
              "optionMark": "D",
              "optionText": "本程序的main函数开始，到本程序文件的最后一个函数结束"
            }
          ]
        },

      ],
      unitList1: [{
        id: 102,
        name: "Python基础语法",
        type: "text",
        startTime: "2023-09-08 08:00:00",
        endTime: "2023-09-14 23:59:59",
        userNumber: 5,
        duration: 120,
        answerPublish: "after_review",
        analysisPublish: "after_review",
        scorePublish: "after_review",
        status: "1",
        tools: ["ai_assistant", "ai_lab"],
        resources: [
          { id: 1003, name: "Python语法速查表", type: "doc", url: "https://example.com/resources/cheatsheet.pdf" }
        ],
        questions: [
          { id: 2003, title: "Python变量命名规则", type: "single", difficulty: "1", score: 5 },
          { id: 2004, title: "Python数据类型有哪些？", type: "multiple", difficulty: "2", score: 10 },
          { id: 2005, title: "编写一个计算圆面积的函数", type: "program", difficulty: "3", score: 20 }
        ],
        hasPython: true,
        maxSubmitTimes: 5,
        maxRunTimes: 20
      },
      {
        id: 103,
        name: "Python流程控制",
        type: "practice",
        startTime: "2023-09-15 08:00:00",
        endTime: "2023-09-21 23:59:59",
        userNumber: 5,
        duration: 180,
        answerPublish: "after_end",
        analysisPublish: "after_end",
        scorePublish: "after_end",
        status: "0",
        tools: ["ai_assistant", "virtual_machine"],
        resources: [
          { id: 1004, name: "流程控制练习题", type: "doc", url: "https://example.com/resources/control.pdf" },
          { id: 1005, name: "示例代码", type: "code", url: "https://example.com/resources/examples.zip" }
        ],
        questions: [
          { id: 2006, title: "if-else语句的使用场景", type: "short", difficulty: "2", score: 10 },
          { id: 2007, title: "编写一个猜数字游戏", type: "program", difficulty: "4", score: 30 }
        ],
        hasPython: true,
        maxSubmitTimes: 3,
        maxRunTimes: 15
      }
      ],
      unitList2: [
        {
          id: 201,
          name: "机器学习概述",
          type: "video",
          startTime: "2023-10-01 08:00:00",
          endTime: "2023-10-07 23:59:59",
          userNumber: 5,
          duration: 90,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "ai_lab"],
          resources: [
            { id: 2001, name: "机器学习发展史", type: "doc", url: "https://example.com/ml/history.pdf" },
            { id: 2002, name: "基础概念讲解视频", type: "video", url: "https://example.com/ml/concepts.mp4" }
          ],
          questions: [
            { id: 3001, title: "什么是监督学习？", type: "single", difficulty: "1", score: 5 },
            { id: 3002, title: "机器学习的三大类型", type: "multiple", difficulty: "2", score: 10 }
          ],
          hasPython: false
        },
        {
          id: 202,
          name: "线性回归实践",
          type: "practice",
          startTime: "2023-10-08 08:00:00",
          endTime: "2023-10-14 23:59:59",
          userNumber: 5,
          duration: 180,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "virtual_machine", "toolkit"],
          resources: [
            { id: 2003, name: "Scikit-learn使用指南", type: "doc", url: "https://example.com/ml/sklearn.pdf" },
            { id: 2004, name: "房价数据集", type: "dataset", url: "https://example.com/ml/housing.csv" }
          ],
          questions: [
            { id: 3003, title: "线性回归的损失函数", type: "short", difficulty: "3", score: 15 },
            { id: 3004, title: "实现波士顿房价预测", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: true,
          maxSubmitTimes: 5,
          maxRunTimes: 25
        },
        {
          id: 203,
          name: "分类算法",
          type: "text",
          startTime: "2023-10-15 08:00:00",
          endTime: "2023-10-21 23:59:59",
          userNumber: 5,
          duration: 150,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["ai_assistant", "ai_lab"],
          resources: [
            { id: 2005, name: "逻辑回归原理", type: "doc", url: "https://example.com/ml/logistic.pdf" },
            { id: 2006, name: "鸢尾花数据集", type: "dataset", url: "https://example.com/ml/iris.csv" }
          ],
          questions: [
            { id: 3005, title: "SVM的核心思想", type: "short", difficulty: "3", score: 15 },
            { id: 3006, title: "实现手写数字识别", type: "program", difficulty: "5", score: 40 }
          ],
          hasPython: true,
          maxSubmitTimes: 4,
          maxRunTimes: 20
        }
      ],

      unitList3: [
        {
          id: 301,
          name: "HTML5基础",
          type: "video",
          startTime: "2023-11-01 08:00:00",
          endTime: "2023-11-07 23:59:59",
          userNumber: 5,
          duration: 80,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["material_lib", "virtual_machine"],
          resources: [
            { id: 3001, name: "HTML5标签手册", type: "doc", url: "https://example.com/web/html5.pdf" },
            { id: 3002, name: "基础布局教学", type: "video", url: "https://example.com/web/layout.mp4" }
          ],
          questions: [
            { id: 4001, title: "HTML5新特性", type: "multiple", difficulty: "2", score: 10 },
            { id: 4002, title: "构建个人简介页面", type: "program", difficulty: "3", score: 20 }
          ],
          hasPython: false
        },
        {
          id: 302,
          name: "CSS3样式设计",
          type: "practice",
          startTime: "2023-11-08 08:00:00",
          endTime: "2023-11-14 23:59:59",
          userNumber: 5,
          duration: 120,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["material_lib", "pose_editor"],
          resources: [
            { id: 3003, name: "CSS3动画指南", type: "doc", url: "https://example.com/web/css3.pdf" },
            { id: 3004, name: "Flex布局示例", type: "code", url: "https://example.com/web/flex.zip" }
          ],
          questions: [
            { id: 4003, title: "BFC是什么？", type: "short", difficulty: "3", score: 15 },
            { id: 4004, title: "实现响应式导航栏", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: false
        },
        {
          id: 303,
          name: "JavaScript交互",
          type: "text",
          startTime: "2023-11-15 08:00:00",
          endTime: "2023-11-21 23:59:59",
          userNumber: 5,
          duration: 180,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["virtual_machine", "toolkit"],
          resources: [
            { id: 3005, name: "ES6特性速查", type: "doc", url: "https://example.com/web/es6.pdf" },
            { id: 3006, name: "DOM操作示例", type: "code", url: "https://example.com/web/dom.zip" }
          ],
          questions: [
            { id: 4005, title: "闭包的概念", type: "short", difficulty: "4", score: 20 },
            { id: 4006, title: "实现购物车功能", type: "program", difficulty: "5", score: 40 }
          ],
          hasPython: false
        }
      ],

      unitList4: [
        {
          id: 401,
          name: "Pandas基础",
          type: "video",
          startTime: "2023-12-01 08:00:00",
          endTime: "2023-12-07 23:59:59",
          userNumber: 5,
          duration: 90,
          answerPublish: "after_submit",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "virtual_machine"],
          resources: [
            { id: 4001, name: "Pandas速查表", type: "doc", url: "https://example.com/da/pandas.pdf" },
            { id: 4002, name: "数据清洗演示", type: "video", url: "https://example.com/da/cleaning.mp4" }
          ],
          questions: [
            { id: 5001, title: "Series和DataFrame区别", type: "short", difficulty: "2", score: 10 },
            { id: 5002, title: "数据去重操作", type: "program", difficulty: "3", score: 20 }
          ],
          hasPython: true,
          maxSubmitTimes: 5,
          maxRunTimes: 20
        },
        {
          id: 402,
          name: "数据可视化",
          type: "practice",
          startTime: "2023-12-08 08:00:00",
          endTime: "2023-12-14 23:59:59",
          userNumber: 5,
          duration: 120,
          answerPublish: "after_review",
          analysisPublish: "after_review",
          scorePublish: "after_review",
          status: "1",
          tools: ["ai_assistant", "ai_image"],
          resources: [
            { id: 4003, name: "Matplotlib指南", type: "doc", url: "https://example.com/da/matplotlib.pdf" },
            { id: 4004, name: "销售数据集", type: "dataset", url: "https://example.com/da/sales.csv" }
          ],
          questions: [
            { id: 5003, title: "折线图和柱状图适用场景", type: "short", difficulty: "3", score: 15 },
            { id: 5004, title: "绘制销售趋势图", type: "program", difficulty: "4", score: 30 }
          ],
          hasPython: true,
          maxSubmitTimes: 4,
          maxRunTimes: 15
        },
        {
          id: 403,
          name: "实战项目",
          type: "project",
          startTime: "2023-12-15 08:00:00",
          endTime: "2023-12-21 23:59:59",
          userNumber: 5,
          duration: 240,
          answerPublish: "after_end",
          analysisPublish: "after_end",
          scorePublish: "after_end",
          status: "0",
          tools: ["ai_assistant", "toolkit", "material_lib"],
          resources: [
            { id: 4005, name: "电商用户行为数据", type: "dataset", url: "https://example.com/da/ecommerce.csv" },
            { id: 4006, name: "分析报告模板", type: "doc", url: "https://example.com/da/template.pdf" }
          ],
          questions: [
            { id: 5005, title: "用户购买行为分析", type: "program", difficulty: "5", score: 50 }
          ],
          hasPython: true,
          maxSubmitTimes: 3,
          maxRunTimes: 10
        }
      ],
      unitList: [],
      toolOptions: [
        { value: "ai_assistant", label: "AI助手" },
        { value: "ai_image", label: "AI生图" },
        { value: "ai_lab", label: "AI对话实验室" },
        { value: "virtual_machine", label: "虚拟机" },
        { value: "toolkit", label: "工具包" },
        { value: "material_lib", label: "素材库" },
        { value: "pose_editor", label: "姿态编辑器" }
      ],
      realName: '',
      scores: [78, 85, 92, 65, 70, 88, 95, 60, 72, 81, 89, 93, 55, 68, 75, 82, 90, 97, 100, 62]
    }
  },
  created() {
    this.courseInfo = this.$router.history.current.params.data
    this.getUnitList()
  },
  activated() {
    this.courseInfo = this.$router.history.current.params.data
  },
  mounted() {
    this.drawScoreChart();
  },
  methods: {


    drawScoreChart() {
      // 定义分数区间
      const scoreRanges = [
        { min: 0, max: 59, label: '不及格' },
        { min: 60, max: 69, label: '60-69' },
        { min: 70, max: 79, label: '70-79' },
        { min: 80, max: 89, label: '80-89' },
        { min: 90, max: 100, label: '90-100' }
      ];

      // 计算每个区间的人数
      const countData = scoreRanges.map(range => {
        return this.scores.filter(score =>
          score >= range.min && score <= range.max
        ).length;
      });

      // 获取区间标签
      const rangeLabels = scoreRanges.map(range => range.label);

      // 初始化图表
      const chart = echarts.init(this.$refs.scoreChart);

      const option = {
        title: {
          text: '学生成绩区间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}人'
        },
        xAxis: {
          type: 'category',
          data: rangeLabels,
          axisLabel: {
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '人数',
          minInterval: 1
        },
        series: [{
          name: '人数',
          type: 'bar',
          data: countData,
          itemStyle: {
            color: function (params) {
              // 不同区间不同颜色
              const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#A37EBD', '#FFA07A'];
              return colors[params.dataIndex];
            }
          },
          label: {
            show: true,
            position: 'top'
          }
        }]
      };

      chart.setOption(option);

      // 窗口大小变化时重绘图表
      window.addEventListener('resize', function () {
        chart.resize();
      });
    },

    getUnitList() {
      this.unitList = this.courseInfo.courseCode == 1000001 ? this.unitList1 : this.courseInfo.courseCode == 1000002 ? this.unitList2 : this.courseInfo.courseCode == 1000003 ? this.unitList3 : this.unitList4
      this.unitName = this.unitList[0].name
      this.resourcesList = this.unitList[0].resources
      this.questionsList = this.unitList[0].questions
    },
    changeTools(val) {
      const toolsName = []
      val.map(item => {
        const obj = this.toolOptions.find(i => i.value == item)
        if (obj) {
          toolsName.push(obj.label)
        }
      })
      return toolsName.join('，')
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    handleClicks(tab, event) {
      console.log(tab, event);
    },
    changeUnit(val) {
      const obj = this.unitList.find(i => i.name == val)
      this.unitName = obj.name
      this.resourcesList = obj.resources
      this.questionsList = obj.questions
    },
    handleReview(row) {
      this.realName = row.realName
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAudit() {
      this.auditDialogVisible = true
    },
    handleCloseAuditDialog() {
      this.auditDialogVisible = false
    },

    handleChangeInput(item, index) {
      item.inputShow = !item.inputShow;
      this.$set(this.testPaperQuestions, index, item);
    },
    handleChanegScore(item, index) {
      var reg = new RegExp("^[0-9]*$");
      if (!reg.test(item.questionScore)) {
        this.$message.error("请输入数字值");
        item.questionScore = undefined;
        this.$set(this.testPaperQuestions, index, item);
      } else if (Number(item.questionScore) > Number(item.questionConfigScore)) {
        this.$message.error("请输入小于本题分数的数字值");
        item.questionScore = undefined;
        this.$set(this.testPaperQuestions, index, item);
      } else {
        item.inputShow = !item.inputShow;
        this.$set(this.testPaperQuestions, index, item);
        let score = 0
        this.testPaperQuestions.map(item => {
          score += Number(item.questionScore)
        })
        this.score = score
        this.$message.success("修改成功");

      }

    },


  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.chart.resize);
  }
}
</script>
<style lang="scss" scoped >
.imgBox {
  width: 100%;
  height: 100%;
  position: relative;
  .devImg {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
    max-height: 100%;
    border: 1px solid #ccc;
  }
}
.content-cell {
  max-height: 60px;
  overflow: hidden;
  line-height: 1.5;
}
</style>
