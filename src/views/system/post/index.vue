<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="岗位编码" prop="postCode">
        <el-input
          v-model="queryParams.postCode"
          placeholder="请输入岗位编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="岗位名称" prop="postName">
        <el-input
          v-model="queryParams.postName"
          placeholder="请输入岗位名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="岗位状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:post:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:post:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:post:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:post:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="岗位编号" align="center" prop="postId" />
      <el-table-column label="岗位编码" align="center" prop="postCode" />
      <el-table-column label="岗位名称" align="center" prop="postName" />
      <el-table-column label="岗位排序" align="center" prop="postSort" />

<!--      <el-table-column label="行业大类" align="center" prop="industryCategory" />-->
<!--      <el-table-column label="细分行业" align="center" prop="industrySubcategory" />-->
      <el-table-column label="行业大类" align="center" prop="industryCategory">
        <template slot-scope="scope">
          <div class="ellipsis-cell">{{ getIndustryCategoryName(scope.row.industryCategory) }}</div>
        </template>
      </el-table-column>

      <el-table-column label="细分行业" align="center" prop="industrySubcategory">
        <template slot-scope="scope">
          <div class="ellipsis-cell">{{ getIndustrySubcategoryName(scope.row.industrySubcategory) }}</div>
        </template>
      </el-table-column>

      <el-table-column label="关联技能" align="center" prop="skills" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:post:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:post:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入编码名称" />
        </el-form-item>
        <el-form-item label="岗位顺序" prop="postSort">
          <el-input-number v-model="form.postSort" controls-position="right" :min="0" />
        </el-form-item>

        <el-form-item label="行业大类" prop="industryCategory">
          <el-select
            v-model="form.industryCategory"
            placeholder="请选择行业大类"
            @change="onIndustryCategoryChange"
            @visible-change="handleIndustryCategoryVisible"
          filterable
          >
          <el-option
            v-for="item in industryCategoryList"
            :key="item.big"
            :label="item.name"
            :value="item.big"
          />
          </el-select>
        </el-form-item>

        <el-form-item label="细分行业" prop="industrySubcategory">
          <el-select
            v-model="form.industrySubcategory"
            placeholder="请选择细分行业"
            :disabled="!form.industryCategory"
            filterable
          >
            <el-option
              v-for="item in industrySubcategoryList"
              :key="item.little"
              :label="item.name"
              :value="item.little"
            />
          </el-select>
        </el-form-item>

<!--        <el-form-item label="行业大类" prop="industryCategory">-->
<!--          <el-input v-model="form.industryCategory" placeholder="请输入行业大类" />-->
<!--        </el-form-item>-->

<!--        <el-form-item label="细分行业" prop="industrySubcategory">-->
<!--          <el-input v-model="form.industrySubcategory" placeholder="请输入细分行业" />-->
<!--        </el-form-item>-->

        <el-form-item label="关联技能" prop="skills">
          <el-select
            v-model="form.skills"
            placeholder="请选择关联大技能"
            multiple
            collapse-tags
          >
            <el-option label="Java编程" value="java" />
            <el-option label="Python开发" value="python" />
            <el-option label="前端开发" value="frontend" />
            <el-option label="数据库设计" value="database" />
            <el-option label="系统架构" value="architecture" />
            <el-option label="数据分析" value="data_analysis" />
            <el-option label="网络安全" value="security" />
          </el-select>
        </el-form-item>
        <el-form-item label="岗位状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPost, getPost, delPost, addPost, updatePost ,getIndustryCategoryList, getIndustrySubcategoryList} from "@/api/system/post";

export default {
  name: "Post",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      industryCategoryList: [],       // 所有大类
      industrySubcategoryList: [],    // 当前选中的大类下的小类
      industryCategoryLoaded: false,  // 新增：标记大类数据是否已加载
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
        // 新增：三个新字段
        industryCategory: '',
        industrySubcategory: '',
        skills: []
      },
      // 表单校验
      rules: {
        postName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" }
        ],
        postCode: [
          { required: true, message: "岗位编码不能为空", trigger: "blur" }
        ],
        postSort: [
          { required: true, message: "岗位顺序不能为空", trigger: "blur" }
        ],
        skills: [
          { type: 'array', message: "请选择关联技能", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    // this.loadIndustryCategoryList();
  },
  methods: {
    // 获取行业大类
    loadIndustryCategoryList() {
      getIndustryCategoryList().then(res => {
        // console.log(res);
        this.industryCategoryList = res;
      });
    },
    // 新增：下拉框展开时触发
    handleIndustryCategoryVisible(visible) {
      // 当下拉框展开且数据未加载过时才请求数据
      if (visible && !this.industryCategoryLoaded) {
        this.loadIndustryCategoryList();
        this.industryCategoryLoaded = true; // 标记为已加载
      }
    },
    // 大类改变时，加载小类
    onIndustryCategoryChange(bigVal) {
      this.form.industrySubcategory = ''; // 清空旧值
      if (!bigVal) {
        this.industrySubcategoryList = [];
        return;
      }
      console.log('请求细分行业，大类编码:', bigVal);
      getIndustrySubcategoryList(bigVal).then(res => {
        console.log('细分行业响应数据:', res);
        this.industrySubcategoryList = res;
      });
    },
    /**
     * 根据编码获取行业大类名称
     */
    getIndustryCategoryName(code) {
      if (!code) return '';
      const item = this.industryCategoryList.find(item => item.big === code);
      return item ? item.name : code;
    },

    /**
     * 根据编码获取细分行业名称
     */
    getIndustrySubcategoryName(code) {
      if (!code) return '';
      const item = this.industrySubcategoryList.find(item => item.little === code);
      return item ? item.name : code;
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      listPost(this.queryParams).then(response => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
        skills: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.postId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加岗位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const postId = row.postId || this.ids
      getPost(postId).then(response => {
        this.form = response.data;
        // 将逗号分隔字符串转换为数组
        if (this.form.skills && typeof this.form.skills === 'string') {
          this.form.skills = this.form.skills.split(',');
        }
        this.open = true;
        this.title = "修改岗位";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 创建表单数据副本，避免直接修改响应式数据
          const formData = { ...this.form };
          // 将技能数组转换为逗号分隔字符串
          if (Array.isArray(formData.skills)) {
            formData.skills = formData.skills.join(',');
          }
          // ============== 新增：将编码转换为名称 ==============
          // 行业大类编码转名称
          const categoryItem = this.industryCategoryList.find(
            item => item.big === formData.industryCategory
          );
          formData.industryCategory = categoryItem ? categoryItem.name : formData.industryCategory;

          // 细分行业编码转名称
          const subcategoryItem = this.industrySubcategoryList.find(
            item => item.little === formData.industrySubcategory
          );
          formData.industrySubcategory = subcategoryItem ? subcategoryItem.name : formData.industrySubcategory;
          // ================================================

          if (this.form.postId != undefined) {
            updatePost(formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal.confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？').then(function() {
        return delPost(postIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/post/export', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
/* 添加文本截断样式 */
.ellipsis-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px; /* 可根据需要调整最大宽度 */
}
</style>
