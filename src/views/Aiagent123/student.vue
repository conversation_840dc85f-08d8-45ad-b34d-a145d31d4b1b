<template>
  <div class="student-portrait-page">
    <!-- 顶部横幅 -->
    <div class="hero">
      <h1>学生画像智能分析</h1>
      <p>基于多维度数据的动态学生画像分析</p>
    </div>

    <!-- 创意卡片 + 图表自由布局 -->
    <div class="dashboard-grid">
      <!-- 问数展示 -->
      <el-card class="card full-width">
        <h3>画像问数速览</h3>
        <div class="metrics-grid">
          <div class="metric-item" v-for="(q, index) in questionData" :key="index">
            <div class="metric-value">{{ q.value }}</div>
            <div class="metric-title">{{ q.title }}</div>
          </div>
        </div>
      </el-card>

      <!-- 雷达图 -->
      <el-card class="card">
        <h3>学生能力雷达图</h3>
        <div id="radarChart" class="chart-box"></div>
      </el-card>

      <!-- 折线图 -->
      <el-card class="card">
        <h3>能力趋势折线图</h3>
        <div id="lineChart" class="chart-box"></div>
      </el-card>

      <!-- AI评语 -->
      <el-card class="card full-width">
        <h3>评语分析</h3>
        <blockquote class="comment-text">{{ studentComment }}</blockquote>
      </el-card>

      <!-- 表格 -->
      <el-card class="card full-width">
        <h3>详细画像数据表</h3>
        <el-table :data="tableData" stripe border>
          <el-table-column prop="dimension" label="维度" width="180" />
          <el-table-column prop="score" label="得分" width="100" />
          <el-table-column prop="level" label="评级" width="100" />
          <el-table-column prop="comment" label="简要分析" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'StudentPortraitPage',
  data() {
    return {
      questionData: [
        { title: '知识掌握度', value: '85%' },
        { title: '逻辑思维', value: '78%' },
        { title: '参与度', value: '92%' },
        { title: '实践能力', value: '75%' },
        { title: '创造力', value: '88%' },
        { title: '学科偏好', value: '理科倾向' },
      ],
      studentComment:
        '学生在学习过程中表现出良好的知识掌握能力，具备较强的逻辑思维和创造力，参与度积极，适合发展理工类方向，实践能力尚有提升空间。',
      tableData: [
        {
          dimension: '知识掌握度',
          score: 85,
          level: '优秀',
          comment: '基础知识扎实，掌握全面。',
        },
        {
          dimension: '逻辑思维',
          score: 78,
          level: '良好',
          comment: '能较好地进行推理与分析。',
        },
        {
          dimension: '参与度',
          score: 92,
          level: '优秀',
          comment: '课堂活跃，任务完成积极。',
        },
        {
          dimension: '实践能力',
          score: 75,
          level: '良好',
          comment: '具备一定实践能力，需持续锻炼。',
        },
        {
          dimension: '创造力',
          score: 88,
          level: '优秀',
          comment: '思维活跃，富有创造性。',
        },
      ],
    }
  },
  mounted() {
    this.initRadar()
    this.initLine()
  },
  methods: {
    initRadar() {
      const radar = echarts.init(document.getElementById('radarChart'))
      radar.setOption({
        title: { text: '学生能力雷达图' },
        tooltip: {},
        radar: {
          indicator: [
            { name: '知识掌握度', max: 100 },
            { name: '逻辑思维', max: 100 },
            { name: '参与度', max: 100 },
            { name: '实践能力', max: 100 },
            { name: '创造力', max: 100 },
          ],
        },
        series: [
          {
            type: 'radar',
            data: [
              {
                value: [85, 78, 92, 75, 88],
                name: '学生画像',
              },
            ],
          },
        ],
      })
    },
    initLine() {
      const line = echarts.init(document.getElementById('lineChart'))
      line.setOption({
        title: { text: '能力趋势折线图' },
        tooltip: {},
        xAxis: {
          type: 'category',
          data: ['第1月', '第2月', '第3月', '第4月', '第5月'],
        },
        yAxis: { type: 'value' },
        series: [
          {
            name: '逻辑思维',
            type: 'line',
            data: [72, 74, 76, 77, 78],
          },
          {
            name: '知识掌握度',
            type: 'line',
            data: [80, 81, 83, 84, 85],
          },
        ],
      })
    },
  },
}
</script>

<style scoped>
.student-portrait-page {
  background: #f4f7f9;
  font-family: 'Segoe UI', sans-serif;
}

.hero {
  background: linear-gradient(to right, #667eea, #764ba2);
  color: white;
  padding: 40px 30px;
  text-align: center;
  border-radius: 0 0 12px 12px;
}

.hero h1 {
  font-size: 30px;
  margin: 0 0 8px;
}

.hero p {
  font-size: 14px;
  opacity: 0.85;
}

.dashboard-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 30px;
}

.card {
  flex: 1 1 calc(50% - 20px);
  box-sizing: border-box;
  border-radius: 10px;
}

.full-width {
  flex: 1 1 100%;
}

.metrics-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 10px;
}

.metric-item {
  flex: 1 1 calc(16.66% - 10px);
  background: #eaf0f8;
  text-align: center;
  padding: 12px 6px;
  border-radius: 8px;
}

.metric-value {
  font-size: 18px;
  color: #409EFF;
  font-weight: bold;
}

.metric-title {
  margin-top: 5px;
  font-size: 13px;
  color: #666;
}

.chart-box {
  height: 350px;
}

.comment-text {
  background: #fffbe6;
  border-left: 5px solid #ff9900;
  padding: 16px;
  font-size: 15px;
  color: #5c5c5c;
  line-height: 1.7;
  margin: 0;
}
</style>
