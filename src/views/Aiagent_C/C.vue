<template>
    <div class="learning-path-page">
        <!-- 顶部标题区域 -->
        <!-- <div class="header">
            <h1>智能学习路径规划</h1>
            <p>帮助学生科学规划技能提升路径，快速成长</p>
        </div> -->

        <div class="hero">
            <div class="hero-text">
                <h1>智能学习路径规划</h1>
                 <p>帮助学生科学规划技能提升路径，快速成长</p>
            </div>
            <!-- <el-button type="primary" icon="el-icon-download" @click="downloadReport">下载对标报告</el-button> -->
        </div>

        <!-- 学习内容关联的知识体系图谱 -->
        <el-card class="section">
            <h2>一、学习内容关联知识体系图谱</h2>
            <div id="knowledgeGraph" style="height: 500px"></div>
        </el-card>

        <!-- 学习历史表现与不足分析 -->
        <el-card class="section">
            <h2>二、学生学习表现分析</h2>
            <el-row :gutter="20" class="performance-row">
                <el-col :span="12">
                    <el-card>
                        <h3>学习内容历史学习情况</h3>
                        <div id="learningHistory" style="height: 300px"></div>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card>
                        <h3>学习表现与不足分析</h3>
                        <div id="performanceChart" style="height: 300px"></div>
                    </el-card>
                </el-col>
            </el-row>
        </el-card>

        <!-- 配套课程资源 -->
        <el-card class="section">
            <h2>三、配套课程资源</h2>
            <el-table :data="courseData" border style="width: 100%">
                <el-table-column prop="name" label="课程名称" width="200" />
                <el-table-column prop="description" label="课程简介" />
            </el-table>
        </el-card>

        <!-- 学习路径规划 -->
        <el-card class="section">
            <h2>四、学习路径规划</h2>
            <div id="learningPathChart" style="height: 400px"></div>
        </el-card>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            // 假数据：配套课程资源
            courseData: [
                { name: 'Java 基础', description: '学习Java语言的基础知识，包括语法、面向对象编程等。' },
                { name: '前端开发基础', description: '学习HTML, CSS, JavaScript，构建现代Web页面。' },
                { name: '算法与数据结构', description: '学习常见的数据结构和算法，提高编程能力。' },
                { name: '系统设计', description: '学习大型系统设计的原则与实践，培养架构思维。' }
            ]
        };
    },
    mounted() {
        this.initKnowledgeGraph();
        this.initLearningHistoryChart();
        this.initPerformanceChart();
        this.initLearningPathChart();
    },
    methods: {
        // 初始化知识体系图谱
        initKnowledgeGraph() {
            const graph = echarts.init(document.getElementById('knowledgeGraph'));

            // 定义同类色（蓝色系渐变）
            const colors = [
                '#4A90E2', '#5B9BD5', '#6BAED6', '#7AADC7', '#8AB2B8',
                '#9BB8A9', '#ACBE9A', '#BCC487', '#CCD875', '#DDEC62',
                '#F0E63E', '#F7D13C', '#F1B539', '#F29C2C'
            ];

            const option = {
                title: { text: '学习内容知识体系图谱' },
                tooltip: {},
                series: [{
                    type: 'graph',
                    layout: 'force',
                    force: {
                        repulsion: 500, // Increased repulsion to make nodes push away more
                        gravity: 0.1, // Slightly increased gravity to pull nodes together
                        edgeLength: [50, 100], // Varying edge length for more flexibility
                        layoutAnimation: true // Enable animation for better visual experience
                    },
                    label: {
                        show: true,
                        position: 'inside',
                        fontSize: 14,
                        color: '#333'
                    },
                    data: [
                        { name: 'Java 基础', symbolSize: 100, label: { show: true, position: 'inside' }, itemStyle: { color: colors[0] } },
                        { name: '前端开发基础', symbolSize: 40, label: { show: true, position: 'inside' }, itemStyle: { color: colors[1] } },
                        { name: '算法与数据结构', symbolSize: 40, label: { show: true, position: 'inside' }, itemStyle: { color: colors[2] } },
                        { name: '系统设计', symbolSize: 40, label: { show: true, position: 'inside' }, itemStyle: { color: colors[3] } },
                        { name: '面向对象编程', symbolSize: 30, label: { show: true, position: 'inside' }, itemStyle: { color: colors[4] } },
                        { name: 'Web开发', symbolSize: 30, label: { show: true, position: 'inside' }, itemStyle: { color: colors[5] } },
                        { name: '数据结构', symbolSize: 30, label: { show: true, position: 'inside' }, itemStyle: { color: colors[6] } },
                        { name: '系统架构', symbolSize: 30, label: { show: true, position: 'inside' }, itemStyle: { color: colors[7] } },
                        { name: '前端框架', symbolSize: 35, label: { show: true, position: 'inside' }, itemStyle: { color: colors[8] } },
                        { name: '数据库', symbolSize: 40, label: { show: true, position: 'inside' }, itemStyle: { color: colors[9] } },
                        { name: '操作系统', symbolSize: 40, label: { show: true, position: 'inside' }, itemStyle: { color: colors[10] } },
                        { name: '网络编程', symbolSize: 45, label: { show: true, position: 'inside' }, itemStyle: { color: colors[11] } },
                        { name: '分布式系统', symbolSize: 50, label: { show: true, position: 'inside' }, itemStyle: { color: colors[12] } },
                        { name: '大数据', symbolSize: 55, label: { show: true, position: 'inside' }, itemStyle: { color: colors[13] } },
                        { name: '人工智能', symbolSize: 60, label: { show: true, position: 'inside' }, itemStyle: { color: colors[0] } }
                    ],
                    links: [
                        { source: 'Java 基础', target: '面向对象编程' },
                        { source: '前端开发基础', target: 'Web开发' },
                        { source: '算法与数据结构', target: '数据结构' },
                        { source: '系统设计', target: '系统架构' },
                        { source: 'Web开发', target: '前端框架' },
                        { source: '数据库', target: '系统设计' },
                        { source: '操作系统', target: '网络编程' },
                        { source: '分布式系统', target: '大数据' },
                        { source: '人工智能', target: '大数据' },
                        { source: '人工智能', target: '算法与数据结构' },
                        { source: '前端开发基础', target: '前端框架' },
                        { source: '数据结构', target: '算法与数据结构' },
                        { source: '系统架构', target: '分布式系统' }
                    ],
                    roam: true
                }]
            };
            graph.setOption(option);
        }

        ,
        // 初始化历史学习情况图表
        initLearningHistoryChart() {
            const learningHistory = echarts.init(document.getElementById('learningHistory'));
            const option = {
                title: { text: '学习内容历史学习情况' },
                tooltip: {},
                xAxis: { type: 'category', data: ['2021年', '2022年', '2023年'] },
                yAxis: { type: 'value' },
                series: [
                    { name: '学习时长', type: 'bar', data: [30, 45, 60] },
                    { name: '学习完成度', type: 'line', data: [70, 80, 90] }
                ]
            };
            learningHistory.setOption(option);
        },
        // 初始化学习表现与不足分析图表
        initPerformanceChart() {
            const performanceChart = echarts.init(document.getElementById('performanceChart'));
            const option = {
                title: { text: '学习表现与不足分析' },
                tooltip: {},
                xAxis: { type: 'category', data: ['Java', '前端', '算法', '系统设计'] },
                yAxis: { type: 'value' },
                series: [
                    { name: '表现', type: 'bar', data: [85, 90, 75, 70] },
                    { name: '不足', type: 'bar', data: [15, 10, 25, 30] }
                ]
            };
            performanceChart.setOption(option);
        },
        // 初始化学习路径规划图表
        initLearningPathChart() {
            const learningPathChart = echarts.init(document.getElementById('learningPathChart'));
            const option = {
                title: { text: '学习路径规划' },
                tooltip: {},
                series: [{
                    type: 'tree',
                    data: [{
                        name: '学习路径',
                        children: [
                            {
                                name: '阶段1',
                                symbol: 'circle',
                                symbolSize: 60,
                                label: { show: true, position: 'inside' },
                                children: [
                                    { name: 'Java 基础', symbol: 'circle', symbolSize: 50, label: { show: true, position: 'inside' } },
                                    { name: '前端开发基础', symbol: 'circle', symbolSize: 50, label: { show: true, position: 'inside' } }
                                ]
                            },
                            {
                                name: '阶段2',
                                symbol: 'circle',
                                symbolSize: 60,
                                label: { show: true, position: 'inside' },
                                children: [
                                    {
                                        name: '数据结构',
                                        symbol: 'circle',
                                        symbolSize: 50,
                                        label: { show: true, position: 'inside' },
                                        children: [
                                            { name: '链表', symbol: 'circle', symbolSize: 40, label: { show: true, position: 'inside' } },
                                            { name: '栈与队列', symbol: 'circle', symbolSize: 40, label: { show: true, position: 'inside' } }
                                        ]
                                    },
                                    {
                                        name: '算法与数据结构',
                                        symbol: 'circle',
                                        symbolSize: 50,
                                        label: { show: true, position: 'inside' }
                                    }
                                ]
                            },
                            {
                                name: '阶段3',
                                symbol: 'circle',
                                symbolSize: 70,
                                label: { show: true, position: 'inside' },
                                children: [
                                    {
                                        name: '系统设计',
                                        symbol: 'circle',
                                        symbolSize: 60,
                                        label: { show: true, position: 'inside' },
                                        children: [
                                            { name: '负载均衡', symbol: 'circle', symbolSize: 50, label: { show: true, position: 'inside' } },
                                            { name: '分布式系统', symbol: 'circle', symbolSize: 50, label: { show: true, position: 'inside' } }
                                        ]
                                    },
                                    {
                                        name: 'Web 开发',
                                        symbol: 'circle',
                                        symbolSize: 60,
                                        label: { show: true, position: 'inside' }
                                    }
                                ]
                            },
                            {
                                name: '阶段4',
                                symbol: 'circle',
                                symbolSize: 80,
                                label: { show: true, position: 'inside' },
                                children: [
                                    {
                                        name: '大数据',
                                        symbol: 'circle',
                                        symbolSize: 70,
                                        label: { show: true, position: 'inside' },
                                        children: [
                                            { name: '数据分析', symbol: 'circle', symbolSize: 60, label: { show: true, position: 'inside' } },
                                            { name: '数据处理', symbol: 'circle', symbolSize: 60, label: { show: true, position: 'inside' } }
                                        ]
                                    },
                                    {
                                        name: '人工智能',
                                        symbol: 'circle',
                                        symbolSize: 70,
                                        label: { show: true, position: 'inside' }
                                    }
                                ]
                            }
                        ]
                    }],
                    top: '10%',
                    left: '10%',
                    roam: true,
                    label: {
                        position: 'top',
                        fontSize: 14,
                        color: '#333'
                    },
                    lineStyle: {
                        color: '#4A90E2',
                        width: 2,
                        type: 'solid'
                    }
                }]
            };
            learningPathChart.setOption(option);
        }

    }
};
</script>

<style scoped>
.learning-path-page {
    padding: 5px;
    font-family: Arial, sans-serif;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 28px;
    color: #333;
}

.header p {
    font-size: 16px;
    color: #888;
}


.hero {
  display: flex;
  justify-content: space-between;
  align-items: center;
    background: linear-gradient(to right, #667eea, #764ba2);
  color: white;
  padding: 30px 40px;
  border-bottom: 3px solid #409EFF;
}

.hero-text h1 {
  font-size: 28px;
  margin: 0 0 8px 0;
}

.hero-text p {
  font-size: 14px;
  opacity: 0.85;
  margin: 0;
}

.section {

    margin-bottom: 40px;
}

.chart-row {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.chart-box {
    flex: 1;
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.el-table th,
.el-table td {
    padding: 10px;
    text-align: center;
}

.el-button {
    font-weight: bold;
    background-color: #409EFF;
    color: white;
    border: none;
}

.el-card {
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: #fff;
}
</style>
