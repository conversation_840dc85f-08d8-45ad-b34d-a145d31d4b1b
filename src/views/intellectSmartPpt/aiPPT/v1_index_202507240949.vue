<template>
  <div
      class="modern-aippt-container"
      :style="containerBackgroundStyle"
  >
    <!-- 现代化背景 -->
    <div class="modern-background" :class="{ 'template-background-active': isTemplateBackgroundActive }">
      <div class="gradient-overlay"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <!-- API key缺失时显示的全屏提示 -->
    <div v-if="!hasValidApiKey && !showTemplateCreationView" class="modern-auth-prompt">
      <div class="auth-card">
        <div class="auth-icon">
          <i class="el-icon-lock"></i>
        </div>
        <h2 class="auth-title">需要认证访问</h2>
        <p class="auth-description">请完成身份验证以继续使用AI PPT生成服务</p>
        <el-button
            type="primary"
            size="large"
            class="auth-button"
            @click="handleAuthenticateLoginAccess"
        >
          <i class="el-icon-user"></i>
          立即认证
        </el-button>
        <div class="auth-tips">
          <div class="tip-item">
            <i class="el-icon-check"></i>
            <span>确保认证参数正确</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-check"></i>
            <span>验证API密钥有效</span>
          </div>
        </div>
      </div>
    </div>




    <!-- 主要内容区域 -->
    <div
        v-loading.fullscreen.lock="isSubmittingPpt"
        element-loading-text="🚀 加载中,请稍后..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
    >
    </div>
    <div v-if="hasValidApiKey">
      <!-- 主内容卡片 -->
      <div class="main-content-card">
        <div ref="form" class="modern-form"
             :style="{
          background: activeStep === 1  ? '' : 'rgba(255, 255, 255, 0.75)'
}"
        >

          <!-- 步骤1: 内容输入区域 -->
          <div v-show="activeStep === 0" class="modern-step-container">
            <!-- 现代化标签页 -->
            <el-tabs v-model="activeContentTab" @tab-click="handleContentTabClick" :before-leave="handleBeforeTabLeave"
                     class="modern-tabs" :class="{'tabs-disabled': isAnyGenerating}"
            >
              <!-- 用户信息和设置区域 -->
              <div v-if="activeStep===0 && !shouldShowPPTEditArea" class="user-settings-area">
                <div class="user-info">
                  <div class="user-avatar">
                    <i class="el-icon-user"></i>
                  </div>
                  <span class="user-name">{{ userName || '用户' }}</span>
                </div>
                <div class="settings-container" ref="settingsContainer">
                  <button
                    class="settings-button"
                    @click="toggleThemeSelector"
                    :class="{ 'active': showThemeSelector }"
                  >
                    <i class="el-icon-setting"></i>
                  </button>

                  <!-- 主题选择器 -->
                  <div
                    v-show="showThemeSelector"
                    class="theme-selector-dropdown"
                    @click.stop
                  >
                    <div class="theme-selector-header">
                      <span>主题</span>
                    </div>
                    <div class="theme-options">
                      <div
                        v-for="theme in backgroundThemeOptions"
                        :key="theme.id"
                        class="theme-option"
                        :class="{ 'active': currentTheme === theme.id }"
                        @click="selectTheme(theme.id)"
                      >
                        <div class="theme-preview" :style="{ background: theme.gradient }"></div>
                        <span class="theme-name">{{ theme.name }}</span>
                        <i v-if="currentTheme === theme.id" class="el-icon-check theme-check"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- PPT创作 标签页 -->
              <el-tab-pane name="creation">
                <span slot="label" class="tab-label">
                  <i class="el-icon-magic-stick"></i>
                  PPT创作
                </span>

                <!-- 添加过渡动画包装器 -->
                <transition name="tab-content-fade" mode="out-in" appear>
                  <div key="creation-content" class="tab-content-wrapper">

                    <!-- 创作方式选择 -->
                    <template v-if="!showOutlineEditor">
                      <div class="creation-welcome">
                        <h2 class="welcome-title">开始创作您的专业PPT</h2>
                        <p class="welcome-subtitle">选择最适合您的创作方式，AI将为您生成精美的演示文稿</p>
                      </div>

                      <!-- 创作方式卡片 -->
                      <div class="creation-methods">
                        <div
                            class="method-card"
                            :class="{ active: requestType === 'topic' }"
                            @click="handleRequestTypeChange('topic')"
                            :disabled="isAnyGenerating"
                        >
                          <div class="method-icon">
                            <i class="el-icon-magic-stick"></i>
                          </div>
                          <div class="method-content">
                            <h3 class="method-title">AI智能创作</h3>
                            <p class="method-desc">输入主题，AI智能生成完整PPT</p>
                            <div class="method-features">
                              <span class="feature-tag">智能</span>
                              <span class="feature-tag">快速</span>
                            </div>
                          </div>
                        </div>

                        <div
                            class="method-card"
                            :class="{ active: requestType === 'text' }"
                            @click="handleRequestTypeChange('text')"
                            :disabled="isAnyGenerating"
                        >
                          <div class="method-icon">
                            <i class="el-icon-edit"></i>
                          </div>
                          <div class="method-content">
                            <h3 class="method-title">文本创作</h3>
                            <p class="method-desc">输入详细内容，精准生成PPT</p>
                            <div class="method-features">
                              <span class="feature-tag">精准</span>
                              <span class="feature-tag">详细</span>
                            </div>
                          </div>
                        </div>

                        <div
                            class="method-card"
                            :class="{ active: requestType === 'file' }"
                            @click="handleRequestTypeChange('file')"
                            :disabled="isAnyGenerating"
                        >
                          <div class="method-icon">
                            <i class="el-icon-upload2"></i>
                          </div>
                          <div class="method-content">
                            <h3 class="method-title">文件创作</h3>
                            <p class="method-desc">上传文档，自动转换为PPT</p>
                            <div class="method-features">
                              <span class="feature-tag">便捷</span>
                              <span class="feature-tag">转换</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <!-- 内容输入和展示区域 -->
                    <template v-if="!showOutlineEditor">
                      <!-- 添加创作内容区域的过渡动画 -->
                      <transition name="creation-content-fade" mode="out-in" appear>
                        <div :key="requestType" class="creation-content-wrapper">
                          <!-- 现代化聊天区域 -->
                          <transition name="chat-fade" appear>
                            <div v-if="requestType === 'topic' && topicMessages.length > 0"
                                 class="modern-chat-container"
                            >
                              <div class="chat-header">
                                <div class="chat-title">
                                  <i class="el-icon-magic-stick"></i>
                                  <span>AI智能创作</span>
                                </div>
                              </div>
                              <div class="chat-messages">
                                <div v-for="(message, index) in topicMessages" :key="`topic-${index}`"
                                     :class="['modern-message-item', message.role === 'user' ? 'message-user' : 'message-assistant']"
                                     class="message-item-with-hover"
                                >
                                  <div class="message-avatar">
                                    <div class="avatar-icon">
                                      <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-chat-dot-square'"></i>
                                    </div>
                                  </div>
                                  <div class="message-bubble">
                                    <div class="message-content">
                                      <span v-if="message.role === 'user'" class="user-message">{{
                                          message.content
                                        }}</span>
                                      <streaming-markdown
                                          v-else
                                          :content="message.content"
                                          :show-cursor="false"
                                          @image-click="previewImage"
                                          :scale="0.9"
                                          class="assistant-message"
                                      />
                                    </div>
                                    <!-- 编辑按钮紧贴消息对话框右下角外侧 -->
                                    <button
                                        v-if="message.role === 'assistant' && index === topicMessages.length - 1 && !isTopicGenerating"
                                        @click="startEditLastAssistantMessage('topic')"
                                        class="message-edit-button"
                                        title="编辑此回复"
                                    >
                                      <i class="el-icon-edit"></i>
                                    </button>
                                  </div>
                                </div>

                                <!-- 生成中的消息 -->
                                <div v-if="isTopicGenerating" class="modern-message-item message-assistant generating">
                                  <div class="message-avatar">
                                    <div class="avatar-icon generating-avatar">
                                      <i class="el-icon-chat-dot-square"></i>
                                    </div>
                                  </div>
                                  <div class="message-bubble">
                                    <div class="message-content">
                                      <streaming-markdown
                                          :content="topicStreamContent"
                                          :show-cursor="showStreamCursor"
                                          @image-click="previewImage"
                                          :scale="0.9"
                                          class="assistant-message"
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </transition>
                          <transition name="chat-fade" appear>
                            <div v-if="requestType === 'text' && textMessages.length > 0" class="modern-chat-container">
                              <div class="chat-header">
                                <div class="chat-title">
                                  <i class="el-icon-edit-outline"></i>
                                  <span>文本创作</span>
                                </div>
                              </div>
                              <div class="chat-messages">
                                <div v-for="(message, index) in textMessages" :key="`text-${index}`"
                                     :class="['modern-message-item', message.role === 'user' ? 'message-user' : 'message-assistant']"
                                     class="message-item-with-hover"
                                >
                                  <div class="message-avatar">
                                    <div class="avatar-icon">
                                      <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-chat-dot-square'"></i>
                                    </div>
                                  </div>
                                  <div class="message-bubble">
                                    <div class="message-content">
                                      <span v-if="message.role === 'user'" class="user-message">{{
                                          message.content
                                        }}</span>
                                      <streaming-markdown
                                          v-else
                                          :content="message.content"
                                          :show-cursor="false"
                                          @image-click="previewImage"
                                          :scale="0.9"
                                          class="assistant-message"
                                      />
                                    </div>
                                    <!-- 编辑按钮紧贴消息对话框右下角外侧 -->
                                    <button
                                        v-if="message.role === 'assistant' && index === textMessages.length - 1 && !isTextGenerating"
                                        @click="startEditLastAssistantMessage('text')"
                                        class="message-edit-button"
                                        title="编辑此回复"
                                    >
                                      <i class="el-icon-edit"></i>
                                    </button>
                                  </div>
                                </div>
                                <!-- 生成中的消息 -->
                                <div v-if="isTextGenerating" class="modern-message-item message-assistant generating">
                                  <div class="message-avatar">
                                    <div class="avatar-icon generating-avatar">
                                      <i class="el-icon-chat-dot-square"></i>
                                    </div>
                                  </div>
                                  <div class="message-bubble">
                                    <div class="message-content">
                                      <streaming-markdown
                                          :content="textStreamContent"
                                          :show-cursor="showStreamCursor"
                                          :scale="0.9"
                                          @image-click="previewImage"
                                          class="assistant-message"
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </transition>
                          <transition name="chat-fade" appear>
                            <div v-if="requestType === 'file' && fileMessages.length > 0" class="modern-chat-container">
                              <div class="chat-header">
                                <div class="chat-title">
                                  <i class="el-icon-upload2"></i>
                                  <span>文件创作</span>
                                </div>
                              </div>
                              <div class="chat-messages">
                                <div v-for="(message, index) in fileMessages" :key="`file-${index}`"
                                     :class="['modern-message-item', message.role === 'user' ? 'message-user' : 'message-assistant']"
                                     class="message-item-with-hover"
                                >
                                  <div class="message-avatar">
                                    <div class="avatar-icon">
                                      <i :class="message.role === 'user' ? 'el-icon-folder-checked' : 'el-icon-chat-dot-square'"></i>
                                    </div>
                                  </div>
                                  <div class="message-bubble">
                                    <div class="message-content">
                                      <span v-if="message.role === 'user'" class="user-message">{{
                                          message.content
                                        }}</span>
                                      <streaming-markdown
                                          v-else
                                          :content="message.content"
                                          :show-cursor="false"
                                          @image-click="previewImage"
                                          :scale="0.9"
                                          class="assistant-message"
                                      />
                                    </div>
                                    <!-- 编辑按钮紧贴消息对话框右下角外侧 -->
                                    <button
                                        v-if="message.role === 'assistant' && index === fileMessages.length - 1 && !isFileGenerating"
                                        @click="startEditLastAssistantMessage('file')"
                                        class="message-edit-button"
                                        title="编辑此回复"
                                    >
                                      <i class="el-icon-edit"></i>
                                    </button>
                                  </div>
                                </div>
                                <!-- 生成中的消息 -->
                                <div v-if="isFileGenerating" class="modern-message-item message-assistant generating">
                                  <div class="message-avatar">
                                    <div class="avatar-icon generating-avatar">
                                      <i class="el-icon-chat-dot-square"></i>
                                    </div>
                                  </div>
                                  <div class="message-bubble">
                                    <div class="message-content">
                                      <streaming-markdown
                                          :content="fileStreamContent"
                                          :show-cursor="showStreamCursor"
                                          :scale="0.9"
                                          @image-click="previewImage"
                                          class="assistant-message"
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </transition>
                          <!-- 现代化输入区域 -->
                          <transition name="input-section-slide" appear>
                            <div v-if="requestType === 'topic'" class="modern-input-section">
                              <div class="input-card">
                                <!-- 主输入框 -->
                                <div class="main-input-area">
                                  <div class="input-wrapper">
                                    <el-input
                                        v-model="topicPrompt"
                                        placeholder="✨ 描述您想要的PPT主题，AI将为您智能生成..."
                                        @keyup.enter.native="handleTopicSubmit"
                                        maxlength="100"
                                        show-word-limit
                                        @mouseenter.native="handleTopicMouseEnter"
                                        @mouseleave.native="handleTopicMouseLeave"
                                        class="modern-input"
                                        size="large"
                                    >
                                      <template slot="prepend">
                                        <i class="el-icon-magic-stick input-icon"></i>
                                      </template>
                                    </el-input>
                                  </div>

                                  <!-- 快速操作按钮 -->
                                  <div class="quick-actions" v-if="!isTopicGenerating">
                                    <el-button
                                        type="primary"
                                        icon="el-icon-magic-stick"
                                        @click="handleTopicSubmit"
                                        :loading="isTopicGenerating"
                                        :disabled="!topicPrompt.trim() || isTopicGenerating"
                                        size="large"
                                        class="create-button"
                                    >
                                      <span v-if="!isTopicGenerating">立即创作</span>
                                      <span v-else>创作中...</span>
                                    </el-button>
                                  </div>
                                </div>

                                <!-- 智能建议 -->
                                <transition name="suggestion-fade">
                                  <div v-if="showTopicSuggestions && !isTopicGenerating"
                                       class="modern-suggestions show-above"
                                       :style="suggestionBoxStyle"
                                       @click.stop
                                       @mouseenter="handleSuggestionBoxMouseEnter"
                                       @mouseleave="handleSuggestionBoxMouseLeave"
                                  >
                                    <div class="suggestions-header">
                                      <i class="el-icon-lightbulb"></i>
                                      <span>✨ 试一试✨ </span>
                                    </div>
                                    <div class="suggestions-list">
                                      <div
                                          v-for="(item, index) in topicSuggestionList"
                                          :key="index"
                                          class="suggestion-item"
                                          @click.stop="selectTopicSuggestion(item.dictValue)"
                                      >
                                        <i class="el-icon-right"></i>
                                        <span>{{ item.dictValue }}</span>
                                      </div>
                                    </div>
                                  </div>
                                </transition>

                                <!-- 输入框底部工具栏 -->
                                <transition name="toolbar-slide" appear>
                                  <div class="input-toolbar" v-if="!isTopicGenerating">
                                    <!-- 选择器组区域 -->
                                    <div class="selectors-container">
                                      <!-- 场景选择器 -->
                                      <card-selector
                                          ref="topicSceneSelector"
                                          v-model="selectedDemoSceneValue"
                                          :options="demoSceneOptions"
                                          label="场景"
                                          placeholder="选择场景"
                                          icon="el-icon-picture-outline-round"
                                          @change="handleDemoSceneChange"
                                          class="selector-item"
                                      />

                                      <!-- 联网搜索 -->
                                      <web-search-card
                                          v-model="webSearch"
                                          @change="handleWebSearchChange"
                                          class="selector-item"
                                      />

                                      <!-- 语言选择器 -->
                                      <card-selector
                                          ref="topicLanguageSelector"
                                          v-model="selectedLanguage"
                                          :options="languageOptions"
                                          label="语言"
                                          placeholder="选择语言"
                                          icon="el-icon-document"
                                          @change="handleLanguageChange"
                                          class="selector-item"
                                      />

                                      <!-- 模型选择器 -->
                                      <card-selector
                                          ref="topicModelSelector"
                                          v-model="selectedChatModelValue"
                                          :options="chatModelOptions"
                                          label="模型"
                                          placeholder="选择模型"
                                          icon="el-icon-cpu"
                                          @change="handleModelChange"
                                          class="selector-item"
                                      />
                                    </div>
                                  </div>
                                </transition>
                              </div>
                            </div>
                          </transition>
                          <transition name="input-section-slide" appear>
                            <div v-if="requestType==='text'"
                                 class="content-wrapper text-input-wrapper modern-input-section"
                            >
                              <div class="input-card">
                                <div class="text-input-container">
                                  <!-- 输入框主体 -->
                                  <div class="input-box-container">
                                    <div class="input-field-wrapper">
                                      <el-input type="textarea" v-model="textContent" placeholder="请输入生成PPT文本"
                                                maxlength="8000"
                                                show-word-limit :autosize="{ minRows: 4, maxRows: 12}"
                                                class="content-textarea"
                                      />
                                    </div>

                                    <!-- 输入框底部工具栏 -->
                                    <transition name="toolbar-slide" appear>
                                      <div class="input-toolbar" v-if="!isTextGenerating && !showOutlineEditor">
                                        <!-- 选择器组区域 -->
                                        <div class="selectors-container">
                                          <!-- 场景选择器 -->
                                          <card-selector
                                              ref="textSceneSelector"
                                              v-model="selectedDemoSceneValue"
                                              :options="demoSceneOptions"
                                              label="场景"
                                              placeholder="选择场景"
                                              icon="el-icon-picture-outline-round"
                                              @change="handleDemoSceneChange"
                                              class="selector-item"
                                          />

                                          <!-- 联网搜索 -->
                                          <web-search-card
                                              v-model="webSearch"
                                              @change="handleWebSearchChange"
                                              class="selector-item"
                                          />

                                          <!-- 语言选择器 -->
                                          <card-selector
                                              ref="textLanguageSelector"
                                              v-model="selectedLanguage"
                                              :options="languageOptions"
                                              label="语言"
                                              placeholder="选择语言"
                                              icon="el-icon-document"
                                              @change="handleLanguageChange"
                                              class="selector-item"
                                          />

                                          <!-- 模型选择器 -->
                                          <card-selector
                                              ref="textModelSelector"
                                              v-model="selectedChatModelValue"
                                              :options="chatModelOptions"
                                              label="模型"
                                              placeholder="选择模型"
                                              icon="el-icon-cpu"
                                              @change="handleModelChange"
                                              class="selector-item"
                                          />
                                        </div>

                                        <!-- 创作按钮 -->
                                        <div class="create-btn-container">
                                          <el-button
                                              type="primary"
                                              icon="el-icon-document-checked"
                                              @click="handleTextSubmit"
                                              :loading="isTextGenerating"
                                              :disabled="!textContent.trim() || isTextGenerating"
                                              size="medium"
                                              class="create-btn"
                                          >立即创作
                                          </el-button>
                                        </div>
                                      </div>
                                    </transition>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </transition>
                          <transition name="input-section-slide" appear>
                            <div v-if="requestType==='file'" class="content-wrapper file-upload-container">
                              <el-upload class="upload-demo" drag ref="upload" :limit="1" :file-list="fileList"
                                         :auto-upload="false"
                                         accept=".txt,.doc,.docx,.pdf" :on-change="handleFileChange"
                                         :on-remove="handleRemove"
                                         :show-file-list="false" :action="'#'"
                              >

                                <template v-if="!isFileGenerating && !showOutlineEditor">
                                  <template v-if="fileList.length === 0">
                                    <div class="upload-content">
                                      <div class="file-type-icons"><i class="el-icon-document icon-word"></i><i
                                          class="el-icon-data-analysis icon-ppt"
                                      ></i><i class="el-icon-reading icon-pdf"></i><i
                                          class="el-icon-tickets icon-txt"
                                      ></i></div>
                                      <div class="el-upload__text">将文件拖拽至此处或<em>点击上传</em></div>
                                      <div class="el-upload__tip">支持doc、docx、pdf、txt、md等文件格式</div>
                                      <p class="upload-privacy-text">大小限制{{ uploadSingleFileMaxSize }}MB</p></div>
                                  </template>
                                  <template v-else>
                                    <div class="upload-file-selected-state">
                                      <div class="file-info-box"><i class="el-icon-document"></i>
                                        <div class="file-details"><span class="file-name"
                                                                        :title="fileList[0].name"
                                        >{{
                                            fileList[0].name
                                          }}</span><span
                                            class="file-size"
                                        >{{ formatFileSize(fileList[0].size) }}</span></div>
                                      </div>
                                      <el-button type="text" class="re-upload-link" @click="handleRemove"><i
                                          class="el-icon-refresh-right"
                                      ></i> 重新上传
                                      </el-button>
                                    </div>
                                  </template>
                                </template>
                                <div v-else class="generating-placeholder"><i class="el-icon-loading"></i>
                                  <p>正在处理文件中,请稍后...</p></div>
                              </el-upload>
                              <div v-if="fileList.length > 0 && !isFileGenerating && !showOutlineEditor"
                                   class="selected-file-info"
                              >
                                <i class="el-icon-document-checked"></i><span>{{ fileList[0].name }}</span>
                                <el-button type="text" @click="handleRemove" class="remove-file-btn-inline"><i
                                    class="el-icon-circle-close"
                                ></i></el-button>
                              </div>
                              <transition name="toolbar-slide" appear>
                                <div v-if="fileList.length > 0 && !isFileGenerating && !showOutlineEditor"
                                     class="file-toolbar-container"
                                >
                                  <!-- 输入框底部工具栏 -->
                                  <div class="input-toolbar">
                                    <!-- 选择器组区域 -->
                                    <div class="selectors-container">
                                      <!-- 场景选择器 -->
                                      <card-selector
                                          ref="fileSceneSelector"
                                          v-model="selectedDemoSceneValue"
                                          :options="demoSceneOptions"
                                          label="场景"
                                          placeholder="选择场景"
                                          icon="el-icon-picture-outline-round"
                                          @change="handleDemoSceneChange"
                                          class="selector-item"
                                      />

                                      <!-- 语言选择器 -->
                                      <card-selector
                                          ref="fileLanguageSelector"
                                          v-model="selectedLanguage"
                                          :options="languageOptions"
                                          label="语言"
                                          placeholder="选择语言"
                                          icon="el-icon-document"
                                          @change="handleLanguageChange"
                                          class="selector-item"
                                      />
                                    </div>

                                    <!-- 创作按钮 -->
                                    <div class="create-btn-container">
                                      <el-button
                                          type="primary"
                                          icon="el-icon-document-checked"
                                          @click="handleFileSubmit"
                                          :loading="isFileGenerating"
                                          :disabled="fileList.length === 0 || isFileGenerating"
                                          size="medium"
                                          class="create-btn"
                                      >立即创作
                                      </el-button>
                                    </div>
                                  </div>
                                </div>
                              </transition>
                            </div>
                          </transition>
                        </div>
                      </transition>
                    </template>
                    <template v-else>
                      <!-- 全屏大纲编辑器 -->
                      <div v-if="showOutlineEditor">
                        <!-- 修改：添加 @request-next-step 监听 -->
                        <outline-editor
                            :markdown-content="generatedMarkdownContent"
                            @close="handleEditorCloseRequest"
                            @outline-updated="handleOutlineUpdate"
                            @title-updated="handleTitleUpdate"
                            @request-next-step="handleGoNextFromEditor"
                        />
                      </div>
                    </template>

                    <!-- 现代化终止生成按钮 -->
                    <transition name="fade-scale">
                      <div v-if="isAnyGenerating && !showOutlineEditor" class="modern-abort-container">
                        <div class="abort-card">
                          <div class="abort-content">
                            <div class="generating-status">
                              <div class="status-icon">
                                <i class="el-icon-loading"></i>
                              </div>
                              <div class="status-text">
                                <span class="status-title">AI正在创作中...</span>
                                <span class="status-subtitle">请稍候，大纲内容即将呈现</span>
                              </div>
                            </div>
                            <el-button
                                @click="abortGeneration()"
                                class="modern-abort-btn"
                                size="medium"
                                type="danger"
                                plain
                            >
                              <i class="el-icon-close"></i>
                              <span>停止创作</span>
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </transition>
                    <!-- 原始内容结束 -->
                  </div>
                </transition>
              </el-tab-pane>

              <!-- 历史记录 标签页 -->
              <el-tab-pane label="历史记录" name="history">
                <!-- 添加过渡动画包装器 -->
                <transition name="tab-content-fade" mode="out-in" appear>
                  <div key="history-content" class="tab-content-wrapper">
                    <div class="history-container">
                      <!-- 骨架屏加载状态 -->
                      <template v-if="pptHistoryLoading">
                        <div class="history-grid">
                          <div v-for="n in skeletonConfig.historySkeletonCount" :key="`skeleton-${n}`"
                               class="history-item-wrapper"
                          >
                            <div class="history-card">
                              <div class="history-card-cover">
                                <el-skeleton :rows="1" animated class="skeleton-full-container">
                                  <template slot="template">
                                    <el-skeleton-item variant="image"
                                                      style="width: 100%; height: 100%; display: block;"
                                    />
                                  </template>
                                </el-skeleton>
                              </div>
                              <div class="history-card-info">
                                <!-- PPT名称骨架屏 -->
                                <div class="history-name-section">
                                  <el-skeleton :rows="1" animated>
                                    <template slot="template">
                                      <el-skeleton-item variant="text" style="width: 80%; height: 20px;"/>
                                    </template>
                                  </el-skeleton>
                                </div>
                                <!-- 时间骨架屏 -->
                                <div class="history-meta">
                                  <el-skeleton :rows="1" animated>
                                    <template slot="template">
                                      <el-skeleton-item variant="text" style="width: 60%; height: 16px;"/>
                                    </template>
                                  </el-skeleton>
                                </div>
                                <!-- 操作按钮骨架屏 -->
                                <div class="history-item-actions">
                                  <el-skeleton :rows="1" animated>
                                    <template slot="template">
                                      <el-skeleton-item variant="button"
                                                        style="width: 60px; height: 28px; margin-right: 8px;"
                                      />
                                      <el-skeleton-item variant="button" style="width: 60px; height: 28px;"/>
                                    </template>
                                  </el-skeleton>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      <!-- 数据加载完成状态 -->
                      <template v-else-if="pptHistoryList.length > 0">
                        <div class="history-grid">
                          <!-- 修改 v-for 循环，添加 wrapper -->
                          <div v-for="item in pptHistoryList" :key="item.id" class="history-item-wrapper">
                            <div class="history-card">
                              <div class="history-card-cover">
                                <el-image :src="item.coverUrl + '?token=' + aiPptToken" fit="cover">
                                  <div slot="placeholder" class="image-slot">
                                    <el-skeleton :rows="1" animated class="skeleton-full-container">
                                      <template slot="template">
                                        <el-skeleton-item variant="image"
                                                          style="width: 100%; height: 100%; display: block;"
                                        />
                                      </template>
                                    </el-skeleton>
                                  </div>
                                  <div slot="error" class="image-slot">
                                    <el-skeleton :rows="1" animated class="skeleton-full-container">
                                      <template slot="template">
                                        <el-skeleton-item variant="image"
                                                          style="width: 100%; height: 100%; display: block;"
                                        />
                                      </template>
                                    </el-skeleton>
                                  </div>
                                </el-image>
                              </div>
                              <div class="history-card-info">
                                <!-- PPT名称区域 - 独立一行 -->
                                <div class="history-name-section" :class="{ editing: item.isEditing }">
                              <span
                                  v-if="!item.isEditing"
                                  class="history-name-display"
                                  @click="handleStartEditHistoryName(item)"
                                  :title="item.name || '未命名PPT'"
                              >
                                {{ item.name || '未命名PPT' }}
                              </span>
                                  <el-input
                                      v-else
                                      v-model="item.editingName"
                                      class="history-name-input"
                                      size="mini"
                                      maxlength="100"
                                      show-word-limit
                                      @blur="handleBlurEditHistoryName(item)"
                                      @keydown.enter.native.prevent=""
                                      ref="historyNameInput"
                                  />
                                </div>
                                <!-- 时间区域 - 独立一行 -->
                                <div class="history-meta">
                                  <span><i class="el-icon-time"></i> {{ item.createTime }}</span>
                                </div>
                                <!-- 操作按钮移到卡片外部 -->
                                <div class="history-item-actions">
                                  <el-button type="primary" size="mini" icon="el-icon-edit" plain
                                             @click="handleOpenHistoryPpt(item)"
                                  >查看
                                  </el-button>
                                  <el-button type="danger" size="mini" icon="el-icon-delete" plain
                                             @click="handleDeleteHistoryPpt(item)"
                                  >删除
                                  </el-button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="pagination-container">
                          <el-pagination background layout="prev, pager, next" :total="pptHistoryTotal"
                                         :page-size="pptHistoryQueryParams.size"
                                         :current-page.sync="pptHistoryQueryParams.page"
                                         @current-change="handleHistoryPageChange"
                          >
                          </el-pagination>
                        </div>
                      </template>
                      <!-- 空状态 -->
                      <template v-else>
                        <el-empty description="暂无历史记录"></el-empty>
                      </template>
                    </div>
                  </div>
                </transition>
              </el-tab-pane>

              <!-- 新增：自定义模板 标签页 -->
              <el-tab-pane label="自定义模板" name="customTemplate">
                <!-- 添加过渡动画包装器 -->
                <transition name="tab-content-fade" mode="out-in" appear>
                  <div key="custom-template-content" class="tab-content-wrapper">
                    <div class="history-container">
                      <!-- 骨架屏加载状态 -->
                      <template v-if="customTemplateLoading">
                        <div class="history-grid">
                          <!-- 创建模板卡片骨架屏 -->
                          <div class="history-item-wrapper">
                            <div class="history-card">
                              <div class="history-card-cover">
                                <el-skeleton :rows="1" animated>
                                  <template slot="template">
                                    <el-skeleton-item variant="image" style="width: 100%; height: 160px;"/>
                                  </template>
                                </el-skeleton>
                              </div>
                              <div class="history-card-info">
                                <div class="history-meta">
                                  <el-skeleton :rows="1" animated>
                                    <template slot="template">
                                      <el-skeleton-item variant="text" style="width: 60%;"/>
                                    </template>
                                  </el-skeleton>
                                </div>
                                <div class="history-item-actions">
                                  <el-skeleton :rows="1" animated>
                                    <template slot="template">
                                      <el-skeleton-item variant="button" style="width: 60px; height: 28px;"/>
                                    </template>
                                  </el-skeleton>
                                </div>
                              </div>
                            </div>
                          </div>
                          <!-- 自定义模板列表骨架屏 -->
                          <div v-for="n in 7" :key="`template-skeleton-${n}`" class="history-item-wrapper">
                            <div class="history-card">
                              <div class="history-card-cover">
                                <el-skeleton :rows="1" animated class="skeleton-full-container">
                                  <template slot="template">
                                    <el-skeleton-item variant="image"
                                                      style="width: 100%; height: 100%; display: block;"
                                    />
                                  </template>
                                </el-skeleton>
                              </div>
                              <div class="history-card-info">
                                <div class="history-meta">
                                  <div class="template-name-time-container">
                                    <!-- 模板名称骨架屏 -->
                                    <div class="template-name-section">
                                      <el-skeleton :rows="1" animated>
                                        <template slot="template">
                                          <el-skeleton-item variant="text" style="width: 80%;"/>
                                        </template>
                                      </el-skeleton>
                                    </div>
                                    <!-- 时间骨架屏 -->
                                    <div class="template-time-section">
                                      <el-skeleton :rows="1" animated>
                                        <template slot="template">
                                          <el-skeleton-item variant="text" style="width: 60%;"/>
                                        </template>
                                      </el-skeleton>
                                    </div>
                                  </div>
                                </div>
                                <!-- 操作按钮骨架屏 -->
                                <div class="history-item-actions">
                                  <el-skeleton :rows="1" animated>
                                    <template slot="template">
                                      <el-skeleton-item variant="button"
                                                        style="width: 32px; height: 28px; margin-right: 8px;"
                                      />
                                      <el-skeleton-item variant="button"
                                                        style="width: 32px; height: 28px; margin-right: 8px;"
                                      />
                                      <el-skeleton-item variant="button" style="width: 32px; height: 28px;"/>
                                    </template>
                                  </el-skeleton>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      <!-- 数据加载完成状态 -->
                      <template v-else-if="customTemplateList.length > 0 || customTemplateParams.pageNum === 1">
                        <div class="history-grid">
                          <!-- 创建模板卡片 -->
                          <div class="history-item-wrapper">
                            <div class="history-card" @click="handleCreateNewTemplate">
                              <div class="history-card-cover create-template-cover">
                                <div class="create-template-content">
                                  <i class="el-icon-plus"></i>
                                  <span>创建新模板</span>
                                </div>
                              </div>
                              <div class="history-card-info">
                                <div class="history-meta">
                                  <span><i class="el-icon-time"></i> {{ formatTime(new Date()) }}</span>
                                </div>
                                <!-- 操作按钮 -->
                                <div class="history-item-actions">
                                  <el-tooltip content="创建新模板" placement="bottom" effect="light">
                                    <el-button type="primary" size="mini" icon="el-icon-plus" plain>
                                    </el-button>
                                  </el-tooltip>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 自定义模板列表卡片 -->
                          <div v-for="item in customTemplateList" :key="item.id" class="history-item-wrapper">
                            <div class="history-card">
                              <div class="history-card-cover">
                                <el-image :src="item.coverUrl + '?token=' + aiPptToken" fit="cover">
                                  <div slot="placeholder" class="image-slot">
                                    <el-skeleton :rows="1" animated class="skeleton-full-container">
                                      <template slot="template">
                                        <el-skeleton-item variant="image"
                                                          style="width: 100%; height: 100%; display: block;"
                                        />
                                      </template>
                                    </el-skeleton>
                                  </div>
                                  <div slot="error" class="image-slot">
                                    <el-skeleton :rows="1" animated class="skeleton-full-container">
                                      <template slot="template">
                                        <el-skeleton-item variant="image"
                                                          style="width: 100%; height: 100%; display: block;"
                                        />
                                      </template>
                                    </el-skeleton>
                                  </div>
                                </el-image>
                              </div>
                              <div class="history-card-info">
                                <div class="history-meta">
                                  <div class="template-name-time-container">
                                    <!-- 模板名称区域 -->
                                    <div class="template-name-section" :class="{ editing: item.isEditing }">
                                  <span
                                      v-if="!item.isEditing"
                                      class="template-name-display"
                                      @click="handleStartEditName(item)"
                                      :title="item.name || '未命名'"
                                  >
                                    {{ item.name || '未命名' }}
                                  </span>
                                      <el-input
                                          v-else
                                          v-model="item.editingName"
                                          class="template-name-input"
                                          size="mini"
                                          maxlength="100"
                                          show-word-limit
                                          @blur="handleBlurEditName(item)"
                                          @keydown.enter.native.prevent=""
                                          ref="nameInput"
                                      />
                                    </div>
                                    <!-- 时间区域 -->
                                    <div class="template-time-section">
                                  <span class="template-time">
                                    <i class="el-icon-time"></i> {{ item.createTime }}
                                  </span>
                                    </div>
                                  </div>
                                </div>
                                <!-- 操作按钮 -->
                                <div class="history-item-actions">
                                  <el-tooltip content="编辑" placement="bottom" effect="light">
                                    <el-button type="primary" size="mini" icon="el-icon-edit" plain
                                               @click="handleEditTemplate(item)"
                                    >
                                    </el-button>
                                  </el-tooltip>
                                  <el-tooltip content="标注" placement="bottom" effect="light">
                                    <el-button type="warning" size="mini" icon="el-icon-s-flag" plain
                                               @click="handleOpenMarking(item)"
                                    >
                                    </el-button>
                                  </el-tooltip>

                                  <!--                              <el-tooltip content="下载" placement="bottom" effect="light">-->
                                  <!--                                <el-button type="success" size="mini" icon="el-icon-download" plain-->
                                  <!--                                           @click="handleDownloadTemplate(item)"-->
                                  <!--                                >-->
                                  <!--                                </el-button>-->
                                  <!--                              </el-tooltip>-->

                                  <el-tooltip content="删除" placement="bottom" effect="light">
                                    <el-button type="danger" size="mini" icon="el-icon-delete" plain
                                               @click="handleDeleteCustomTemplate(item)"
                                    >
                                    </el-button>
                                  </el-tooltip>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination-container">
                          <el-pagination background layout="prev, pager, next" :total="customTemplateTotal"
                                         :page-size="customTemplateParams.pageSize"
                                         :current-page.sync="customTemplateParams.pageNum"
                                         @current-change="handleCustomTemplatePageChange"
                          >
                          </el-pagination>
                        </div>
                      </template>
                      <!-- 空状态 -->
                      <template v-else>
                        <el-empty description="暂无自定义模板"></el-empty>
                      </template>
                    </div>
                  </div>
                </transition>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 步骤2: 模板设置 -->
          <div v-show="activeStep === 1" class="step-container template-step-container">
            <!-- 模板选择器组件 - 全屏显示 -->
            <template-selector
                :templates="themeOptions"
                :selected-template-id="form.templateId"
                :token="aiPptToken"
                :loading="templateLoading"
                :category="selectedCategory"
                :style-filter="selectedStyle"
                @template-select="handleTemplateSelect"
                @template-type-change="handleTemplateTypeChange"
                @refresh-templates="handleGetAiPptRandomTemplates"
                @category-change="handleCategoryChange"
                @style-change="handleStyleChange"
                @search="handleTemplateSearch"
                @clear-filters="handleClearFilters"
            />
          </div>

          <!-- 步骤3: PPT生成 -->
          <div v-show="activeStep === 2" class="step-container">
            <div class="step-header">
              <h2>生成您的PPT</h2>
              <p class="step-description">点击"生成PPT"按钮开始创建</p>
            </div>

            <div class="generation-container">
              <div class="beautiful-empty-state">
                <!-- 动画背景装饰 -->
                <div class="floating-elements">
                  <div class="floating-element element-1"></div>
                  <div class="floating-element element-2"></div>
                  <div class="floating-element element-3"></div>
                  <div class="floating-element element-4"></div>
                  <div class="floating-element element-5"></div>
                </div>

                <!-- 主要内容区域 -->
                <div class="main-content">
                  <!-- 动画图标 -->
                  <div class="animated-icon-container">
                    <div class="icon-background"></div>
                    <i class="el-icon-document animated-icon"></i>
                    <div class="icon-pulse"></div>
                  </div>

                  <!-- 标题和描述 -->
                  <div class="content-text">
                    <h3 class="main-title">准备生成您的专属PPT</h3>
                    <p class="description">AI将根据您的大纲内容，智能生成精美的演示文稿</p>
                  </div>

                  <!-- 特性展示 -->
                  <div class="features-grid">
                    <div class="feature-item">
                      <div class="feature-icon">
                        <i class="el-icon-magic-stick"></i>
                      </div>
                      <span>AI智能生成</span>
                    </div>
                    <div class="feature-item">
                      <div class="feature-icon">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                      <span>精美模板</span>
                    </div>
                    <div class="feature-item">
                      <div class="feature-icon">
                        <i class="el-icon-timer"></i>
                      </div>
                      <span>快速生成</span>
                    </div>
                  </div>

                  <!-- 提示文字 -->
                  <div class="action-hint">
                    <div class="hint-icon">
                      <i class="el-icon-bottom"></i>
                    </div>
                    <p>点击下方"生成PPT"按钮开始创建</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 现代化步骤控制按钮 -->
          <div class="modern-step-controls" v-if="activeContentTab === 'creation' && !isAnyGenerating">
            <div class="step-controls-container">
              <div class="controls-left">
                <el-button
                    @click="goPrevStep"
                    :disabled="isSubmittingPpt || activeStep === 0"
                    class="step-button prev-button"
                    size="large"
                >
                  <i class="el-icon-arrow-left"></i>
                  上一步
                </el-button>
              </div>

              <div class="controls-center">
                <div class="step-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: ((activeStep + 1) / 3 * 100) + '%' }"></div>
                  </div>

                </div>
              </div>

              <div class="controls-right">
                <template v-if="activeStep !== 2">
                  <el-button
                      type="primary"
                      @click="goNextStep"
                      :disabled="activeStep === 1 && !form.templateId"
                      class="step-button next-button"
                      size="large"
                  >
                    下一步
                    <i class="el-icon-arrow-right"></i>
                  </el-button>
                </template>
                <template v-else>
                  <el-button
                      type="primary"
                      @click="handleSubmit"
                      :loading="btnLoad"
                      :disabled="isSubmittingPpt || disableConfirmDownload"
                      class="step-button generate-button"
                      size="large"
                  >
                    <i class="el-icon-magic-stick" v-if="!btnLoad"></i>
                    <span v-if="!btnLoad">生成PPT</span>
                    <span v-else>生成中...</span>
                  </el-button>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- 模板创建视图 -->
      <div v-if="showTemplateCreationView" class="templateCreationView">
        <template-creation
            @close="handleShowTemplateCreationView"
            @edit-template="handleEditTemplate"
            :api-key="apiKey"
            :ppt-token="aiPptToken"
        />
      </div>
      <!--  aIPpt编辑页面    -->
      <div v-if="shouldShowPPTEditArea" class="editor-wrapper">
        <aiPptCustomEditor
            :ppt-id="currentPptId"
            :template-id="currentTemplateId"
            :token="aiPptToken"
            :animation="isAnimation"
            :editor-url="editUrl"
            :loading-text="pptEditLoadingText"
            @editor-ready="handleEditorReady"
            @editor-error="handleEditorError"
            @token-invalid="handleTokenInvalid"
            @editor-message="handleEditorMessage"
            @close="handleEditorCloseRequest"
        />
      </div>

      <!-- 标注视图 -->
      <marking
          v-if="showMarkingView"
          :url="markingUrl"
          :visible="showMarkingView"
          @close="showMarkingView = false"
      >
      </marking>
    </div>
  </div>
</template>

<script>
import {
  aiPptDelete,
  aiPptDelTemplateId,
  aiPptGeneratePptx,
  aiPptListPptx,
  createApiToken,
  createTask,
  getAiPptRandomTemplates,
  getAiPptTemplates, getChatModel,
  getOutlineLang, getPptEditData,
  getSceneOptions,
  getUidDetail,
  downloadTemplate, getCasEnter, generateContentApi, cancelGenerationApi, templatesNameUpdateApi, updatePptxAttrApi
} from '@/api/intellectSmartPpt/intellectSmartPpt.js'
import formatTip from '@/views/intellectSmartPpt/component2/formatTip.vue'
import Cookies from 'js-cookie'
import aiPptCustomEditor from '@/views/intellectSmartPpt/aiPPT/components/Editor/index.vue'
import OutlineEditor from '@/views/intellectSmartPpt/aiPPT/components/OutlineEditor/index.vue'
import { getToken } from '@/utils/auth' // 新增：导入大纲编辑器组件
import TemplateCreation from '@/views/intellectSmartPpt/aiPPT/components/TemplateCreation/index.vue'
import TemplateSelector from '@/views/intellectSmartPpt/aiPPT/components/TemplateSelector/index.vue' // 导入新的模板选择器组件
import Marking from '@/views/intellectSmartPpt/aiPPT/components/Marking/index.vue'
import SceneThemeSelector from '@/views/intellectSmartPpt/aiPPT/components/SceneThemeSelector.vue' // 导入场景主题选择器组件
import LanguageSelector from '@/views/intellectSmartPpt/aiPPT/components/LanguageSelector.vue' // 导入语言选择器组件
import ModelSelector from '@/views/intellectSmartPpt/aiPPT/components/ModelSelector.vue' // 导入模型选择器组件
import CardSelector from '@/views/intellectSmartPpt/aiPPT/components/CardSelector.vue' // 导入卡片式选择器组件
import WebSearchCard from '@/views/intellectSmartPpt/aiPPT/components/WebSearchCard.vue' // 导入网络搜索卡片组件
import Vue from 'vue'
import StreamingMarkdown from '@/components/MarkdownRenderer/StreamingMarkdown.vue'
import { getDicts } from '@/api/system/dict/data'
import { createNotification, createNotificationTopLeft, CreateNotificationType } from '@/utils/common'

export default {
  name: 'CreatePPT',
  components: {
    StreamingMarkdown,
    formatTip,
    aiPptCustomEditor,
    OutlineEditor,
    TemplateCreation,
    TemplateSelector, // 注册新的模板选择器组件
    Marking,
    SceneThemeSelector,
    LanguageSelector,
    ModelSelector,
    CardSelector,
    WebSearchCard
  }, // 注册选择器组件
  data() {
    return {
      // API密钥相关
      /** API密钥验证状态 */
      hasValidApiKey: false,
      currentPptId: '', // 生成PPT后获得的ID，保持不变
      currentTemplateId: '', // 选择编辑的模板id
      aiPptToken: '',// aiPpt token
      /** API key密钥值  用于本系统的接口请求*/
      apiKey: '',
      apiKeyFatherProvider: Vue.observable({ // 为子孙组件传递参数
        apiKey: ''
      }),
      editUrl: '', //编辑器url
      pptEditLoadingText: '加载中...', // 加载Loging的文字
      /** 内容 uid */
      uid: '',
      /** 唯一标识 用于区别aiPpt中数据  */
      userName: '', // 唯一标识 用于区别aiPpt中数据
      // 上传相关配置
      /** 文件上传地址 */
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/file/upload',
      /** 上传请求附加数据 */
      uploadData: { modeltype: 'smartPPT' },
      isAnimation: false, // 是否显示编辑动画，保持不变
      elTabPaneItem: {
        label: 'PPT创作',
        name: 'creation'
      },
      // --- 状态隔离：Start ---
      // 通用状态
      /** 当前激活的生成类型: topic / text / file */
      requestType: 'topic', // 保持，用于切换UI
      /** 大纲编辑器正在编辑的类型: 'topic' 或 'text' */
      editingType: '',

      // Topic（一句话生成）相关状态
      topicPrompt: '', // 输入框内容
      topicMessages: [], // 消息历史
      topicTaskId: '', // 任务ID
      topicStreamContent: '', // 流式内容
      isTopicGenerating: false, // 是否正在生成
      isTopicGenerateComplete: false, // 是否生成完成
      topicFinalMarkdown: '', // 最终确认的Markdown

      // Text（文本生成）相关状态
      textContent: '', // 输入框内容
      textMessages: [], // 消息历史
      textTaskId: '', // 任务ID
      textStreamContent: '', // 流式内容
      isTextGenerating: false, // 是否正在生成
      isTextGenerateComplete: false, // 是否生成完成
      textFinalMarkdown: '', // 最终确认的Markdown

      // --- 新增：选择器显示状态控制 ---
      showSceneSelector: false,
      showLanguageSelector: false,
      showModelSelector: false,
      // 用于新卡片选择器的值
      selectedDemoSceneValue: '',
      selectedChatModelValue: '',
      // --- 选择器状态控制结束 ---

      // --- 新增：一句话生成悬停建议相关状态 ---
      /** 控制一句话建议浮窗的显示 */
      showTopicSuggestions: false,
      /** 建议框动态样式 - Fixed定位 */
      suggestionBoxStyle: {},
      /** 一句话建议列表 */
      topicSuggestionList: [
        {
          dictCode: '1',
          dictLabel: '未来城市的构想',
          dictValue: '未来城市的构想'
        },
        {
          dictCode: '2',
          dictLabel: '梦境解析与现实影响',
          dictValue: '梦境解析与现实影响'
        },
        {
          dictCode: '3',
          dictLabel: '古代神话与现代心理学的交汇',
          dictValue: '古代神话与现代心理学的交汇'
        },
        {
          dictCode: '4',
          dictLabel: '人工智能的应用与挑战',
          dictValue: '人工智能的应用与挑战'
        },
        {
          dictCode: '5',
          dictLabel: '领导力与团队合作',
          dictValue: '领导力与团队合作'
        },
        {
          dictCode: '6',
          dictLabel: '物联网（IoT）',
          dictValue: '物联网（IoT）'
        },
        {
          dictCode: '7',
          dictLabel: '可持续发展与环保技术',
          dictValue: '可持续发展与环保技术'
        },
        {
          dictCode: '8',
          dictLabel: '数字化转型策略',
          dictValue: '数字化转型策略'
        }
      ],
      /** 一句话建议的悬停计时器 */
      topicHoverTimeout: null,
      /** 跟踪鼠标是否在输入框区域内 */
      isMouseInInputArea: false,
      /** 跟踪鼠标是否在提示框区域内 */
      isMouseInSuggestionBox: false,
      /** 延迟隐藏计时器 */
      hideDelayTimeout: null,
      // --- 建议状态结束 ---

      // --- 新增：主题设置相关状态 ---
      /** 是否显示主题选择器 */
      showThemeSelector: false,
      /** 当前选中的主题 */
      currentTheme: 'default',
      /** 主题选项列表 */
      backgroundThemeOptions: [
        {
          id: 'default',
          name: '星河幻境',
          gradient: 'linear-gradient(135deg, rgba(26, 206, 233, 0.60) 0%, rgba(64, 160, 222, 0.70) 10%, rgba(100, 100, 216, 0.70) 20%, rgba(123, 35, 211, 0.70) 30%, rgba(150, 20, 205, 0.60) 40%, rgba(176, 10, 199, 0.50) 50%, rgba(168, 22, 130, 0.60) 60%, rgba(160, 33, 52, 0.40) 70%, rgba(80, 90, 190, 0.60) 85%, rgba(5, 135, 250, 0.80) 100%)'
        },
        {
          id: 'white',
          name: '纯白梦境',
          gradient: 'linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(245, 245, 245, 0.95) 25%, rgba(240, 240, 240, 0.9) 50%, rgba(235, 235, 235, 0.85) 75%, rgba(230, 230, 230, 0.8) 100%)'
        },
        {
          id: 'dark',
          name: '暗夜星尘',
          gradient: 'linear-gradient(135deg, rgba(20, 20, 20, 1) 0%, rgba(30, 30, 30, 0.95) 25%, rgba(40, 40, 40, 0.9) 50%, rgba(60, 60, 60, 0.85) 75%, rgba(80, 80, 80, 0.8) 100%)'
        },
        {
          id: 'black',
          name: '极夜深渊',
          gradient: 'linear-gradient(135deg, rgba(0, 0, 0, 1) 0%, rgba(5, 5, 5, 0.98) 20%, rgba(10, 10, 10, 0.96) 40%, rgba(15, 15, 15, 0.94) 60%, rgba(20, 20, 20, 0.92) 80%, rgba(25, 25, 25, 0.9) 100%)'
        },
        {
          id: 'ocean',
          name: '深海蓝调',
          gradient: 'linear-gradient(135deg, rgba(13, 71, 161, 0.65) 0%, rgba(25, 118, 210, 0.75) 15%, rgba(33, 150, 243, 0.70) 30%, rgba(3, 169, 244, 0.65) 45%, rgba(0, 188, 212, 0.60) 60%, rgba(0, 150, 136, 0.70) 75%, rgba(76, 175, 80, 0.65) 90%, rgba(139, 195, 74, 0.75) 100%)'
        },
        {
          id: 'sunset',
          name: '晚霞余晖',
          gradient: 'linear-gradient(135deg, rgba(255, 87, 34, 0.65) 0%, rgba(255, 152, 0, 0.70) 15%, rgba(255, 193, 7, 0.60) 30%, rgba(255, 235, 59, 0.55) 45%, rgba(255, 111, 97, 0.65) 60%, rgba(240, 98, 146, 0.70) 75%, rgba(186, 104, 200, 0.65) 90%, rgba(149, 117, 205, 0.70) 100%)'
        },
        {
          id: 'forest',
          name: '翡翠森林',
          gradient: 'linear-gradient(135deg, rgba(27, 94, 32, 0.70) 0%, rgba(46, 125, 50, 0.75) 15%, rgba(67, 160, 71, 0.65) 30%, rgba(102, 187, 106, 0.60) 45%, rgba(129, 199, 132, 0.65) 60%, rgba(165, 214, 167, 0.55) 75%, rgba(200, 230, 201, 0.50) 90%, rgba(232, 245, 233, 0.45) 100%)'
        },
        {
          id: 'aurora',
          name: '极光之夜',
          gradient: 'linear-gradient(135deg, rgba(74, 20, 140, 0.75) 0%, rgba(106, 27, 154, 0.70) 15%, rgba(142, 36, 170, 0.65) 30%, rgba(171, 71, 188, 0.60) 45%, rgba(186, 104, 200, 0.65) 60%, rgba(206, 147, 216, 0.55) 75%, rgba(225, 190, 231, 0.50) 90%, rgba(243, 229, 245, 0.45) 100%)'
        },
        {
          id: 'sakura',
          name: '樱花飞舞',
          gradient: 'linear-gradient(135deg, rgba(255, 182, 193, 0.65) 0%, rgba(255, 192, 203, 0.70) 15%, rgba(255, 160, 180, 0.60) 30%, rgba(255, 105, 135, 0.65) 45%, rgba(255, 20, 147, 0.55) 60%, rgba(219, 112, 147, 0.60) 75%, rgba(199, 21, 133, 0.65) 90%, rgba(139, 69, 19, 0.45) 100%)'
        },
        {
          id: 'golden',
          name: '黄金时代',
          gradient: 'linear-gradient(135deg, rgba(255, 215, 0, 0.70) 0%, rgba(255, 193, 7, 0.75) 15%, rgba(255, 152, 0, 0.65) 30%, rgba(255, 111, 0, 0.60) 45%, rgba(205, 133, 63, 0.65) 60%, rgba(184, 134, 11, 0.70) 75%, rgba(139, 69, 19, 0.60) 90%, rgba(101, 67, 33, 0.55) 100%)'
        },
        {
          id: 'ice',
          name: '冰雪奇缘',
          gradient: 'linear-gradient(135deg, rgba(240, 248, 255, 0.60) 0%, rgba(176, 224, 230, 0.65) 15%, rgba(135, 206, 235, 0.70) 30%, rgba(70, 130, 180, 0.65) 45%, rgba(100, 149, 237, 0.60) 60%, rgba(123, 104, 238, 0.65) 75%, rgba(147, 112, 219, 0.55) 90%, rgba(186, 85, 211, 0.50) 100%)'
        },
        {
          id: 'volcano',
          name: '火山熔岩',
          gradient: 'linear-gradient(135deg, rgba(139, 0, 0, 0.75) 0%, rgba(178, 34, 34, 0.70) 15%, rgba(220, 20, 60, 0.65) 30%, rgba(255, 69, 0, 0.70) 45%, rgba(255, 140, 0, 0.60) 60%, rgba(255, 165, 0, 0.55) 75%, rgba(255, 215, 0, 0.50) 90%, rgba(255, 255, 224, 0.45) 100%)'
        },
        {
          id: 'midnight',
          name: '午夜星空',
          gradient: 'linear-gradient(135deg, rgba(25, 25, 112, 0.80) 0%, rgba(72, 61, 139, 0.75) 15%, rgba(106, 90, 205, 0.70) 30%, rgba(123, 104, 238, 0.65) 45%, rgba(147, 112, 219, 0.60) 60%, rgba(138, 43, 226, 0.65) 75%, rgba(75, 0, 130, 0.70) 90%, rgba(25, 25, 112, 0.75) 100%)'
        },
        {
          id: 'dark-forest',
          name: '暗夜森林',
          gradient: 'linear-gradient(135deg, rgba(0, 20, 0, 0.85) 0%, rgba(10, 40, 10, 0.80) 15%, rgba(20, 60, 20, 0.75) 30%, rgba(15, 80, 15, 0.70) 45%, rgba(25, 100, 25, 0.65) 60%, rgba(35, 120, 35, 0.60) 75%, rgba(45, 140, 45, 0.55) 90%, rgba(55, 160, 55, 0.50) 100%)'
        },
        {
          id: 'deep-ocean',
          name: '深海深渊',
          gradient: 'linear-gradient(135deg, rgba(0, 10, 30, 0.90) 0%, rgba(5, 20, 50, 0.85) 15%, rgba(10, 30, 70, 0.80) 30%, rgba(15, 40, 90, 0.75) 45%, rgba(20, 50, 110, 0.70) 60%, rgba(25, 60, 130, 0.65) 75%, rgba(30, 70, 150, 0.60) 90%, rgba(35, 80, 170, 0.55) 100%)'
        },
        {
          id: 'shadow-purple',
          name: '暗影紫魅',
          gradient: 'linear-gradient(135deg, rgba(20, 0, 40, 0.90) 0%, rgba(40, 10, 60, 0.85) 15%, rgba(60, 20, 80, 0.80) 30%, rgba(80, 30, 100, 0.75) 45%, rgba(100, 40, 120, 0.70) 60%, rgba(120, 50, 140, 0.65) 75%, rgba(140, 60, 160, 0.60) 90%, rgba(160, 70, 180, 0.55) 100%)'
        },
        {
          id: 'carbon-black',
          name: '碳黑科技',
          gradient: 'linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(20, 20, 25, 0.90) 15%, rgba(30, 30, 40, 0.85) 30%, rgba(40, 40, 55, 0.80) 45%, rgba(50, 50, 70, 0.75) 60%, rgba(60, 60, 85, 0.70) 75%, rgba(70, 70, 100, 0.65) 90%, rgba(80, 80, 115, 0.60) 100%)'
        },
        {
          id: 'bloodmoon',
          name: '血月之夜',
          gradient: 'linear-gradient(135deg, rgba(40, 0, 0, 0.90) 0%, rgba(60, 10, 10, 0.85) 15%, rgba(80, 20, 20, 0.80) 30%, rgba(100, 30, 30, 0.75) 45%, rgba(120, 40, 40, 0.70) 60%, rgba(140, 50, 50, 0.65) 75%, rgba(160, 60, 60, 0.60) 90%, rgba(180, 70, 70, 0.55) 100%)'
        },
        {
          id: 'mystic-night',
          name: '神秘夜幕',
          gradient: 'linear-gradient(135deg, rgba(15, 15, 35, 0.90) 0%, rgba(25, 25, 55, 0.85) 15%, rgba(35, 35, 75, 0.80) 30%, rgba(45, 45, 95, 0.75) 45%, rgba(55, 55, 115, 0.70) 60%, rgba(65, 65, 135, 0.65) 75%, rgba(75, 75, 155, 0.60) 90%, rgba(85, 85, 175, 0.55) 100%)'
        },
        {
          id: 'emerald-dark',
          name: '翡翠暗影',
          gradient: 'linear-gradient(135deg, rgba(0, 30, 20, 0.90) 0%, rgba(10, 50, 35, 0.85) 15%, rgba(20, 70, 50, 0.80) 30%, rgba(30, 90, 65, 0.75) 45%, rgba(40, 110, 80, 0.70) 60%, rgba(50, 130, 95, 0.65) 75%, rgba(60, 150, 110, 0.60) 90%, rgba(70, 170, 125, 0.55) 100%)'
        }
      ],
      // --- 主题设置状态结束 ---

      // --- 新增：PPT 历史记录相关状态 ---
      /** 历史记录列表 */
      pptHistoryList: [],
      /** 历史记录加载状态 */
      pptHistoryLoading: false,
      /** 历史记录总条数 */
      pptHistoryTotal: 0,
      /** 历史记录查询参数 */
      pptHistoryQueryParams: {
        page: 1,
        size: 9
      },
      /** 骨架屏配置 */
      skeletonConfig: {
        historySkeletonCount: 8, // 历史记录骨架屏数量
        templateSkeletonCount: 7, // 自定义模板骨架屏数量（除去创建模板卡片）
        imageHeight: '160px', // 卡片图片高度
        templateImageHeight: '200px' // 模板选择对话框图片高度
      },
      // --- 历史记录状态结束 ---

      // --- 新增：第一步内容区域活动标签页 ---
      /** 活动内容标签页 (creation: PPT创作, history: 历史记录) */
      activeContentTab: 'creation',
      // --- 活动标签页结束 ---

      // 文件上传状态 (保持不变)
      fileId: '',
      fileList: [],
      // --- 状态隔离：End ---

      // --- 文件上传生成状态 ---
      /** 文件上传流程的消息历史 */
      fileMessages: [],
      /** 文件上传成功后创建的任务ID */
      fileTaskId: '',
      /** 标记文件内容生成大纲的过程是否在进行中 */
      isFileGenerating: false,
      /** 标记文件大纲生成是否完成 */
      isFileGenerateComplete: false,
      /** 文件生成并编辑后的最终 Markdown */
      fileFinalMarkdown: '',
      /** 文件大纲生成时的流式显示 */
      fileStreamContent: '',
      // --- 统一的生成控制 ---
      /** 全局生成中止控制器 - 统一管理所有类型的生成中止 */
      globalAbortController: null,
      requestId: null,
      /** 标记当前生成是否被用户主动中止 */
      isGenerationAborted: false,
      /** 上传的文件名 */
      // uploadedFileName: '', // 不再需要，从 selectedFile 或 fileList 获取
      // --- 文件上传状态结束 ---

      // 是否显示上解析markdown 骨架光标
      showStreamCursor: true,
      /** 表单数据对象 */
      form: {
        templateId: '' // 初始化templateId
      },

      // 步骤和状态控制
      /** 当前活动步骤 */
      activeStep: 0,
      /** 按钮加载状态 */
      btnLoad: false, // 保持，用于最终提交按钮的loading
      /** 表单验证规则 */ // (留空)

      /** 查询数据对象 */
      queryData: {
        filePath: ''
      },

      /** 主题选项 */
      themeOptions: [],

      // /** 文件列表 */ (已移至状态隔离区)
      // fileList: [],

      /** 生成完成状态 (步骤3用) */
      isGenerateSuccess: false,

      /** 模板选择对话框状态 - 已移除，直接显示模板选择器 */
      /** 模板搜索 */
      templateSearch: '',
      /** 选中的模板 */
      selectedTemplate: null,
      /** 选中的模板ID */
      selectedTemplateId: null,

      // --- 新增：模板类型选择 ---
      /** 当前选择的模板类型 (1: 系统模板, 4: 自定义模板) */
      selectedTemplateType: 1,
      // --- 模板类型选择结束 ---

      // --- 新增：模板筛选相关 ---
      /** 模板加载状态 */
      templateLoading: false,
      /** 选中的分类筛选 */
      selectedCategory: '',
      /** 选中的风格筛选 */
      selectedStyle: '',
      // --- 模板筛选结束 ---

      // --- 新增：模板背景相关 ---
      /** 是否启用模板背景 */
      isTemplateBackgroundActive: false,
      /** 当前模板背景图片URL */
      currentTemplateBackgroundUrl: '',
      // --- 模板背景结束 ---

      // 新增：大纲编辑相关
      /** 新增：存储最终生成的 Markdown 文本 */ // (不再直接使用，通过 finalMarkdown 获取)
      // generatedMarkdown: '',
      /** 控制是否显示大纲编辑器的状态 */
      showOutlineEditor: false,
      /** 新增：存储转换后的 Markdown 字符串 (传递给编辑器的prop) */
      generatedMarkdownContent: '',
      isSubmittingPpt: false, // 确保这行存在
      disableConfirmDownload: false,
      editingMessageInfo: { type: null, index: -1 }, // Bug 2: 重置编辑消息状态
      showTemplateCreationView: false, // 3. 添加状态控制变量

      // --- 自定义模板列表相关 ---
      customTemplateList: [],
      customTemplateTotal: 0,
      customTemplateLoading: false,
      customTemplateParams: {
        pageNum: 1,
        pageSize: 9
      },
      // --- 历史记录相关 ---
      list: [],
      loading: true,

      // --- 标注视图相关 ---
      /** 控制标注视图的显示 */
      showMarkingView: false,
      /** 传递给标注视图的 URL */
      markingUrl: '',

      // --- 新增：演示场景相关 --- //
      /** 当前选中的演示场景 */
      selectedDemoScene: '',
      /** 演示场景选项 */
      demoSceneOptions: [],
      // --- 演示场景相关结束 --- //
      selectedLanguage: '', // 默认选择中文
      languageOptions: [],

      // 模型类型
      selectedChatModel: '',// 默认选择的模型类型
      chatModelOptions: [],

      /** 控制是否启用网络搜索 */
      webSearch: false,

      // 上传单个文件大小校验
      uploadSingleFileMaxSize: 30
    }
  },
  provide() {
    return {
      apiKeyFatherProvider: this.apiKeyFatherProvider // 为子孙组件提供整个响应式对象
    }
  },
  /**
   * 计算当前步骤是否为第一步
   * @returns {boolean} 是否为第一步
   */
  computed: {
    // 判断是否有任何模式正在进行流式输出
    isAnyGenerating() {
      return this.isTopicGenerating || this.isTextGenerating || this.isFileGenerating
    },

    // 容器背景样式
    containerBackgroundStyle() {
      if (this.isTemplateBackgroundActive && this.currentTemplateBackgroundUrl) {
        return {
          backgroundImage: this.currentTemplateBackgroundUrl,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          transition: 'background-image 0.6s ease-in-out'
        }
      }
      return {
        transition: 'background-image 0.6s ease-in-out'
      }
    },

    // 计算属性：当前主题的渐变样式
    currentThemeGradient() {
      const theme = this.backgroundThemeOptions.find(t => t.id === this.currentTheme)
      return theme ? theme.gradient : this.backgroundThemeOptions[0].gradient
    },

    shouldShowPPTEditArea() {
      return (this.currentPptId || this.currentTemplateId) && this.editUrl;
    }
  },
  watch: {},
  /**
   * 组件创建时的生命周期钩子
   * 初始化页面数据和事件监听
   */
  created() {
    // 初始化状态
    this.requestType = 'text'  // 默认使用文本输入模式
    this.textContent = ''
    this.topicPrompt = ''
    this.messages = []
    this.streamContent = ''
    this.isGenerating = false
    this.isGenerateComplete = false
    this.fileId = ''
    this.fileList = []

    // 移除CAS跳转逻辑，已经在路由守卫中处理
    // 直接获取必要的初始数据
    this.getUrlParam()
  },
  mounted() {
    // 添加全局点击事件监听，用于关闭所有打开的选择器
    document.addEventListener('click', this.handleOutsideClick)
    // 监听窗口大小变化，重新计算建议框位置
    window.addEventListener('resize', this.handleWindowResize)

    // 检查当前步骤，如果是模板选择步骤，启用模板背景
    if (this.activeStep === 1) {
      this.enableTemplateBackground()
    }

    // 初始化主题设置
    this.initializeTheme()
  },

  // 添加beforeDestroy钩子函数移除事件监听
  beforeDestroy() {
    // 移除全局点击事件监听
    document.removeEventListener('click', this.handleOutsideClick)
    // 清理窗口大小变化监听器
    window.removeEventListener('resize', this.handleWindowResize)

    // 清理建议框相关计时器
    if (this.topicHoverTimeout) {
      clearTimeout(this.topicHoverTimeout)
    }
    if (this.hideDelayTimeout) {
      clearTimeout(this.hideDelayTimeout)
    }
  },

  methods: {
    /**
     * 根据指定name获取url参数
     */
    handleUrlParam(name) {
      const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`)
      const r = window.location.search.slice(1).match(reg)
      return r ? decodeURIComponent(r[2]) : null
    },

    // --- 新增：选择器相关方法 ---
    /** 切换场景选择器显示状态 */
    toggleSceneSelector() {
      // 先关闭其他选择器
      this.showLanguageSelector = false
      this.showModelSelector = false
      // 切换场景选择器状态
      this.showSceneSelector = !this.showSceneSelector
    },

    /** 切换语言选择器显示状态 */
    toggleLanguageSelector() {
      // 先关闭其他选择器
      this.showSceneSelector = false
      this.showModelSelector = false
      // 切换语言选择器状态
      this.showLanguageSelector = !this.showLanguageSelector
    },

    /** 切换模型选择器显示状态 */
    toggleModelSelector() {
      // 先关闭其他选择器
      this.showSceneSelector = false
      this.showLanguageSelector = false
      // 切换模型选择器状态
      this.showModelSelector = !this.showModelSelector
    },

    /** 切换联网搜索状态 */
    toggleWebSearch() {
      this.webSearch = !this.webSearch
    },

    handleWebSearchChange(value) {
      this.webSearch = value
    },

    /** 处理场景选择 */
    handleSceneSelect(item) {
      this.selectedDemoScene = item.label
      this.selectedDemoSceneValue = item.value
      this.showSceneSelector = false
      this.handleDemoSceneChange(item.value)
    },

    /** 处理语言选择 */
    handleLanguageSelect(item) {
      this.selectedLanguage = item.value
      this.showLanguageSelector = false
      this.handleLanguageChange(item.value)
    },

    /** 处理模型选择 */
    handleModelSelect(item) {
      this.selectedChatModel = item.label
      this.showModelSelector = false
      this.handleModelChange(item.value)
    },
    // --- 选择器方法结束 ---

    /** 处理点击外部区域关闭选择器 */
    handleOutsideClick(event) {
      // 检查点击事件是否发生在选择器按钮或下拉面板内
      const isSceneClick = event.target.closest('.scene-select-wrapper')
      const isLanguageClick = event.target.closest('.language-select-wrapper')
      const isModelClick = event.target.closest('.model-select-wrapper')
      const isSettingsClick = event.target.closest('.settings-container')

      // 如果点击不是发生在对应的选择器区域内，则关闭相应的选择器
      if (!isSceneClick && this.showSceneSelector) {
        this.showSceneSelector = false
      }

      if (!isLanguageClick && this.showLanguageSelector) {
        this.showLanguageSelector = false
      }

      if (!isModelClick && this.showModelSelector) {
        this.showModelSelector = false
      }

      // 处理主题选择器的外部点击
      if (!isSettingsClick && this.showThemeSelector) {
        this.showThemeSelector = false
      }
    },
    validateString(str) {
      return !(str === '' || str == null || str.trim() === '' || str.length === 0 || str === 'null' || str === 'undefined')
    },
    // 重构：校验初始参数信息 (优先级: Cookie -> URL UID -> System Token)
    async getUrlParam() {
      // --- 准备工作：获取所有可能的认证信息 ---
      let urlUid = this.handleUrlParam('uid')             // 尝试从 URL 读取 UID
      if (urlUid === '' || urlUid == null) {
        urlUid = Cookies.get('cas_enter_ppt_v1_uid')   // 尝试从缓存(cas_enter_ppt_v1_uid)中获取  uid
        console.log('尝试从缓存(cas_enter_ppt_v1_uid)中获取  uid', urlUid)
      }

      console.log('认证: 开始认证检查...')

      // --- 优先级 2: 检查 URL 中的 UID
      if (this.validateString(urlUid)) {
        console.log('检查 URL UID:', urlUid)
        try {
          // 调用后端接口验证 UID
          const res = await getUidDetail({ uid: urlUid })
          // 检查后端响应是否成功且没有错误信息
          if (res.code === 200 && !res.data.error_msg) {
            console.log('认证: UID 验证成功。')
            // 从后端获取 API Key
            // this.apiKey = res.data.apiKey
            // this.userName = res.data.userId
            // this.apiKeyFatherProvider.apiKey = this.apiKey // 更新 provide 的值

            // 根据后端响应设置初始内容 (可选)
            this.requestType = res.data.type
            if ('topic' === res.data.type) {
              this.topicPrompt = res.data.content
            } else if ('text' === res.data.type) this.textContent = res.data.content

            this.hasValidApiKey = true // 允许访问页面
            // 清理 URL 中的 uid 参数并尝试导航 (避免刷新时重复验证)
            const newPath = this.cleanUrlAndNavigate('uid')
            if (this.$router && window.location.pathname + window.location.search !== newPath) {
              await this.$router.push(newPath)
              // 注意：导航可能会导致组件重新加载并再次执行此检查流程
            }

          } else {
            // 后端验证 UID 失败
            console.log('认证: UID 验证失败。', res.data?.error_msg)
            this.handleError(res.data?.error_msg || 'UID密钥验证失败')
            // 清理可能无效的 uid 参数，避免后续混淆
            const newPath = this.cleanUrlAndNavigate('uid')
            if (this.$router && window.location.pathname + window.location.search !== newPath) {
              await this.$router.push(newPath)
            }
            // 虽然 UID 验证失败，但流程会继续检查 System Token
          }
        } catch (error) {
          // 调用后端接口本身失败 (例如网络错误)
          console.error('认证: UID 验证请求失败:', error)
          this.handleError('API密钥验证服务不可用')
          // 清理可能无效的 uid 参数
          const newPath = this.cleanUrlAndNavigate('uid')
          if (this.$router && window.location.pathname + window.location.search !== newPath) {
            await this.$router.push(newPath)
          }
          // 即使接口请求失败，流程也会继续检查 System Token
        }
      }

      const systemLoginToken = getToken()    // 尝试获取系统登录 Token
      // --- 优先级 3: 检查系统登录 Token (仅当 Cookie 和 UID 都无效或不存在时) ---
      if (this.validateString(systemLoginToken)) {
        console.log('检查系统 Token 存在。')
        this.hasValidApiKey = true // 允许访问页面本身
        // 用户完成了系统登录, 获取登录缓存的用户名 也就是账户作为用户唯一标识
        if (Cookies.get('userName')) {
          this.userName = Cookies.get('userName')
        }
      }

      // --- 最后处理：根据认证结果决定是否初始化后续数据 ---
      // 修改逻辑：只要 apiKey 设置成功 或 用户已登录系统 (有systemLoginToken)，就尝试初始化
      if (this.hasValidApiKey || systemLoginToken) {
        console.log('认证: API key 已设置或系统已登录。正在初始化数据...')
        await this.initializeData() // 调用 handleCreateApiToken 获取 AI PPT Token 等
      } else {
        console.log('认证: API key 未设置且系统未登录。跳过数据初始化。')
      }

      // --- 处理完全认证失败的情况 ---
      if (!this.hasValidApiKey && !this.validateString(systemLoginToken)) {
        console.log('认证: 所有认证方法均失败。')
        this.hasValidApiKey = false // 最终确认无法访问
        this.$notify.error({
          title: '访问受限',
          message: '缺少必要的认证参数或认证失败，无法访问此功能。',
          duration: 8000
        })
      }
    },

    // 处理错误的辅助方法
    handleError(message) {
      this.hasValidApiKey = false
      this.$notify.error({
        title: '错误',
        message: message,
        duration: 5000
      })
    },

    // 初始化其他数据的辅助方法
    async initializeData() {
      await this.handleCreateApiToken() // 获取aI ppt token
      this.handleGetSceneOptions().then() // 场景主题选择
      this.handleGetLanguage().then() // 语言选择
      this.handleGetChatModel().then()  // 模型类型
      this.handleGetPptEditData() // 编辑器url
      this.handleGetAiPptRandomTemplates() // 获取模板列表，确保初始化时就有模板数据
      this.handleGetTopicSuggestionList() // 获取一句话主题建议
    },

    handleGetTopicSuggestionList() {
      getDicts('topic_suggestion_list').then(res => {
        this.topicSuggestionList = res.data
        console.log(this.topicSuggestionList)
      })
    },

    // --- 新增：主题设置相关方法 ---
    /**
     * 初始化主题设置
     */
    initializeTheme() {
      // 从本地存储加载用户选择的主题
      const savedTheme = localStorage.getItem('ai_ppt_user_theme')
      if (savedTheme && this.backgroundThemeOptions.find(t => t.id === savedTheme)) {
        this.currentTheme = savedTheme
      }
      // 应用主题
      this.applyTheme()
    },

    /**
     * 切换主题选择器显示状态
     */
    toggleThemeSelector() {
      this.showThemeSelector = !this.showThemeSelector
    },

    /**
     * 选择主题
     */
    selectTheme(themeId) {
      this.currentTheme = themeId
      // 保存到本地存储
      localStorage.setItem('ai_ppt_user_theme', themeId)
      // 应用主题
      this.applyTheme()
      // 关闭选择器
      this.showThemeSelector = false
    },

    /**
     * 应用主题样式
     */
    applyTheme() {
      const theme = this.backgroundThemeOptions.find(t => t.id === this.currentTheme)
      if (theme) {
        // 更新CSS变量
        document.documentElement.style.setProperty('--theme-gradient', theme.gradient)
      }
    },


    // 清理url 相关参数地址
    cleanUrlAndNavigate(item) {
      const currentUrl = new URL(window.location.href)
      const params = new URLSearchParams(currentUrl.search)
      params.delete(item)
      return '/pptV1' + (params.toString() ? `?${params.toString()}` : '')
    },
    // 获取生成大纲语言选择
    async handleGetLanguage() {
      let headers = null
      if (!getToken() && this.apiKey) {
        headers['apiKey'] = this.apiKey
      }
      getOutlineLang(null, headers).then(res => {
        if (res.code === 200 && res.data) {
          this.selectedLanguage = res.data[0].value
          this.languageOptions = res.data

        }
      })
    },
    // 获取自定义场景主题
    async handleGetSceneOptions() {
      let headers = null
      if (!getToken() && this.apiKey) {
        headers['apiKey'] = this.apiKey
      }
      getSceneOptions(null, headers).then(res => {
        if (res.code === 200 && res.data) {
          this.selectedDemoScene = res.data[0].label
          this.selectedDemoSceneValue = res.data[0].value // 确保设置value属性，用于CardSelector
          this.demoSceneOptions = res.data
        }
      })
    },
    // 获取模型类型
    async handleGetChatModel() {
      let headers = null
      if (!getToken() && this.apiKey) {
        headers['apiKey'] = this.apiKey
      }
      getChatModel(null, headers).then(res => {
        if (res.code === 200 && res.data) {
          this.selectedChatModel = res.data[0].label
          this.selectedChatModelValue = res.data[0].value // 确保设置value属性，用于CardSelector
          this.chatModelOptions = res.data
        }
      })
    },
    // 获取aI ppt token
    async handleCreateApiToken() {
      const tokenKey = `${this.userName}_ai_ppt_token`
      const cacheToken = Cookies.get(tokenKey)
      if (cacheToken) {
        this.aiPptToken = cacheToken
        console.log('使用 cache_ai_ppt_token')
        return cacheToken
      }

      const data = {
        uid: this.userName,
        timestamp: Date.now()
      }

      try {
        const res = await createApiToken(data, { apiKey: this.apiKey })
        if (res.code === 200 && res.data.pptToken) {
          this.aiPptToken = res.data.pptToken
          console.log(`uid = ${data.uid},接口获取 PptToken 成功`)
          const expiresTime = new Date(Date.now() + 3 * 60 * 1000)
          Cookies.set(tokenKey, this.aiPptToken, { expires: expiresTime })
          return this.aiPptToken
        } else {
          throw new Error(res.data.error || '获取 token 失败')
        }
      } catch (err) {
        this.$notify({
          title: '错误',
          message: err.message,
          type: 'error',
          duration: 5000
        })
        return null
      }
    },

    /**
     * 重置表单数据
     */
    resetForm() {
      this.form = {
        query: '',
        theme: '',
        font: '',
        logoPosition: '',
        logoZoom: 1,
        spacingXY: '',
        createModel: '',
        isCardNote: false
      }
      this.fileId = ''
      this.activeStep = 0
      this.isGenerateSuccess = false
      this.imgSinglePreviewList = []
      localStorage.removeItem('createPPTData')
    },

    /**
     * 提交表单，生成 PPT
     */
    handleSubmit() {
      this.pptEditLoadingText = 'PPT生成中...'
      // Bug 3 调试：添加日志
      console.log('[handleSubmit] Triggered. Current state:',
          {
            activeStep: this.activeStep,
            editingType: this.editingType,
            topicTaskId: this.topicTaskId,
            topicFinalMarkdown: this.topicFinalMarkdown ? this.topicFinalMarkdown.substring(0, 50) + '...' : '',
            textTaskId: this.textTaskId,
            textFinalMarkdown: this.textFinalMarkdown ? this.textFinalMarkdown.substring(0, 50) + '...' : '',
            templateId: this.form.templateId,
            isSubmittingPpt: this.isSubmittingPpt,
            disableConfirmDownload: this.disableConfirmDownload
          }
      )
      if (this.activeStep === 2) {
        // Bug 1 调试：确认 editingType
        console.log('[handleSubmit] Calling handleAiPptGeneratePptx with editingType:', this.editingType)
        this.handleAiPptGeneratePptx()
      } else {
        this.$notify({
          title: '错误',
          message: '请完成所有步骤后再提交',
          type: 'error',
          duration: 3000
        })
      }
    },

    async handleUploadSuccess(response, file) {
      console.log('[handleUploadSuccess] Response:', response, 'File:', file)
      if (response.code === 200 && response.data && response.data.id) {
        const fileId = response.data.id
        this.fileId = fileId // 保留 fileId 状态，用于可能的删除操作
        this.uploadedFileName = file.name
        this.fileList = [{ name: file.name, url: '' }] // 更新文件列表以显示

        this.$notify({
          title: '提示',
          message: '上传成功，正在创建处理任务...',
          type: 'success',
          duration: 3000
        })

        try {
          const taskId = await this.handleCreateTask('file', fileId)
          if (taskId) {
            // 添加初始消息
            this.fileMessages.push({
              role: 'user',
              content: `文件 '${this.uploadedFileName}' 上传成功，开始生成大纲...`,
              timestamp: new Date().getTime()
            })
            this.scrollToBottom() // 滚动到底部
            // 调用内容生成
            await this.generateContent('file')
          } else {
            // handleCreateTask 内部应该已经通知错误了
            console.error('[handleUploadSuccess] Failed to create task after upload.')
          }
        } catch (error) {
          // handleCreateTask 内部应该已经通知错误了
          console.error('[handleUploadSuccess] Error during task creation or content generation:', error)
          // 可能需要额外的错误处理或状态重置
          this.handleRemove() // 清理文件状态
        }

      } else {
        this.$notify.error({
          title: '错误' + (response.code ? ' ' + response.code : ''),
          message: response.msg || '文件上传失败或返回数据无效'
        })
        this.handleRemove() // 清理文件状态
      }
    },
    handleRemove(file, fileList) {
      // 清理文件相关的所有状态
      this.fileId = '' // 这个 ID 现在可能没用了，但清理无害
      this.fileList = []
      this.selectedFile = null // 新增：清理选中的文件对象
      this.fileTaskId = ''
      this.fileMessages = []
      this.fileFinalMarkdown = ''
      this.fileStreamContent = ''
      this.isFileGenerateComplete = false
      this.isFileGenerating = false
      // 如果当前正在编辑文件内容，也需要重置
      if (this.editingType === 'file') {
        this.showOutlineEditor = false
        this.editingType = ''
        this.generatedMarkdownContent = ''
        this.editingMessageInfo = { type: null, index: -1 }
      }
      console.log('[handleRemove] File states cleared.')
    },

    /** 获取模板列表 */
    handleGetAiPptTemplates() {
      let data = {
        'page': 1,
        'size': 10,
        'filters': { 'type': 1 } // 1系统模板、4用户自定义模板
      }
      getAiPptTemplates(data, { apiKey: this.apiKey }).then(res => {
        this.themeOptions = [...res.rows]
        this.form.templateId = this.themeOptions[0]?.id
      })
    },

    /**
     * 下载模板
     * @param {Object} item - 模板对象
     */
    async handleDownloadTemplate(item) {
      const data = {
        id: item.id,
        pptToken: await this.handleCreateApiToken()
      }

      try {
        const res = await downloadTemplate(data, { apiKey: this.apiKey })

        if (res.code === 200 && res.data) {
          const fileUrl = res.data.fileUrl

          // 发起 fetch 请求获取 Blob
          const response = await fetch(fileUrl)
          const blob = await response.blob()

          // 创建 Blob URL
          const blobUrl = window.URL.createObjectURL(blob)

          // 构建下载链接
          const link = document.createElement('a')
          link.href = blobUrl

          // 设定你想要的文件名
          link.download = '模板_' + item.id + '.pptx'

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          // 释放 URL 对象
          window.URL.revokeObjectURL(blobUrl)

          this.$notify({
            title: '下载成功',
            message: '模板下载已开始',
            type: 'success',
            duration: 3000
          })
        } else {
          this.$notify.error({
            title: '下载失败',
            message: res.message || '无法获取模板下载链接',
            duration: 5000
          })
        }
      } catch (error) {
        console.error('模板下载失败:', error)
        this.$notify.error({
          title: '下载失败',
          message: '下载过程中出现错误，请稍后重试',
          duration: 5000
        })
      }
    },

    /** 获取随机模板列表 */
    async handleGetAiPptRandomTemplates() {
      this.templateLoading = true
      let data = {
        'size': 12, // 增加数量以支持更好的筛选体验
        'filters': { 'type': this.selectedTemplateType }, // 修改：使用 data 属性
        'pptToken': await this.handleCreateApiToken()
      }
      getAiPptRandomTemplates(data, { apiKey: this.apiKey }).then(res => {
        this.themeOptions = [...res.rows]
        // Bug fix: 切换类型或刷新后，可能需要重置 templateId，避免选中一个不存在于新列表的模板
        // 检查当前选中的 templateId 是否在新列表中，如果不在则清空
        const currentSelectionExists = this.themeOptions.some(t => t.id === this.form.templateId)
        if (!currentSelectionExists) {
          this.form.templateId = null // 清空无效选择
          this.selectedTemplate = null
        }

        // 优化：如果当前没有选中任何模板且列表不为空，则自动选择第一个
        // 这个逻辑主要用于初始化时的自动选择，TemplateSelector组件的watch也会处理这种情况
        if (!this.form.templateId && this.themeOptions.length > 0) {
          const firstTemplate = this.themeOptions[0]
          this.form.templateId = firstTemplate.id
          this.selectedTemplate = firstTemplate
          this.selectedTemplateId = firstTemplate.id
          console.log('自动选择第一个模板:', firstTemplate.id)

          // 如果当前在模板选择步骤，更新背景
          if (this.activeStep === 1 && this.isTemplateBackgroundActive) {
            this.updateTemplateBackground(firstTemplate)
          }
        }

        this.templateLoading = false
      }).catch(error => {
        console.error('获取模板列表失败:', error)
        this.templateLoading = false
      })
    },

    // --- 新增：处理模板类型切换 ---
    handleTemplateTypeChange(templateType) {
      this.selectedTemplateType = templateType
      console.log('切换模板类型为:', this.selectedTemplateType)
      this.templateSearch = '' // 清空搜索条件
      this.selectedCategory = '' // 清空分类筛选
      this.selectedStyle = '' // 清空风格筛选
      // // 清空当前选择，因为新列表可能不包含它
      // this.form.templateId = null;
      // this.selectedTemplate = null;
      // 重新获取模板列表
      this.handleGetAiPptRandomTemplates()
    },

    // 获取历史生成记录
    async handleAiPptListPptx() {
      console.log('获取历史生成记录: 页码', this.pptHistoryQueryParams.page)
      this.pptHistoryLoading = true
      const data = {
        page: this.pptHistoryQueryParams.page,
        size: this.pptHistoryQueryParams.size,
        pptToken: await this.handleCreateApiToken()
      }
      aiPptListPptx(data, { apiKey: this.apiKey }).then(res => {
        console.log('历史记录响应:', res)
        if (res.code === 200) {
          // this.pptHistoryList = (res.rows || []).sort((a, b) => {
          //   return new Date(b.createTime) - new Date(a.createTime); // 倒序，最新在前
          // });
          this.pptHistoryList = res.rows || []
          this.pptHistoryTotal = res.total || 0
        } else {
          this.$notify.error({ title: '获取历史记录失败', message: res.msg || '未知错误' })
          this.pptHistoryList = []
          this.pptHistoryTotal = 0
        }
      }).catch(error => {
        console.error('获取历史记录 API 错误:', error)
        this.$notify.error({ title: '网络错误', message: '无法获取历史记录' })
        this.pptHistoryList = []
        this.pptHistoryTotal = 0
      }).finally(() => {
        this.pptHistoryLoading = false
      })
    },

    // --- 新增：历史记录分页改变处理 ---
    handleHistoryPageChange(newPage) {
      this.pptHistoryQueryParams.page = newPage
      this.handleAiPptListPptx()
    },

    // --- 新增：Tab切换前的检查 ---
    handleBeforeTabLeave(activeName, oldActiveName) {
      // 如果正在进行流式输出，阻止tab切换
      if (this.isAnyGenerating) {
        // 返回 false 阻止切换
        this.$notify.warning({ title: '请等待', message: '正在生成中，请稍候' })
        return false
      }
      // 允许切换
      return true
    },

    // --- 新增：内容区域 Tab 点击处理 ---
    handleContentTabClick(tab) {
      if (tab !== null) {
        console.log('切换到 Tab:', tab.name)
        this.elTabPaneItem = {
          label: tab.label,
          name: tab.name
        }
      }
      if (this.elTabPaneItem.name === 'history') {
        // 切换到历史记录时，如果列表为空，则加载第一页
        if (this.pptHistoryList.length === 0) {
          this.pptHistoryQueryParams.page = 1 // 确保从第一页开始加载
        }
        this.handleAiPptListPptx()
      } else if (this.elTabPaneItem.name === 'customTemplate') { // 添加对自定义模板tab的判断
        this.getCustomTemplateList() // 调用获取自定义模板列表的方法
      }
    },

    // --- 建议逻辑结束 ---

    // 修改：通用化创建任务方法，接收 fileOrContent
    async handleCreateTask(inputType, fileOrContent = null) {
      const formData = new FormData()
      let typeApiValue = ''

      if (inputType === 'topic') {
        this.topicTaskId = ''
        this.topicFinalMarkdown = ''
        this.topicStreamContent = ''
        this.isTopicGenerateComplete = false
        typeApiValue = '1'
        if (!fileOrContent || !fileOrContent.trim()) {
          this.$notify.error({
            title: '错误',
            message: '请输入主题描述',
            duration: 4000
          })
          return Promise.reject('输入内容为空')
        }
        // 添加用户消息到对应的数组
        const userMessage = { role: 'user', content: fileOrContent, timestamp: new Date().getTime() }
        this.topicMessages.push(userMessage)
        // console.log(`[handleCreateTask - topic] Appending content to FormData: "${fileOrContent}"`); // 移除日志
        formData.append('content', fileOrContent)

      } else if (inputType === 'text') {
        this.textTaskId = ''
        this.textFinalMarkdown = ''
        this.textStreamContent = ''
        this.isTextGenerateComplete = false
        typeApiValue = '6'
        // 移除这里的验证，handleTextSubmit 已验证
        // if (!fileOrContent || !fileOrContent.trim()) {
        // ... (验证注释保持不变)
        // }
        // 添加用户消息到对应的数组
        const userMessage = { role: 'user', content: fileOrContent, timestamp: new Date().getTime() }
        this.textMessages.push(userMessage)
        // console.log(`[handleCreateTask - text] Appending content to FormData: "${fileOrContent}"`); // 移除日志
        formData.append('content', fileOrContent)

      } else if (inputType === 'file') {
        this.fileTaskId = ''
        this.fileFinalMarkdown = ''
        this.fileStreamContent = ''
        this.isFileGenerateComplete = false
        typeApiValue = '2' // 文件类型为 2
        if (!(fileOrContent instanceof File)) { // 校验是否为 File 对象
          this.$notify.error({ title: '错误', message: '缺少有效的文件对象，无法创建任务', duration: 4000 })
          return Promise.reject('缺少文件对象')
        }
        formData.append('file', fileOrContent) // 使用 file 字段传递文件对象
        // 用户初始消息在 handleFileSubmit 中添加

      } else {
        this.$notify.error({ title: '错误', message: '无效的任务类型', duration: 4000 })
        return Promise.reject('无效的任务类型')
      }

      formData.append('type', typeApiValue)

      try {
        formData.append('pptToken', await this.handleCreateApiToken())
        let headers = null
        if (!getToken() && this.apiKey) {
          headers['apiKey'] = this.apiKey
        }
        const res = await createTask(formData, headers)
        console.log(`创建任务 (${inputType}) 响应：`, res)

        if (res.code !== 200 || res.data.error || !res.data.taskId) {
          const errorMsg = res.data?.error || '创建任务失败，未返回任务ID'
          this.$notify.error({
            title: '错误',
            message: errorMsg,
            duration: 4000
          })
          return Promise.reject(errorMsg)
        }

        // 存储任务ID到对应的变量
        const taskId = res.data.taskId
        if (inputType === 'topic') {
          this.topicTaskId = taskId
        } else if (inputType === 'text') {
          this.textTaskId = taskId
        } else if (inputType === 'file') {
          this.fileTaskId = taskId
        }

        console.log(`创建任务 (${inputType}) 成功，任务ID：`, taskId)
        return Promise.resolve(taskId) // 只返回 taskId
      } catch (error) {
        console.error(`创建任务 (${inputType}) API 调用失败:`, error)
        this.$notify.error({ title: '错误', message: '创建任务请求失败', duration: 4000 })
        return Promise.reject('创建任务请求失败')
      }
    },

    /**
     * 统一的中止生成请求方法
     * 自动识别当前活跃的生成类型并中止
     */
    async abortGeneration() {
      try {
        if (!this.globalAbortController) {
          console.warn('[abortGeneration] No active generation to abort')
          return
        }

        // 自动识别当前活跃的生成类型
        let activeType = ''
        let messagesArray = []

        if (this.isTopicGenerating) {
          activeType = 'topic'
          messagesArray = this.topicMessages
        } else if (this.isTextGenerating) {
          activeType = 'text'
          messagesArray = this.textMessages
        } else if (this.isFileGenerating) {
          activeType = 'file'
          messagesArray = this.fileMessages
        }

        if (!activeType) {
          console.warn('[abortGeneration] No active generation found')
          return
        }

        // 1. 设置中止标志并前端中止请求
        this.isGenerationAborted = true
        this.globalAbortController.abort()

        // 2. 调用后台取消接口（如果有 requestId）
        if (this.requestId) {
          try {
            await cancelGenerationApi({ 'requestId': this.requestId })
          } catch (error) {
            console.error('[abortGeneration] Cancel generation API call failed:', error)
            // 后台取消失败不影响前端中止
          }
        }

        // 3. 删除未完成的对话记录
        this.removeIncompleteMessages(activeType, messagesArray)

        // 4. 重置相关状态
        this.resetGenerationState(activeType)

        createNotificationTopLeft('生成已成功中止', CreateNotificationType.SUCCESS)

      } catch (error) {
        console.error('[abortGeneration] Error aborting generation:', error)
      }
    },

    /**
     * 删除未完成的对话记录
     * @param {string} inputType - 生成类型
     * @param {Array} messagesArray - 消息数组
     */
    removeIncompleteMessages(inputType, messagesArray) {
      // 删除最后一条用户消息和未完成的助手回复
      if (messagesArray.length > 0) {
        const lastMessage = messagesArray[messagesArray.length - 1]

        // 如果最后一条是助手消息（可能未完成），删除它
        if (lastMessage.role === 'assistant') {
          messagesArray.pop()
          console.log(`[removeIncompleteMessages] Removed incomplete assistant message for type: ${inputType}`)
        }

        // 如果现在最后一条是用户消息，也删除它
        if (messagesArray.length > 0) {
          const currentLastMessage = messagesArray[messagesArray.length - 1]
          if (currentLastMessage.role === 'user') {
            messagesArray.pop()
            console.log(`[removeIncompleteMessages] Removed user message for type: ${inputType}`)
          }
        }
      }
    },

    /**
     * 重置生成状态
     * @param {string} inputType - 生成类型
     */
    resetGenerationState(inputType) {
      if (inputType === 'topic') {
        this.isTopicGenerating = false
        this.isTopicGenerateComplete = false
        this.topicStreamContent = ''
        this.topicFinalMarkdown = ''
      } else if (inputType === 'text') {
        this.isTextGenerating = false
        this.isTextGenerateComplete = false
        this.textStreamContent = ''
        this.textFinalMarkdown = ''
      } else if (inputType === 'file') {
        this.isFileGenerating = false
        this.isFileGenerateComplete = false
        this.fileStreamContent = ''
        this.fileFinalMarkdown = ''
      }
      this.generatedMarkdownContent = ''
      this.editingType = ''
      this.editingMessageInfo = { type: null, index: -1 }
    },

    /**
     * 处理一句话生成请求
     * @returns {Promise<void>}
     */
    async generateContent(inputType) {
      // Bug 2: 重置编辑特定消息的状态
      this.editingMessageInfo = { type: null, index: -1 }

      let taskId = ''
      if (inputType === 'topic') {
        taskId = this.topicTaskId
      } else if (inputType === 'text') {
        taskId = this.textTaskId
      } else if (inputType === 'file') { // 新增：处理文件类型
        taskId = this.fileTaskId
      } else {
        this.$notify.error({ title: '错误', message: '无效的生成类型', duration: 4000 })
        return
      }

      if (!taskId) {
        this.$notify.error('无法生成内容，缺少任务ID')
        if (inputType === 'topic') {
          this.isTopicGenerating = false
        } else if (inputType === 'text') {
          this.isTextGenerating = false
        } else if (inputType === 'file') {
          this.isFileGenerating = false
        }
        return
      }

      // 创建统一的 AbortController 并重置中止标志
      this.globalAbortController = new AbortController()
      this.isGenerationAborted = false

      // 设置对应模式的加载状态和清空流式内容
      if (inputType === 'topic') {
        this.isTopicGenerating = true
        this.topicStreamContent = ''
        this.isTopicGenerateComplete = false
      } else if (inputType === 'text') {
        this.isTextGenerating = true
        this.textStreamContent = ''
        this.isTextGenerateComplete = false
      } else if (inputType === 'file') { // 新增：设置文件类型状态
        this.isFileGenerating = true
        this.fileStreamContent = ''
        this.isFileGenerateComplete = false
      }

      let currentAssistantMessage = '' // 累加器，用于最终的 Markdown 内容

      try {
        // 1. 获取 loginToken
        const loginToken = getToken()

        // 2. 构建基础 Headers
        const headers = {
          'Content-Type': 'application/json',
          'api-key': this.apiKey
        }

        // 3. 如果 Token 存在，添加 Authorization Header
        if (loginToken) {
          headers['Authorization'] = 'Bearer ' + loginToken
        } else if (this.apiKey) {
          headers['api-key'] = this.apiKey
        }

        const data = {
          'id': taskId, // 使用正确的任务ID
          'stream': true,
          'length': 'long',
          'lang': this.selectedLanguage,
          'prompt': this.selectedDemoScene.slice(0, 50), // 50字以内,过长自动截取
          'model': inputType === 'file' ? '默认' : this.selectedChatModel, //模型 (文件类型 走默认其余走选择的)
          'webSearch': this.webSearch // 添加网络搜索参数
        }
        // 4. 发起 Fetch 请求
        const response = await generateContentApi(data, headers, this.globalAbortController.signal)

        if (!response.ok) { // 检查网络响应状态
          throw new Error(`API 请求失败，状态码: ${response.status}`)
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let buffer = ''

        while (true) {
          const { done, value } = await reader.read()
          if (done) break // 正确的结束信号

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim().startsWith('data:')) {
              try {
                const jsonStr = line.replace('data:', '').trim()
                if (!jsonStr) continue // 跳过空数据行
                const data = JSON.parse(jsonStr)

                // 只处理 status 3，累加内容
                if (data.status.toString() === '3' && data.text) {
                  data.text = data.text
                      .replace(/^```(?:markdown)?\s*/, '')     // 去掉开头
                      .replace(/\s*```$/, '')                 // 去掉结尾
                  currentAssistantMessage += data.text
                  this.requestId = data.requestId
                  // 更新对应模式的流式内容
                  if (inputType === 'topic') {
                    this.topicStreamContent = currentAssistantMessage
                  } else if (inputType === 'text') {
                    this.textStreamContent = currentAssistantMessage
                  } else if (inputType === 'file') this.fileStreamContent = currentAssistantMessage // 新增：更新文件流式内容
                  this.scrollToBottom() // 确保滚动
                }
                // **移除所有 data.status === 4 的处理逻辑**
              } catch (e) {
                console.error('Error parsing SSE data:', e, line)
              }
            }
          }
        } // --- while 循环结束 ---

        // --- 在循环结束后处理最终结果 ---
        console.log(`[generateContent] Stream finished for type ${inputType}. Final accumulated markdown length:`, currentAssistantMessage.length)

        if (currentAssistantMessage && currentAssistantMessage.trim()) {
          let finalMarkdown = currentAssistantMessage
          let messagesArray = []

          // 分配最终 markdown 并设置完成状态
          if (inputType === 'topic') {
            this.topicFinalMarkdown = finalMarkdown
            messagesArray = this.topicMessages
            this.isTopicGenerateComplete = true
            this.topicStreamContent = '' // 清空流式显示
          } else if (inputType === 'text') {
            this.textFinalMarkdown = finalMarkdown
            messagesArray = this.textMessages
            this.isTextGenerateComplete = true
            this.textStreamContent = '' // 清空流式显示
          } else if (inputType === 'file') { // 新增：处理文件类型
            this.fileFinalMarkdown = finalMarkdown
            messagesArray = this.fileMessages
            this.isFileGenerateComplete = true
            this.fileStreamContent = '' // 清空流式显示
            // 文件名不清空，保留显示 this.uploadedFileName = '';
          }

          // 准备并显示编辑器（仅在未被中止的情况下）
          console.log('this.isGenerationAborted', this.isGenerationAborted)
          if (!this.isGenerationAborted) {
            // 添加完整的助手消息到数组
            if (messagesArray) {
              const assistantMessage = {
                role: 'assistant',
                content: finalMarkdown,
                timestamp: new Date().getTime()
              }
              console.log(`[generateContent] Adding final assistant message for type ${inputType}:`, assistantMessage)
              messagesArray.push(assistantMessage)
              this.scrollToBottom() // 添加消息后再次滚动到底部
            }

            this.generatedMarkdownContent = finalMarkdown
            this.editingType = inputType
            this.showOutlineEditor = true
          } else {
            console.log(`[generateContent] Generation was aborted, skipping editor display for type: ${inputType}`)
          }

        } else {
          // 处理流结束但未累积内容的情况
          console.warn(`[generateContent] Stream finished for type ${inputType} but no content was accumulated.`)
          if (inputType === 'topic') {
            this.isTopicGenerateComplete = false
          }// 标记为未完成
          else if (inputType === 'text') {
            this.isTextGenerateComplete = false
          } else if (inputType === 'file') this.isFileGenerateComplete = false
        }
        // --- 最终结果处理结束 ---

      } catch (error) {
        // 检查是否是用户主动中止
        if (error.name === 'AbortError') {
          console.log(`[generateContent] Generation aborted for type: ${inputType}`)
          this.$notify.info({
            title: '已中止',
            message: '生成已被用户中止',
            position: 'top-left'
          })
        } else {
          this.$notify.error(`生成 (${inputType}) 失败：` + error.message)
        }
        // 确保错误时完成状态为 false
        if (inputType === 'topic') {
          this.isTopicGenerateComplete = false
        } else if (inputType === 'text') {
          this.isTextGenerateComplete = false
        } else if (inputType === 'file') this.isFileGenerateComplete = false
      } finally {
        // 停止加载指示器
        if (inputType === 'topic') {
          this.isTopicGenerating = false
        } else if (inputType === 'text') {
          this.isTextGenerating = false
        } else if (inputType === 'file') {
          this.isFileGenerating = false
        }

        // 清理全局 AbortController
        this.globalAbortController = null

        // 在 finally 中检查最终的完成状态并发送通知
        const isComplete = inputType === 'file' ? this.isFileGenerateComplete
            : (inputType === 'topic' ? this.isTopicGenerateComplete : this.isTextGenerateComplete)

        if (isComplete && this.showOutlineEditor) {
          // // 成功通知（编辑器已显示）
          // this.$notify({
          //   title: '大纲内容生成成功',
          //   message: '请编辑您的大纲',
          //   type: 'success',
          //   duration: 2000
          // })
        } else if (!isComplete) {
          // 失败或未完成通知 (如果 catch 中没有更具体的错误消息)
          console.warn(`Generation (${inputType}) finished but might be incomplete or failed.`)
          // 可以选择性地在此处添加一个通用的失败通知，如果 catch 中未处理
          // 例如: this.$notify.warning(`大纲生成 (${inputType}) 未完成或失败。`);
        }
      }
    },

    scrollToBottom() {
      this.$nextTick(() => {
        // 修改：滚动目标改为 .chat-messages
        const chatMessagesContainer = document.querySelector('.chat-messages')
        if (chatMessagesContainer) {
          chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight
        }
      })
    },

    /**
     * 进入下一步
     */
    goNextStep() {
      if (this.activeStep < 2) {
        // 步骤1：验证模板选择
        if (this.activeStep === 1) {
          if (!this.form.templateId) {
            this.$notify.warning('请选择一个模板')
            return
          }
        }

        // 修改：在第一步时，检查是否有有效的大纲内容，并同步最终结果到消息列表
        if (this.activeStep === 0) {
          let hasValidOutline = false
          let finalMarkdown = ''
          let messagesArray = []

          if (this.editingType === 'topic' && this.topicFinalMarkdown && this.topicFinalMarkdown.trim()) {
            hasValidOutline = true
            finalMarkdown = this.topicFinalMarkdown
            messagesArray = this.topicMessages
          } else if (this.editingType === 'text' && this.textFinalMarkdown && this.textFinalMarkdown.trim()) {
            hasValidOutline = true
            finalMarkdown = this.textFinalMarkdown
            messagesArray = this.textMessages
          } else if (this.editingType === 'file' && this.fileFinalMarkdown && this.fileFinalMarkdown.trim()) { // 新增
            hasValidOutline = true
            finalMarkdown = this.fileFinalMarkdown
            messagesArray = this.fileMessages
          }

          // 如果没有有效的大纲，则提示并返回
          if (!hasValidOutline) {
            this.$notify.warning('请先进行您的内容创作')
            return
          }

          // Bug 1 & 2 修复：同步最终 Markdown 到对应的消息列表
          if (messagesArray.length > 0) {
            // 寻找最后一条助手消息进行更新
            let lastAssistantIndex = -1
            for (let i = messagesArray.length - 1; i >= 0; i--) {
              if (messagesArray[i].role === 'assistant') {
                lastAssistantIndex = i
                break
              }
            }

            if (lastAssistantIndex !== -1) {
              // 如果找到，更新内容
              if (messagesArray[lastAssistantIndex].content !== finalMarkdown) {
                console.log(`[goNextStep] Updating assistant message at index ${lastAssistantIndex} for type ${this.editingType}`)
                this.$set(messagesArray[lastAssistantIndex], 'content', finalMarkdown)
              }
            } else {
              // 如果没有找到助手消息（理论上不应该，除非只有用户消息），则添加一条新的
              console.log(`[goNextStep] Adding new assistant message for type ${this.editingType}`)
              messagesArray.push({
                role: 'assistant',
                content: finalMarkdown,
                timestamp: new Date().getTime()
              })
            }
          } else {
            // 如果消息数组为空（异常情况），也添加一条
            console.warn(`[goNextStep] Message array for type ${this.editingType} was empty. Adding new assistant message.`)
            messagesArray.push({
              role: 'assistant',
              content: finalMarkdown,
              timestamp: new Date().getTime()
            })
          }
        }

        // 如果检查通过，则进入下一步
        this.activeStep++

        // 如果进入模板设置步骤，自动获取模板列表并启用模板背景
        if (this.activeStep === 1) {
          this.handleGetAiPptRandomTemplates()
          this.enableTemplateBackground()
        }
        // 如果离开模板设置步骤，禁用模板背景
        else if (this.activeStep === 2) {
          this.disableTemplateBackground()
        }
      }
    },

    /**
     * 返回上一步
     */
    goPrevStep() {
      if (this.activeStep > 0) {
        this.activeStep--
        // 步骤切换逻辑 - 控制模板背景
        if (this.activeStep === 1) {
          // 进入模板选择步骤，启用模板背景
          this.enableTemplateBackground()
        } else if (this.activeStep === 0) {
          // 离开模板选择步骤，禁用模板背景
          this.disableTemplateBackground()
        }
      }
    },

    // 修改：切换请求类型的处理方法
    handleRequestTypeChange(newType) {
      // 如果正在进行流式输出，阻止切换
      if (this.isAnyGenerating) {
        this.$notify.warning({ message: '正在生成中，请稍候' })
        // 恢复到当前的requestType，阻止切换
        this.$nextTick(() => {
          // 强制恢复到原来的值，阻止UI切换
          // 注意：这里不能直接赋值，因为会触发无限循环
          // 我们需要通过其他方式来阻止切换
        })
        return false
      }

      console.log(`Switching request type to: ${newType}`)
      // 清理与新类型相关的输入状态
      if (newType === 'topic') {
        this.topicPrompt = ''
        // 清理文件状态，以防之前选了文件
        this.handleRemove()
      } else if (newType === 'text') {
        this.textContent = ''
        // 清理文件状态
        this.handleRemove()
      } else if (newType === 'file') {
        // 文件状态在上传时或移除时处理，切换到文件类型时不清空 topic/text 输入
        // 但需要确保文件列表等是空的，调用 handleRemove 比较合适
        this.handleRemove()
      }

      // 重置通用编辑和加载状态
      this.btnLoad = false // 重置最终提交按钮状态
      this.showOutlineEditor = false // 隐藏编辑器
      this.editingType = '' // 重置当前编辑上下文
      this.editingMessageInfo = { type: null, index: -1 } // 重置特定消息编辑状态
      // 注意：不清空各自的消息历史 (topicMessages, textMessages, fileMessages)

      // 更新当前的请求类型
      this.requestType = newType

      // 新增：在 DOM 更新后尝试移除可能残留在隐藏元素上的焦点
      this.$nextTick(() => {
        if (document.activeElement && document.activeElement !== document.body) {
          console.log('尝试移除焦点，避免 aria-hidden 警告', document.activeElement)
          document.activeElement.blur()
        }
      })
    },

    // 获取编辑器实际URL
    handleGetPptEditData() {
      getPptEditData().then(res => {
        console.log('获取到的数据为：', res)
        if (res.code === 200 && res.data) {

          res.data.forEach(item => {
            if (item.label === 'editUrl') {
              this.editUrl = item.value
            }
          })
          console.log('获取到的URL为：', this.editUrl)
        } else {
          this.$notify.error({ title: '获取编辑器URL失败', message: res })
        }
      }).catch(error => {
        this.$notify.error({ title: '请求编辑器URL接口失败', message: error })
      })
    },
    handleEditorReady() {
      console.log('编辑器准备就绪')
    },

    handleEditorError(error) {
      this.$notify({
        title: error.title,
        message: error.message,
        type: 'error',
        duration: 4000
      })
    },

    handleTokenInvalid() {
      this.$notify({
        title: '提示',
        message: 'API密钥已失效，请更新',
        type: 'warning',
        duration: 4000
      })
    },

    handleEditorMessage(message) {
      console.log('编辑器消息:', message)
    },

    handleTemplateSearch(value) {
      // 搜索功能已通过计算属性实现
    },

    selectTemplate(template) {
      if (!template || !template.id) {
        this.$notify({
          title: '错误',
          message: '无效的模板',
          type: 'error',
          duration: 2000
        })
        return
      }

      this.selectedTemplateId = template.id
      this.selectedTemplate = template
      this.form.templateId = template.id // 确保设置到form对象中

      console.log('Template selected:', template.id)
    },

    // --- 新增：模板选择器事件处理方法 ---
    /** 处理模板选择事件 */
    handleTemplateSelect(template) {
      this.selectTemplate(template)
      // 更新模板背景
      this.updateTemplateBackground(template)
    },

    /** 处理分类筛选变化 */
    handleCategoryChange(category) {
      this.selectedCategory = category
      console.log('分类筛选变化:', category)
    },

    /** 处理风格筛选变化 */
    handleStyleChange(style) {
      this.selectedStyle = style
      console.log('风格筛选变化:', style)
    },

    /** 处理清除筛选条件 */
    handleClearFilters() {
      this.selectedCategory = ''
      this.selectedStyle = ''
      this.templateSearch = ''
      console.log('清除所有筛选条件')
    },
    // --- 模板选择器事件处理结束 ---

    // --- 新增：模板背景处理方法 ---
    /** 更新模板背景 */
    updateTemplateBackground(template) {
      if (!template) {
        this.currentTemplateBackgroundUrl = ''
        return
      }

      let backgroundUrl = ''

      // 系统模板(type=1)：优先使用pageCoverUrls[0]
      if (template.type === 1) {
        if (template.pageCoverUrls && template.pageCoverUrls.length > 0) {
          backgroundUrl = `url(${template.pageCoverUrls[0]}?token=${this.aiPptToken})`
        } else if (template.coverUrl) {
          backgroundUrl = `url(${template.coverUrl}?token=${this.aiPptToken})`
        }
      }
      // 自定义模板(type=4)：使用coverUrl
      else if (template.type === 4) {
        if (template.coverUrl) {
          backgroundUrl = `url(${template.coverUrl}?token=${this.aiPptToken})`
        }
      }

      this.currentTemplateBackgroundUrl = backgroundUrl
      console.log('更新模板背景:', backgroundUrl)
    },

    /** 启用模板背景 */
    enableTemplateBackground() {
      this.isTemplateBackgroundActive = true
      // 如果当前有选中的模板，立即更新背景
      if (this.selectedTemplate) {
        this.updateTemplateBackground(this.selectedTemplate)
      }
    },

    /** 禁用模板背景 */
    disableTemplateBackground() {
      this.isTemplateBackgroundActive = false
      this.currentTemplateBackgroundUrl = ''
    },
    // --- 模板背景处理结束 ---

    // --- 修改 大纲编辑handleEditorCloseRequest 方法 ---
    handleEditorCloseRequest() {
      // Bug 3 修复：仅关闭编辑器，不返回第一步，不重置状态
      this.currentPptId = '' // 清除当前加载编辑器相关ID
      this.currentTemplateId = '' // 清除当前加载模板相关ID
      this.showOutlineEditor = false // 隐藏大纲编辑器
      this.editingMessageInfo = { type: null, index: -1 } // Bug 2: 重置编辑消息状态
      this.activeStep = 0 // 回到内容生成的过程

      this.handleContentTabClick(null)
    },

    confirmTemplateSelection() {
      if (!this.form.templateId) {
        this.$notify.warning('请选择一个模板')
        return
      }
      this.goNextStep()
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },

    /**
     * 处理 OutlineEditor 组件发出的 Markdown 更新事件
     * @param {string} newMarkdown 更新后的 Markdown 字符串
     */
    handleOutlineUpdate(newMarkdown) {
      console.log('[v1_index] Received outline-updated event.')

      // --- 核心修复：始终尝试更新最后一条助手消息 --- //
      let messagesArray = []
      if (this.editingType === 'topic') {
        messagesArray = this.topicMessages
      } else if (this.editingType === 'text') {
        messagesArray = this.textMessages
      } else if (this.editingType === 'file') { // 新增
        messagesArray = this.fileMessages
      }

      if (messagesArray.length > 0) {
        // 寻找最后一条助手消息
        let lastAssistantIndex = -1
        for (let i = messagesArray.length - 1; i >= 0; i--) {
          if (messagesArray[i].role === 'assistant') {
            lastAssistantIndex = i
            break
          }
        }

        if (lastAssistantIndex !== -1) {
          // 如果找到，更新内容
          if (messagesArray[lastAssistantIndex].content !== newMarkdown) {
            console.log(`[v1_index] Updating last assistant message content at index ${lastAssistantIndex} for type ${this.editingType}`)
            this.$set(messagesArray[lastAssistantIndex], 'content', newMarkdown)
          }
        } else {
          console.warn(`[v1_index] handleOutlineUpdate: No assistant message found to update for type ${this.editingType}`)
        }
      } else {
        console.warn(`[v1_index] handleOutlineUpdate: Message array is empty for type ${this.editingType}`)
      }
      // --- 修复结束 --- //

      // 始终更新父组件中用于驱动编辑器的 prop
      this.generatedMarkdownContent = newMarkdown

      // 始终根据当前编辑类型，更新对应的 finalMarkdown
      if (this.editingType === 'topic') {
        console.log('[v1_index] Syncing updated markdown content to topicFinalMarkdown.')
        this.topicFinalMarkdown = newMarkdown
      } else if (this.editingType === 'text') {
        console.log('[v1_index] Syncing updated markdown content to textFinalMarkdown.')
        this.textFinalMarkdown = newMarkdown
      } else if (this.editingType === 'file') { // 新增
        console.log('[v1_index] Syncing updated markdown content to fileFinalMarkdown.')
        this.fileFinalMarkdown = newMarkdown
      } else {
        console.warn('[v1_index] Cannot sync final markdown update, editingType is unknown:', this.editingType)
      }
    },
    formatTime(date) {
      const pad = (n) => (n < 10 ? '0' + n : n)
      return (
          date.getFullYear() + '-' +
          pad(date.getMonth() + 1) + '-' +
          pad(date.getDate()) + ' ' +
          pad(date.getHours()) + ':' +
          pad(date.getMinutes()) + ':' +
          pad(date.getSeconds())
      )
    },
    // 修改：处理标题更新事件
    handleTitleUpdate(newTitle) {
      console.log('[v1_index] Received title-updated event.')

      let currentMarkdown = ''
      if (this.editingType === 'topic') {
        currentMarkdown = this.topicFinalMarkdown
      } else if (this.editingType === 'text') {
        currentMarkdown = this.textFinalMarkdown
      } else if (this.editingType === 'file') { // 新增
        currentMarkdown = this.fileFinalMarkdown
      } else {
        console.warn('[v1_index] Cannot update title, editingType is unknown:', this.editingType)
        return
      }

      // 使用正则表达式替换 Markdown 中的第一行标题
      let updatedMarkdownContent = currentMarkdown.replace(/^#\s*.*/m, `# ${newTitle}`)
      // 检查替换是否成功，如果原本没有标题行，则在开头添加
      if (updatedMarkdownContent === currentMarkdown && !currentMarkdown.startsWith('# ')) {
        updatedMarkdownContent = `# ${newTitle}\n\n${currentMarkdown}`
      }

      // --- 核心修复：始终尝试更新最后一条助手消息 --- //
      let messagesArray = []
      if (this.editingType === 'topic') {
        messagesArray = this.topicMessages
      } else if (this.editingType === 'text') {
        messagesArray = this.textMessages
      } else if (this.editingType === 'file') { // 新增
        messagesArray = this.fileMessages
      }

      if (messagesArray.length > 0) {
        // 寻找最后一条助手消息
        let lastAssistantIndex = -1
        for (let i = messagesArray.length - 1; i >= 0; i--) {
          if (messagesArray[i].role === 'assistant') {
            lastAssistantIndex = i
            break
          }
        }

        if (lastAssistantIndex !== -1) {
          // 如果找到，更新内容
          if (messagesArray[lastAssistantIndex].content !== updatedMarkdownContent) {
            console.log(`[v1_index] Updating last assistant message content (title edit) at index ${lastAssistantIndex} for type ${this.editingType}`)
            this.$set(messagesArray[lastAssistantIndex], 'content', updatedMarkdownContent)
          }
        } else {
          console.warn(`[v1_index] handleTitleUpdate: No assistant message found to update for type ${this.editingType}`)
        }
      } else {
        console.warn(`[v1_index] handleTitleUpdate: Message array is empty for type ${this.editingType}`)
      }
      // --- 修复结束 --- //

      // 始终更新对应的 finalMarkdown
      if (this.editingType === 'topic') {
        console.log('[v1_index] Syncing updated title to topicFinalMarkdown.')
        this.topicFinalMarkdown = updatedMarkdownContent
      } else if (this.editingType === 'text') {
        console.log('[v1_index] Syncing updated title to textFinalMarkdown.')
        this.textFinalMarkdown = updatedMarkdownContent
      } else if (this.editingType === 'file') { // 新增
        console.log('[v1_index] Syncing updated title to fileFinalMarkdown.')
        this.fileFinalMarkdown = updatedMarkdownContent
      }

      // 始终更新传递给编辑器的 prop 以保持一致性
      this.generatedMarkdownContent = updatedMarkdownContent
    },

    // --- 添加 startEditLastAssistantMessage 方法 (包含 file 类型) ---
    startEditLastAssistantMessage(inputType) {
      console.log(`[startEditLastAssistantMessage] Called for type: ${inputType}`)
      let messagesArray = []
      if (inputType === 'topic') {
        messagesArray = this.topicMessages
      } else if (inputType === 'text') {
        messagesArray = this.textMessages
      } else if (inputType === 'file') { // 新增
        messagesArray = this.fileMessages
      } else {
        console.warn('[startEditLastAssistantMessage] Invalid input type')
        return
      }

      if (messagesArray.length === 0) {
        console.warn(`[startEditLastAssistantMessage] No messages found for type: ${inputType}`)
        return
      }

      // 寻找最后一条助手消息
      let lastAssistantIndex = -1
      for (let i = messagesArray.length - 1; i >= 0; i--) {
        if (messagesArray[i].role === 'assistant') {
          lastAssistantIndex = i
          break
        }
      }

      if (lastAssistantIndex !== -1) {
        const messageToEdit = messagesArray[lastAssistantIndex]
        console.log(`[startEditLastAssistantMessage] Found last assistant message at index ${lastAssistantIndex}.`)

        // 设置编辑状态
        this.editingMessageInfo = { type: inputType, index: lastAssistantIndex }
        this.generatedMarkdownContent = messageToEdit.content // 加载内容到编辑器
        this.editingType = inputType // 设置当前编辑模式
        this.showOutlineEditor = true // 显示编辑器
      } else {
        console.warn(`[startEditLastAssistantMessage] No assistant message found for type: ${inputType}`)
      }
    },

    // --- 添加 handleTopicSubmit 方法 ---
    async handleTopicSubmit() {
      this.editingMessageInfo = { type: null, index: -1 }
      if (this.isTopicGenerating) return
      if (!this.topicPrompt.trim()) {
        this.$notify.warning('请输入主题描述')
        return
      }

      // 使用当前输入内容，但不清空输入框
      const currentPrompt = this.topicPrompt

      this.isTopicGenerating = true
      try {
        const taskId = await this.handleCreateTask('topic', currentPrompt)
        if (taskId) {
          await this.generateContent('topic')
        }
      } catch (error) {
        console.error('Error in topic submission flow:', error)
        this.isTopicGenerating = false
      }
    },

    // --- 添加 handleTextSubmit 方法 ---
    async handleTextSubmit() {
      this.editingMessageInfo = { type: null, index: -1 }
      if (this.isTextGenerating) return
      if (!this.textContent.trim()) {
        this.$notify.warning('请输入文本内容')
        return
      }

      // 使用当前输入内容，但不清空输入框
      const currentContent = this.textContent

      this.isTextGenerating = true
      try {
        const taskId = await this.handleCreateTask('text', currentContent)
        if (taskId) {
          await this.generateContent('text')
        }
      } catch (error) {
        console.error('Error in text submission flow:', error)
        this.isTextGenerating = false
      }
    },

    // --- 添加 handleAiPptGeneratePptx 方法 (包含 file 类型) ---
    async handleAiPptGeneratePptx() {
      console.log('[handleAiPptGeneratePptx] Called. Current state:', {
        editingType: this.editingType,
        topicTaskId: this.topicTaskId,
        topicFinalMarkdown: this.topicFinalMarkdown ? this.topicFinalMarkdown.substring(0, 50) + '...' : '',
        textTaskId: this.textTaskId,
        textFinalMarkdown: this.textFinalMarkdown ? this.textFinalMarkdown.substring(0, 50) + '...' : '',
        fileTaskId: this.fileTaskId, // 新增
        fileFinalMarkdown: this.fileFinalMarkdown ? this.fileFinalMarkdown.substring(0, 50) + '...' : '', // 新增
        templateId: this.form.templateId
      })

      if (!this.form.templateId) {
        this.$notify.warning('请先选择一个模板')
        return
      }

      let taskId = ''
      let markdown = ''

      // 根据 editingType 确定使用哪个 taskId 和 markdown
      if (this.editingType === 'topic') {
        taskId = this.topicTaskId
        markdown = this.topicFinalMarkdown
        console.log('[handleAiPptGeneratePptx] Using TOPIC data:', { taskId, markdownLength: markdown?.length })
      } else if (this.editingType === 'text') {
        taskId = this.textTaskId
        markdown = this.textFinalMarkdown
        console.log('[handleAiPptGeneratePptx] Using TEXT data:', { taskId, markdownLength: markdown?.length })
      } else if (this.editingType === 'file') { // 新增
        taskId = this.fileTaskId
        markdown = this.fileFinalMarkdown
        console.log('[handleAiPptGeneratePptx] Using FILE data:', { taskId, markdownLength: markdown?.length })
      } else {
        console.error('[handleAiPptGeneratePptx] Invalid editingType:', this.editingType)
        this.$notify.error('无法确定要生成的PPT内容来源')
        return
      }

      if (!taskId || !markdown || !markdown.trim()) {
        this.$notify.warning('缺少有效的任务ID或Markdown内容')
        return
      }

      this.isSubmittingPpt = true
      this.btnLoad = true

      const data = {
        'id': taskId,
        'templateId': this.form.templateId,
        'markdown': markdown
      }

      try {
        let headers = null
        if (!getToken() && this.apiKey) {
          headers['apiKey'] = this.apiKey
        }
        const res = await aiPptGeneratePptx(data, headers)
        console.log('生成PPT响应:', res)
        this.isAnimation = true
        if (res && res.code === 200 && res.data && res.data.id) {
          if (res.data.error) {
            this.$notify({ title: '生成通知', message: res.data.error, type: 'warning', duration: 4000 })
            this.isGenerateSuccess = false
          } else {
            this.currentPptId = res.data.id
            this.pptUrl = res.data.pptPath
            this.pptName = res.data.pptName

            setTimeout(() => {
              this.isAnimation = false
            }, 3000)
          }
        } else {
          this.$notify({
            title: '错误',
            message: res?.data?.msg || res?.data?.error || '生成失败，服务器未返回有效信息',
            type: 'error',
            duration: 6000
          })
          this.isGenerateSuccess = false
          this.isAnimation = false
        }
      } catch (error) {
        console.error('生成PPT错误:', error)
        this.$notify({ title: '错误', message: '生成PPT请求失败', type: 'error', duration: 4000 })
        this.isGenerateSuccess = false
        this.isAnimation = false
      } finally {
        this.isSubmittingPpt = false
        this.btnLoad = false
      }
    },

    // --- 新增：处理文件选择变化 --- //
    handleFileChange(file, fileList) {
      console.log('[handleFileChange] File:', file, 'FileList:', fileList)

      // 确保 fileList 不为空且有有效的 file 对象
      if (!fileList || fileList.length === 0 || !file || !file.raw) {
        console.warn('[handleFileChange] Invalid file or fileList.')
        this.handleRemove() // 清理状态
        return
      }

      // Element UI 的 on-change 会在添加和移除时都触发，我们需要确保只处理添加的情况
      // 并且由于 limit=1，理论上 fileList 长度最多为 1
      // 获取最后一个文件，即当前操作的文件
      const currentFile = fileList[fileList.length - 1]
      const rawFile = currentFile.raw

      // 1. 文件大小校验
      if (rawFile.size > this.uploadSingleFileMaxSize * 1024 * 1024) {
        this.$notify.error({
          title: '文件过大',
          message: `上传文件大小不能超过 ${this.uploadSingleFileMaxSize}MB！当前文件大小约为 ${this.formatFileSize(rawFile.size)}。`
        })
        // 从列表中移除不符合要求的文件并清理状态
        this.handleRemove()
        return // 阻止后续处理
      }

      // 2. 文件类型校验 (可以在 accept 属性基础上做更严格的检查，如果需要)
      // const allowedTypes = ['text/plain', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/markdown'];
      // if (!allowedTypes.includes(rawFile.type)) {
      //   this.$notify.error('不支持的文件类型！');
      //   this.handleRemove();
      //   return;
      // }

      // 校验通过，只保留当前选择的文件
      this.fileList = [currentFile] // 保留当前文件对象（包含name, size, uid等）
      this.selectedFile = rawFile   // 存储原始 File 对象，用于后续上传或处理
      console.log('[handleFileChange] Selected file stored:', this.selectedFile)
    },

    // --- 新增：处理文件提交 --- //
    async handleFileSubmit() {
      if (!this.selectedFile) {
        this.$notify.warning('请先选择一个文件')
        return
      }
      if (this.isFileGenerating) {
        return // 防止重复提交
      }

      console.log(`[handleFileSubmit] Starting process for file: ${this.selectedFile.name}`)
      this.isFileGenerating = true // 开始加载状态（按钮 loading）
      try {
        // 1. 创建任务，传入文件对象
        const taskId = await this.handleCreateTask('file', this.selectedFile)

        if (taskId) {
          // 2. 添加用户消息
          this.fileMessages.push({
            role: 'user',
            content: `开始处理文件 '${this.selectedFile.name}' 并生成大纲...`,
            timestamp: new Date().getTime()
          })
          this.scrollToBottom()

          // 3. 调用内容生成
          await this.generateContent('file')
        } else {
          console.error('[handleFileSubmit] Failed to create task.')
          // handleCreateTask 内部会提示错误，可能需要重置 isFileGenerating
          this.isFileGenerating = false
        }
      } catch (error) {
        console.error('[handleFileSubmit] Error:', error)
        // generateContent 内部的 finally 会处理 isFileGenerating
        // 但如果 handleCreateTask 失败且未 catch，这里可能需要重置
        this.isFileGenerating = false // 再次确保重置
      }
    },

    // --- 新增：格式化文件大小 ---
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // --- 新增：处理一句话建议的悬停和选择逻辑 ---
    /** 鼠标进入一句话输入区域 */
    handleTopicMouseEnter() {
      // console.log('Mouse entered topic input area');
      this.isMouseInInputArea = true

      // 清除可能存在的延迟隐藏计时器
      if (this.hideDelayTimeout) {
        clearTimeout(this.hideDelayTimeout)
        this.hideDelayTimeout = null
      }

      // 清除可能存在的旧显示计时器
      if (this.topicHoverTimeout) {
        clearTimeout(this.topicHoverTimeout)
        this.topicHoverTimeout = null
      }

      // 设置延迟后显示建议的计时器
      this.topicHoverTimeout = setTimeout(() => {
        // console.log('Timeout triggered, showing suggestions');
        this.calculateSuggestionPosition() // 计算fixed定位的位置
        this.showTopicSuggestions = true
      }, 500)
    },

    /** 鼠标离开一句话输入区域 */
    handleTopicMouseLeave() {
      // console.log('Mouse left topic input area');
      this.isMouseInInputArea = false

      // 清除显示计时器（无论是否已触发）
      if (this.topicHoverTimeout) {
        clearTimeout(this.topicHoverTimeout)
        this.topicHoverTimeout = null
      }

      // 延迟检查是否需要隐藏提示框
      this.scheduleHideCheck()
    },

    /** 鼠标进入提示框区域 */
    handleSuggestionBoxMouseEnter() {
      // console.log('Mouse entered suggestion box');
      this.isMouseInSuggestionBox = true

      // 清除可能存在的延迟隐藏计时器
      if (this.hideDelayTimeout) {
        clearTimeout(this.hideDelayTimeout)
        this.hideDelayTimeout = null
      }
    },

    /** 鼠标离开提示框区域 */
    handleSuggestionBoxMouseLeave() {
      // console.log('Mouse left suggestion box');
      this.isMouseInSuggestionBox = false

      // 延迟检查是否需要隐藏提示框
      this.scheduleHideCheck()
    },

    /** 安排隐藏检查 - 统一的延迟隐藏逻辑 */
    scheduleHideCheck() {
      // 清除之前的延迟隐藏计时器
      if (this.hideDelayTimeout) {
        clearTimeout(this.hideDelayTimeout)
        this.hideDelayTimeout = null
      }

      // 设置新的延迟隐藏计时器
      this.hideDelayTimeout = setTimeout(() => {
        // 检查鼠标是否完全离开了联合区域
        if (!this.isMouseInInputArea && !this.isMouseInSuggestionBox) {
          this.showTopicSuggestions = false
        }
      }, 250) // 250ms延迟，给用户足够时间移动鼠标
    },

    /** 计算建议框位置 - Fixed定位版本 */
    calculateSuggestionPosition() {
      this.$nextTick(() => {
        const inputWrapper = this.$el.querySelector('.input-wrapper')
        if (!inputWrapper) return

        const inputRect = inputWrapper.getBoundingClientRect()

        // 计算建议框的固定位置
        const suggestionBoxHeight = 380
        const suggestionBoxWidth = 350

        // 在输入框上方15px处显示，左对齐
        const top = inputRect.top - suggestionBoxHeight - 15
        const left = inputRect.left

        console.log('建议框Fixed位置计算:', {
          inputRect,
          top,
          left,
          suggestionBoxHeight,
          suggestionBoxWidth
        })

        this.suggestionBoxStyle = {
          top: `${Math.max(10, top)}px`, // 确保不超出屏幕顶部
          left: `${left}px`,
          width: `${suggestionBoxWidth}px`,
          height: `${suggestionBoxHeight}px`
        }
      })
    },

    /** 处理窗口大小变化 */
    handleWindowResize() {
      // 如果建议框正在显示，重新计算位置
      if (this.showTopicSuggestions) {
        this.calculateSuggestionPosition()
      }
    },

    /** 用户点击建议项 */
    selectTopicSuggestion(suggestion) {
      // console.log('Suggestion selected:', suggestion);
      this.topicPrompt = suggestion // 填充输入框
      this.showTopicSuggestions = false // 隐藏浮窗

      // 清理所有相关状态和计时器
      this.isMouseInInputArea = false
      this.isMouseInSuggestionBox = false

      if (this.topicHoverTimeout) {
        clearTimeout(this.topicHoverTimeout)
        this.topicHoverTimeout = null
      }

      if (this.hideDelayTimeout) {
        clearTimeout(this.hideDelayTimeout)
        this.hideDelayTimeout = null
      }

      // 可选：让输入框获取焦点
      this.$nextTick(() => {
        // this.$refs.form.querySelector('.topic-input-wrapper textarea')?.focus();
        this.$refs.form.$el.querySelector('.topic-input-wrapper textarea')?.focus()
      })
    },
    // --- 建议逻辑结束 ---

    handleOpenHistoryPpt(item) {
      this.pptEditLoadingText = 'PPT加载中...'

      // 设置要编辑的PPT ID
      this.currentPptId = item.id
      // 确保动画设置为 false
      this.isAnimation = false
      // 假设当前的 aiPptToken 仍然有效，PptEditor 会使用它
    },
    async handleDeleteHistoryPpt(item) {
      // console.log('删除历史记录PPT, ID:', id); // Using item now
      this.$confirm('确定要删除这条历史记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        // 构建请求数据
        const data = {
          'id': item.id,
          'pptToken': await this.handleCreateApiToken()
          // 如果后端需要 pptToken 进行验证，也需要加上
          // "pptToken": this.aiPptToken
        }
        // 调用删除接口，注意第二个参数的结构
        aiPptDelete(data, { apiKey: this.apiKey }) // Pass apiKey correctly
            .then(res => {
              if (res.code === 200) {
                this.$notify.success({ message: '删除成功' })
                // 刷新列表，需要考虑当前页码，如果删除的是最后一页的唯一项，页码可能需要减1
                // 简单处理：直接刷新当前页或回到第一页
                // this.pptHistoryQueryParams.page = 1; // Or keep current page
                this.handleAiPptListPptx() // Refresh the list
              } else {
                this.$message.error(res.msg || '删除失败')
              }
            })
            .catch(err => {
              console.error('删除历史记录 API 调用失败:', err)
              this.$message.error('删除请求失败')
            })
      }).catch(() => {
        // 用户点击了取消
        this.$message.info('已取消删除')
      })
    },
    // --- 占位符结束 ---
    // 新增：处理创建新模板的点击事件
    handleCreateNewTemplate() {
      // this.$message.info('创建新模板功能正在开发中...');
      // 后续可以跳转到模板创建页面或打开创建对话框
      this.showTemplateCreationView = true // 4. 修改方法以显示新视图
    },

    /** 处理Tab点击 */
    handleTabClick(tab, event) {
      console.log(`切换到 Tab: ${tab.name}`)
      this.activeName = tab.name
      if (tab.name === 'history') {
        this.getList()
      } else if (tab.name === 'customTemplate') {
        this.getCustomTemplateList()
      }
    },

    /** 获取自定义模板列表 */
    async getCustomTemplateList() {
      this.customTemplateLoading = true
      const params = {
        page: this.customTemplateParams.pageNum,
        size: this.customTemplateParams.pageSize,
        filters: { type: 4 }, // 查询用户自定义模板
        pptToken: await this.handleCreateApiToken()
      }
      getAiPptTemplates(params, { apiKey: this.apiKey }).then(response => {
        this.customTemplateList = response.rows || []
        this.customTemplateTotal = response.total || 0
        this.customTemplateLoading = false
      }).catch(error => {
        console.error('获取自定义模板列表失败:', error)
        this.$message.error('获取自定义模板列表失败')
        this.customTemplateLoading = false
      })
    },

    /** 处理自定义模板分页变化 */
    handleCustomTemplatePageChange(page) {
      this.customTemplateParams.pageNum = page
      this.getCustomTemplateList()
    },

    /** 开始编辑模板名称 */
    handleStartEditName(item) {
      // 设置编辑状态
      this.$set(item, 'isEditing', true)
      this.$set(item, 'editingName', item.name || '')

      // 下一个tick后聚焦输入框
      this.$nextTick(() => {
        // 查找对应的输入框并聚焦
        const inputs = this.$refs.nameInput
        if (inputs) {
          const inputArray = Array.isArray(inputs) ? inputs : [inputs]
          const targetInput = inputArray.find(input => input.$el)
          if (targetInput) {
            targetInput.focus()
            targetInput.select()
          }
        }
      })
    },

    /** 处理模板名称输入框失去焦点 */
    handleBlurEditName(item) {
      this.handleSaveEditName(item)
    },

    /** 保存编辑的模板名称 */
    async handleSaveEditName(item) {
      const newName = item.editingName?.trim()

      // 如果名称没有变化，直接取消编辑
      if (newName === item.name) {
        this.handleCancelEditName(item)
        return
      }

      // 如果名称为空，提示用户
      if (!newName) {
        this.$message.warning('模板名称不能为空')
        // 恢复原始名称
        item.editingName = item.name || ''
        return
      }

      try {
        // 调用接口更新模板名称
        const data = {
          'id': item.id,
          'name': newName,
          'pptToken': await this.handleCreateApiToken()
        }
        await templatesNameUpdateApi(data).then(res => {
          if (res.code === 200 && res.data) {
            createNotification('模板命名更新成功', CreateNotificationType.SUCCESS)
            // 更新本地数据
            item.name = newName
          }
        })
      } catch (error) {
        console.error('更新模板名称失败:', error)
        this.$message.error('更新模板名称失败')
      } finally {
        // 退出编辑状态
        this.$set(item, 'isEditing', false)
        this.$delete(item, 'editingName')
        this.$delete(item, 'isCancelling')
      }
    },

    /** 取消编辑模板名称 */
    handleCancelEditName(item) {
      this.$set(item, 'isEditing', false)
      this.$delete(item, 'editingName')
    },

    /** 开始编辑历史记录PPT名称 */
    handleStartEditHistoryName(item) {
      // 设置编辑状态
      this.$set(item, 'isEditing', true)
      this.$set(item, 'editingName', item.subject || '')

      // 下一个tick后聚焦输入框
      this.$nextTick(() => {
        // 查找对应的输入框并聚焦
        const inputs = this.$refs.historyNameInput
        if (inputs) {
          const inputArray = Array.isArray(inputs) ? inputs : [inputs]
          const targetInput = inputArray.find(input => input.$el)
          if (targetInput) {
            targetInput.focus()
            targetInput.select()
          }
        }
      })
    },

    /** 处理历史记录PPT名称输入框失去焦点 */
    handleBlurEditHistoryName(item) {
      this.handleSaveEditHistoryName(item)
    },

    /** 保存编辑的历史记录PPT名称 */
    async handleSaveEditHistoryName(item) {
      const newName = item.editingName?.trim()

      // 如果名称没有变化，直接取消编辑
      if (newName === item.subject) {
        this.handleCancelEditHistoryName(item)
        return
      }

      // 如果名称为空，提示用户
      if (!newName) {
        this.$message.warning('PPT名称不能为空')
        // 恢复原始名称
        item.editingName = item.subject || ''
        return
      }

      try {
        // 调用接口更新PPT名称
        const data = {
          'id': item.id,
          'name': newName,
          'subject': newName,
          'pptToken': await this.handleCreateApiToken()
        }
        await updatePptxAttrApi(data, { apiKey: this.apiKey }).then(res => {
          if (res.code === 200 && res.data) {
            // 更新本地数据
            item.name = newName
            item.subject = newName
            createNotification('命名成功', CreateNotificationType.SUCCESS)
          }
        })

      } catch (error) {
        console.error('更新PPT名称失败:', error)
        createNotification('命名失败', CreateNotificationType.ERROR)
        return
      } finally {
        // 退出编辑状态
        this.$set(item, 'isEditing', false)
        this.$delete(item, 'editingName')
        this.$delete(item, 'isCancelling')
      }
    },

    /** 取消编辑历史记录PPT名称 */
    handleCancelEditHistoryName(item) {
      this.$set(item, 'isEditing', false)
      this.$delete(item, 'editingName')
    },

    /** 删除自定义模板 */
    async handleDeleteCustomTemplate(row) {
      this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        console.log('准备删除模板:', row.id) // 使用哪个ID需要确认
        // 调用实际的删除 API
        const data = {
          'id': row.id,
          'pptToken': await this.handleCreateApiToken()
        }
        aiPptDelTemplateId(data, { apiKey: this.apiKey }).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getCustomTemplateList()
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        }).catch(err => {
          console.error('删除模板失败:', err)
          this.$message.error('删除失败，请稍后重试')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    /** 获取历史记录列表 */
    getList() {
      // ... existing getList method ...
    },

    /**
     * 打开标注视图
     * @param {object} item 模板项
     */
    async handleOpenMarking(item) {
      this.markingUrl = `https://docmee.cn/marker/${item.id}?token=${await this.handleCreateApiToken()}`
      this.showMarkingView = true
    },
    /**
     * 加载编辑器 进行模板编辑
     * @param {object} item 模板项
     */
    async handleEditTemplate(item) {
      this.currentTemplateId = item.id
      this.isAnimation = false
    },

    handleShowTemplateCreationView() {
      this.showTemplateCreationView = false
      this.getCustomTemplateList()

    },

    /**
     * 新增：处理从 OutlineEditor 触发的下一步事件
     */
    handleGoNextFromEditor() {
      console.log('[v1_index] Received request-next-step from OutlineEditor.')
      // 在进入下一步之前，确保编辑器已关闭（否则视觉上会重叠）
      this.showOutlineEditor = false
      // 调用现有的下一步逻辑
      this.goNextStep()
    },

    // --- 新增：处理演示场景切换 --- //
    handleDemoSceneChange(scene) {
      this.selectedDemoScene = scene
      this.selectedDemoSceneValue = scene
      console.log('演示场景已切换为:', scene)
      // 后续可以在这里添加根据场景调整其他参数的逻辑
    },

    // --- 处理语言切换 --- //
    handleLanguageChange(lang) {
      this.selectedLanguage = lang
      console.log('语言已切换为:', lang)
    },

    // --- 处理模型切换 --- //
    handleModelChange(model) {
      this.selectedChatModel = model
      this.selectedChatModelValue = model
      console.log('模型已切换为:', model)
    },

    handleAuthenticateLoginAccess() {
      try {
        const inFiveMinutes = new Date()
        inFiveMinutes.setTime(inFiveMinutes.getTime() + (60 * 1000)) // 当前时间加 1 分钟（单位：毫秒）
        getCasEnter().then(res => {
          if (res.code === 200 && res.data) {
            console.log(res.data)
            console.log('cas enter' + res.data[0].value)
            Cookies.set('cas_enter_ppt_v1_uid', this.handleUrlParam('uid'), { expires: inFiveMinutes })
            Cookies.set('route_url_label', '/pptV1', { expires: inFiveMinutes })
            window.location.href = res.data[0].value
          }

        })
      } catch (e) {
        this.$notify.error({ title: '错误', message: e.message })
      }
    },

    // 预览图片
    previewImage(src) {
      console.log('预览图片:', src)
      // 创建一个临时的图片预览元素
      const previewContainer = document.createElement('div')
      previewContainer.className = 'image-preview-container'
      previewContainer.style.cssText = 'position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.7);z-index:9999;display:flex;align-items:center;justify-content:center;'

      const img = document.createElement('img')
      img.src = src
      img.style.cssText = 'max-width:90%;max-height:90%;object-fit:contain;box-shadow:0 0 20px rgba(0,0,0,0.5);'

      const closeBtn = document.createElement('div')
      closeBtn.textContent = '×'
      closeBtn.style.cssText = 'position:absolute;top:20px;right:20px;color:white;font-size:30px;cursor:pointer;'

      previewContainer.appendChild(img)
      previewContainer.appendChild(closeBtn)
      document.body.appendChild(previewContainer)

      // 点击关闭预览
      previewContainer.addEventListener('click', () => {
        document.body.removeChild(previewContainer)
      })
    },

    closeTemplateCreation() {
      this.showTemplateCreationView = false
      this.getCustomTemplateList()
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/variables.scss";

// 定义主题色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 现代化渐变色
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); // 替换为蓝青色渐变
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);

// 定义响应式断点
$screen-xs: 480px;
$screen-sm: 768px;
$screen-md: 992px;
$screen-lg: 1200px;

// 定义动画时长
$transition-duration: 0.3s;
$transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);

// ===== 现代化样式开始 =====

// 现代化主容器
.modern-aippt-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  width: 100%;
  border-radius: 5px;
}

// 现代化背景
.modern-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;

  .gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--theme-gradient, linear-gradient(135deg,
        rgba(26, 206, 233, 0.60) 0%, /* 青蓝 */
        rgba(64, 160, 222, 0.70) 10%, /* 蓝紫中转 */
        rgba(100, 100, 216, 0.70) 20%, /* 蓝紫 */
        rgba(123, 35, 211, 0.70) 30%, /* 紫色 */
        rgba(150, 20, 205, 0.60) 40%, /* 深紫 */
        rgba(176, 10, 199, 0.50) 50%, /* 品红紫 */
        rgba(168, 22, 130, 0.60) 60%, /* 紫红过渡 */
        rgba(160, 33, 52, 0.40) 70%, /* 暖红色 */
        rgba(80, 90, 190, 0.60) 85%, /* 紫蓝拉回 */
        rgba(5, 135, 250, 0.80) 100% /* 冷蓝收尾 */
    ));
    border-radius: 5px;

    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    transition: background 0.6s ease-in-out; /* 添加过渡动画 */
  }

  .floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 20s infinite linear;

      &.shape-1 {
        width: 300px;
        height: 300px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 200px;
        height: 200px;
        top: 60%;
        right: 10%;
        animation-delay: -7s;
      }

      &.shape-3 {
        width: 150px;
        height: 150px;
        bottom: 20%;
        left: 60%;
        animation-delay: -14s;
      }
    }
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(30px) rotate(240deg);
  }
}

// 现代化认证提示
.modern-auth-prompt {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .auth-card {
    background: $gradient-card;
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 48px 40px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 480px;
    width: 90%;
    animation: slideInUp 0.6s $transition-smooth;

    .auth-icon {
      width: 80px;
      height: 80px;
      background: $gradient-primary;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;

      i {
        font-size: 36px;
        color: white;
      }
    }

    .auth-title {
      font-size: 28px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 12px;
    }

    .auth-description {
      font-size: 16px;
      color: #7f8c8d;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .auth-button {
      padding: 16px 32px;
      font-size: 16px;
      border-radius: 12px;
      background: $gradient-primary;
      border: none;
      margin-bottom: 24px;
      transition: all $transition-duration $transition-smooth;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }
    }

    .auth-tips {
      .tip-item {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        color: #7f8c8d;
        font-size: 14px;

        i {
          color: $success-color;
          margin-right: 8px;
        }
      }
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 现代化主内容区域
.modern-main-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 现代化头部
.modern-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    width: 100%;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    .header-center {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 步骤指示器
.step-indicator {
  display: flex;
  align-items: center;

  .step-item {
    display: flex;
    align-items: center;
    position: relative;

    .step-number {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #e9ecef;
      color: #6c757d;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
      transition: all $transition-duration $transition-smooth;
      margin-right: 8px;
    }

    .step-label {
      font-size: 14px;
      color: #6c757d;
      font-weight: 500;
      transition: all $transition-duration $transition-smooth;
    }

    &.active {
      .step-number {
        background: $gradient-primary;
        color: white;
        transform: scale(1.1);
      }

      .step-label {
        color: #2c3e50;
        font-weight: 600;
      }
    }

    &.completed {
      .step-number {
        background: $success-color;
        color: white;
      }

      .step-label {
        color: $success-color;
      }
    }
  }

  .step-connector {
    width: 40px;
    height: 2px;
    background: #e9ecef;
    margin: 0 16px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: $success-color;
      width: 0;
      transition: width $transition-duration $transition-smooth;
    }
  }
}

// 主内容卡片
.main-content-card {
  flex: 1;
  width: 100%;
  margin: 0;
  padding: 24px;
  backdrop-filter: blur(5px);
  border-radius: 5px;

  .modern-form {
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 5px 20px;
    animation: slideInUp 0.8s $transition-smooth;
    min-height: calc(100vh - 140px);
  }
}

// 现代化步骤容器
.modern-step-container {
  position: relative;

  .modern-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 32px;
      border-bottom: 2px solid #f0f2f5;
    }

    ::v-deep .el-tabs__item {
      padding: 16px 24px;
      font-size: 16px;
      font-weight: 500;
      color: #6c757d;
      border: none;
      border-radius: 12px 12px 0 0;
      transition: all $transition-duration $transition-smooth;

      &.is-active {
        color: $primary-color;
        font-weight: 600;
      }

      &:hover {
        color: $primary-color;
        background: rgba(64, 158, 255, 0.05);
      }

      .tab-label {
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          font-size: 18px;
        }
      }
    }

    ::v-deep .el-tabs__active-bar {
      background: $primary-color;
      height: 3px;
      border-radius: 2px;
    }
  }
}

// 创作欢迎区域
.creation-welcome {
  text-align: center;
  margin-bottom: 24px;

  .welcome-title {
    font-size: 20px;
    font-weight: 600;
    background: $gradient-primary;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    animation: slideInUp 0.6s $transition-smooth;
  }

  .welcome-subtitle {
    font-size: 14px;
    color: #7f8c8d;
    line-height: 1.5;
    max-width: 500px;
    margin: 0 auto;
    animation: slideInUp 0.8s $transition-smooth;
  }
}

// 创作方式卡片
.creation-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .method-card {
    background: $gradient-card;
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 20px 16px;
    text-align: center;
    cursor: pointer;
    transition: all $transition-duration $transition-smooth;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    animation: slideInUp 1s $transition-smooth;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: $gradient-primary;
      opacity: 0;
      transition: opacity $transition-duration $transition-smooth;
      z-index: -1;
    }

    .method-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      transition: all $transition-duration $transition-smooth;

      i {
        font-size: 24px;
        color: #6c757d;
        transition: all $transition-duration $transition-smooth;
      }
    }

    .method-content {
      .method-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        transition: all $transition-duration $transition-smooth;
      }

      .method-desc {
        font-size: 12px;
        color: #7f8c8d;
        line-height: 1.5;
        margin-bottom: 12px;
        transition: all $transition-duration $transition-smooth;
      }

      .method-features {
        display: flex;
        justify-content: center;
        gap: 8px;

        .feature-tag {
          background: rgba(64, 158, 255, 0.1);
          color: $primary-color;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          transition: all $transition-duration $transition-smooth;
        }
      }
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      border-color: rgba(64, 158, 255, 0.3);

      .method-icon {
        background: $gradient-primary;
        transform: scale(1.1);

        i {
          color: white;
        }
      }

      .method-content {
        .method-title {
          color: $primary-color;
        }

        .method-features .feature-tag {
          background: rgba(64, 158, 255, 0.2);
        }
      }
    }

    &.active {
      border-color: $primary-color;
      background: linear-gradient(145deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.02) 100%);

      &::before {
        opacity: 0.05;
      }

      .method-icon {
        background: $gradient-primary;

        i {
          color: white;
        }
      }

      .method-content {
        .method-title {
          color: $primary-color;
        }

        .method-features .feature-tag {
          background: $primary-color;
          color: white;
        }
      }
    }

    &:disabled,
    &[disabled] {
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// 响应式调整
@media (max-width: $screen-md) {
  .modern-header .header-content {
    padding: 0 16px;

    .step-indicator {
      display: none;
    }
  }

  .main-content-card {
    padding: 16px;
    margin: 12px;

    .modern-form {
      padding: 24px;
      border-radius: 16px;
    }
  }

  .creation-welcome {
    .welcome-title {
      font-size: 24px;
    }

    .welcome-subtitle {
      font-size: 16px;
    }
  }

  .creation-methods {
    grid-template-columns: 1fr;
    gap: 16px;

    .method-card {
      padding: 24px 20px;

      .method-icon {
        width: 64px;
        height: 64px;

        i {
          font-size: 28px;
        }
      }
    }
  }
}

// 现代化聊天区域样式
.modern-chat-container {
  margin-bottom: 32px;
  border-radius: 16px; // 圆角
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); // 阴影
  border: 1px solid #e8e8e8; // 边框
  padding: 20px; // 内边距

  .chat-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    .chat-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333;

      i {
        font-size: 18px;
        color: #409EFF;
      }
    }
  }

  .chat-messages {
    max-height: 500px;
    overflow-y: auto;
    padding: 16px;
    border-radius: 16px;
    backdrop-filter: blur(10px);

    .modern-message-item {
      display: flex;
      margin-bottom: 24px;
      animation: slideInUp 0.4s $transition-smooth;

      &.message-user {
        flex-direction: row-reverse;

        .message-avatar .avatar-icon {
          background: $gradient-primary;
          color: white;
        }

        .message-bubble {
          background: $gradient-primary;
          color: white;
          margin-right: 12px;
          margin-left: 0;

          .user-message {
            color: white;
            font-weight: 500;
          }
        }
      }

      &.message-assistant {
        .message-avatar .avatar-icon {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          color: #6c757d;
        }

        .message-bubble {
          color: #2c3e50;
          margin-left: 12px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
      }

      &.generating {
        .message-avatar .avatar-icon.generating-avatar {
          background: $gradient-success;
          color: white;
          animation: pulse 2s infinite;
        }
      }

      .message-avatar {
        flex-shrink: 0;

        .avatar-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          transition: all $transition-duration $transition-smooth;
        }
      }

      .message-bubble {
        max-width: 70%;
        padding: 16px 20px;
        border-radius: 18px;
        position: relative;
        transition: all $transition-duration $transition-smooth;

        .message-content {
          line-height: 1.6;

          .assistant-message {
            color: inherit;
          }
        }

        .message-actions {
          margin-top: 12px;
          text-align: right;

          .edit-button {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 8px;
            opacity: 0.7;
            transition: all $transition-duration $transition-smooth;

            &:hover {
              opacity: 1;
              background: rgba(64, 158, 255, 0.1);
            }
          }
        }

        .modern-typing-indicator {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 8px;

          span {
            width: 8px;
            height: 8px;
            background: $success-color;
            border-radius: 50%;
            animation: typing-dot-bounce 1.4s infinite ease-in-out both;

            &:nth-child(1) {
              animation-delay: -0.32s;
            }

            &:nth-child(2) {
              animation-delay: -0.16s;
            }

            &:nth-child(3) {
              animation-delay: 0s;
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes typing-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// 现代化输入区域样式
.modern-input-section {
  .input-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .main-input-area {
      display: flex;
      gap: 16px;
      align-items: flex-end;
      margin-bottom: 24px;

      .input-wrapper {
        flex: 1;
        position: relative; // 为建议框提供定位上下文

        .modern-input {
          ::v-deep .el-input-group__prepend {
            background: $gradient-primary;
            border: none;
            color: white;
            border-radius: 12px 0 0 12px;

            .input-icon {
              font-size: 18px;
            }
          }

          ::v-deep .el-input__inner {
            border: 2px solid #f0f2f5;
            border-radius: 0 12px 12px 0;
            padding: 16px 20px;
            font-size: 16px;
            transition: all $transition-duration $transition-smooth;

            &:focus {
              border-color: $primary-color;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
            }

            &::placeholder {
              color: #a0a6b1;
              font-style: italic;
            }
          }

          ::v-deep .el-input__count {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 2px 8px;
            font-size: 12px;
          }
        }
      }

      .quick-actions {
        .create-button {
          padding: 16px 32px;
          border-radius: 12px;
          background: $gradient-primary;
          border: none;
          font-size: 16px;
          font-weight: 600;
          transition: all $transition-duration $transition-smooth;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .advanced-options {
      border-top: 1px solid #f0f2f5;
      padding-top: 24px;

      .options-header {
        margin-bottom: 20px;

        .options-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;

          i {
            margin-right: 8px;
            color: $primary-color;
          }
        }
      }

      .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;

        .option-selector {
          // 选择器样式将由CardSelector组件处理
        }
      }
    }

    .modern-suggestions {
      position: fixed !important; // 使用fixed定位，不受父容器层级限制
      background: white;
      border-radius: 16px;
      border: 1px solid #f0f2f5;
      padding: 20px;
      z-index: 99999 !important; // 超高z-index，确保在Element UI对话框之上
      backdrop-filter: blur(10px); // 添加背景模糊效果

      // 固定位置和尺寸 - 始终向上展示
      // 使用JavaScript动态计算位置，这里只设置基本样式
      width: 350px;
      height: 380px; // 固定高度，足够显示5个项目
      box-shadow: 0 -12px 40px rgba(0, 0, 0, 0.15); // 向上的阴影

      .suggestions-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #2c3e50;

        i {
          margin-right: 8px;
          color: #f39c12;
          font-size: 18px;
        }
      }

      .suggestions-list {
        max-height: 280px; // 5个项目的精确高度 (4*56px + 1*48px + 8px = 280px)
        overflow-y: auto; // 超出时显示滚动条
        padding-right: 4px; // 为滚动条留出空间
        padding-bottom: 4px; // 为最后一个项目添加底部间距

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        .suggestion-item {
          display: flex;
          align-items: center;
          padding: 14px 18px; // 增加内边距
          border-radius: 12px; // 增加圆角
          cursor: pointer;
          transition: all $transition-duration $transition-smooth;
          margin-bottom: 8px; // 增加间距
          background: #f8f9fa; // 添加背景色
          border: 1px solid transparent; // 添加边框

          &:last-child {
            margin-bottom: 0;
          }

          i {
            margin-right: 12px;
            color: #4facfe; // 使用冷色调
            font-size: 14px; // 增大图标
            opacity: 0.8;
            transition: all $transition-duration;
          }

          span {
            font-size: 14px;
            color: #2c3e50;
            line-height: 1.4;
            font-weight: 500; // 增加字重
          }

          &:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #e0f7fa 100%); // 冷色调渐变
            border-color: #4facfe;
            transform: translateX(4px) scale(1.02); // 添加缩放效果
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.2); // 添加阴影

            i {
              opacity: 1;
              color: #00f2fe; // 悬停时更亮的颜色
            }
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: $screen-md) {
  .modern-chat-container .chat-messages .modern-message-item .message-bubble {
    max-width: 85%;
  }

  .modern-input-section .input-card {
    padding: 20px;

    .main-input-area {
      flex-direction: column;
      gap: 16px;

      .quick-actions .create-button {
        width: 100%;
      }
    }

    .advanced-options .options-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .modern-suggestions {
      width: 90%;
      left: 5% !important;
    }
  }
}

// 现代化步骤控制按钮样式
.modern-step-controls {
  position: sticky;
  bottom: 0;
  border-radius: 10px;
  margin-top: 40px;
  z-index: -1;

  .step-controls-container {
    max-width: 100wh;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .controls-left,
    .controls-right {
      flex: 1;
      display: flex;

      .step-button {
        padding: 12px 24px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        transition: all $transition-duration $transition-smooth;
        border: 2px solid transparent;

        &.prev-button {
          background: #f8f9fa;
          color: #6c757d;
          border-color: #e9ecef;

          &:hover:not(:disabled) {
            background: #e9ecef;
            color: #495057;
            transform: translateX(-2px);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        &.next-button {
          background: $gradient-primary;
          border: none;
          color: white;

          &:hover {
            transform: translateX(2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
          }
        }

        &.generate-button {
          background: $gradient-success;
          border: none;
          color: white;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &:disabled {
            opacity: 0.8;
            cursor: not-allowed;
          }
        }

        i {
          font-size: 16px;

          &.el-icon-arrow-left {
            margin-right: 8px;
          }

          &.el-icon-arrow-right {
            margin-left: 8px;
          }

          &.el-icon-magic-stick {
            margin-right: 8px;
          }
        }
      }
    }

    .controls-right {
      justify-content: flex-end;
    }

    .controls-center {
      flex: 0 0 auto;

      .step-progress {
        display: flex;
        flex-direction: column;
        align-items: center;

        .progress-bar {
          width: 100vh;
          height: 6px;
          background: #f0f2f5;
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 8px;

          .progress-fill {
            height: 100%;
            background: $gradient-primary;
            border-radius: 3px;
            transition: width 0.6s $transition-smooth;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
              animation: shimmer 2s infinite;
            }
          }
        }

        .progress-text {
          font-size: 14px;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 响应式调整
@media (max-width: $screen-md) {
  .modern-step-controls {
    .step-controls-container {
      padding: 0 16px;
      flex-direction: column;
      gap: 16px;

      .controls-left,
      .controls-right {
        flex: none;
        width: 100%;
        justify-content: center;
      }

      .controls-center {
        order: -1;

        .step-progress .progress-bar {
          width: 200px;
        }
      }

      .step-button {
        width: 100%;
        max-width: 200px;
      }
    }
  }
}

// 现代化终止生成按钮样式
.modern-abort-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;

  .abort-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 400px;
    text-align: center;

    .abort-content {
      .generating-status {
        margin-bottom: 24px;

        .status-icon {
          width: 60px;
          height: 60px;
          background: $gradient-success;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16px;

          i {
            font-size: 24px;
            color: white;
            animation: spin 1s linear infinite;
          }
        }

        .status-text {
          .status-title {
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
          }

          .status-subtitle {
            display: block;
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
          }
        }
      }

      .modern-abort-btn {
        padding: 12px 24px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 500;
        border: 2px solid #f56c6c;
        color: #f56c6c;
        background: transparent;
        transition: all $transition-duration $transition-smooth;

        i {
          margin-right: 8px;
          font-size: 14px;
        }

        &:hover {
          background: #f56c6c;
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(245, 108, 108, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 淡入淡出缩放动画
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.4s $transition-smooth;
}

.fade-scale-enter,
.fade-scale-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

// 响应式调整
@media (max-width: $screen-md) {
  .modern-abort-container .abort-card {
    min-width: 320px;
    padding: 24px;
    margin: 0 16px;

    .abort-content .generating-status .status-icon {
      width: 50px;
      height: 50px;

      i {
        font-size: 20px;
      }
    }
  }
}

// ===== 现代化样式结束 =====
$screen-xl: 1600px;

// --- 新增：输入框底部工具栏样式 ---
.topic-input-container,
.text-input-container {
  position: relative;
}

.input-box-container {
  margin-bottom: 10px;
  overflow: visible; /* 确保下拉菜单不被截断 */
}

// 输入框独立容器 - 参考文件上传的虚线边框样式
.input-field-wrapper {
  border: 1px dashed #dcdfe6; // 与文件上传一致的灰色虚线边框
  border-radius: 12px;
  background: #fdfdfd; // 与文件上传一致的背景色
  overflow: hidden;
  transition: all 0.3s ease;

  // 悬停效果 - 与文件上传一致
  &:hover {
    border-color: #9b59b6; // 紫色虚线边框
    background-color: #f8fcff; // 与文件上传一致的背景色
  }

  // 聚焦状态 - 保持紫色主题
  &:focus-within {
    border-color: #9b59b6; // 紫色虚线边框
    background-color: #f8fcff;
    box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.15); // 紫色外辉光
  }

  .el-input, .el-textarea {
    .el-input__inner, .el-textarea__inner {
      border: none !important; // 移除输入框自身边框
      border-radius: 12px; // 与容器圆角一致
      background: transparent !important; // 强制透明背景
      transition: all 0.3s ease;
      padding: 20px 24px; // 增大内边距
      font-size: 15px; // 增大字体
      line-height: 1.6; // 增加行高

      &:focus {
        box-shadow: none !important; // 移除输入框自身的聚焦效果
        background: transparent !important; // 强制透明背景
        border: none !important; // 确保聚焦时也无边框
      }

      &:hover {
        background: transparent !important; // 悬停时也保持透明
        border: none !important; // 悬停时也无边框
      }

      &::placeholder {
        color: #9ca3af;
        font-size: 14px;
      }
    }
  }

  // 一句话生成输入框特殊处理
  .el-input {
    .el-input__inner {
      height: 56px !important; // 一句话输入框高度
      line-height: 1.4;
      background: transparent !important; // 确保背景透明
      border: none !important; // 确保无边框
    }
  }

  .content-textarea {
    .el-textarea__inner {
      min-height: 180px !important;
      resize: vertical; // 允许垂直调整大小
      background: transparent !important; // 确保背景透明
      border: none !important; // 确保无边框
      font-size: 15px !important; // 增大字体
      line-height: 1.6 !important; // 增加行高
    }
  }

  // 字数统计样式优化 - 移除背景色
  .el-input__count, .el-textarea__count {
    position: absolute;
    bottom: 8px;
    right: 16px;
    background: transparent !important; // 移除背景色
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    color: #909399;
  }

  // 移动端优化
  @media (max-width: $screen-sm) {
    border-radius: 8px;
    border-width: 2px; // 移动端保持虚线边框，但稍细一点

    .el-input, .el-textarea {
      .el-input__inner, .el-textarea__inner {
        padding: 12px 16px; // 移动端减少内边距
        border-radius: 8px;
        background: transparent !important; // 移动端也保持透明
        border: none !important; // 移动端也无边框
      }
    }
  }
}

// 全局字数统计样式覆盖 - 移除所有背景色
::v-deep .el-input__count,
::v-deep .el-textarea__count {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

// 更强力的全局覆盖 - 确保一句话生成的字数统计也透明
.el-input__count,
.el-textarea__count {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

// 全局样式覆盖，确保输入框样式一致性
.topic-input-container, .text-input-container {
  // 覆盖Element UI默认样式
  ::v-deep .el-input__inner,
  ::v-deep .el-textarea__inner {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;

    &:focus {
      background-color: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }

    &:hover {
      background-color: transparent !important;
      border: none !important;
    }
  }

  // 确保字数统计不受影响 - 移除背景色
  ::v-deep .el-input__count,
  ::v-deep .el-textarea__count {
    background: transparent !important; // 移除背景色
    border: none !important;
    box-shadow: none !important;
    border-radius: 4px;
    padding: 2px 6px;
  }
}

// 特别针对一句话生成输入框的字数统计样式
.topic-input-container {
  ::v-deep .el-input {
    .el-input__count {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      color: #909399 !important;
    }
  }
}

// 工具栏简洁设计 - 无边框，简洁外观
.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 0; // 移除所有内边距
  margin-top: 35px; // 增加与输入框的间距
  background: transparent; // 透明背景
  border: none; // 移除边框
  border-radius: 0; // 移除圆角
  box-shadow: none; // 移除阴影
  transition: all 0.3s ease;

  // 移除悬停效果，保持简洁
  // &:hover { ... } 已移除

  // 移动端优化
  @media (max-width: $screen-sm) {
    margin-top: 20px; // 移动端稍小的间距
  }
}

.selectors-container {
  display: flex;
  flex-wrap: wrap;
  gap: 13px; // 稍微增加间距
  flex: 1;

  @media (max-width: $screen-md) {
    margin-bottom: 16px;
    margin-right: 0;
    width: 100%;
  }
}

.selector-item {
  width: auto;
  flex-grow: 0;
  flex-shrink: 0; // 防止被压缩

  @media (max-width: $screen-sm) {
    width: calc(50% - 6px); // 适应新的gap
  }
}

.create-btn-container {
  display: flex;
  align-items: center;
  flex-shrink: 0; // 防止按钮被压缩
  margin-left: 20px;

  @media (max-width: $screen-md) {
    width: 100%;
    justify-content: center;
  }

  .create-btn {
    padding: 12px 28px; // 稍微增加水平内边距
    border-radius: 12px; // 与整体圆角保持一致
    font-weight: 500;
    font-size: 14px;
    height: 44px; // 固定高度，与选择器保持一致
    background: $gradient-primary !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;

    &:hover {
      box-shadow: 0 6px 16px rgba(64, 158, 255, 0.6) !important;
      transform: translateY(-2px) !important;
    }

    &:active {
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4) !important;
      transform: translateY(1px) !important;
    }

    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.35);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.file-toolbar-container {
  margin-top: 15px;
}

.file-selectors-container {
  margin-top: 15px;
}

.selectors-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 隐藏原始选择器但保留功能 */
.hidden-selectors {
  position: absolute;
  visibility: hidden;
  height: 0;
  width: 0;
  overflow: hidden;
}

// --- 新增样式结束 ---

.app-container {
  overflow-y: auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
  height: 100%;
  padding: 5px 3px;
  cursor: pointer;
  @media (max-width: $screen-sm) {
    padding: 10px;
  }
}

.ck-form {
  width: 95%; // 增加宽度占比，更好地利用屏幕空间
  max-width: 3200px; // 增加最大宽度限制，适应更大的屏幕
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  @media (max-width: $screen-sm) {
    width: 100%;
    padding: 20px 15px;
    border-radius: 8px;
  }
}

.steps-nav {
  margin: 0 auto 20px;
  max-width: 2400px; // 增加步骤导航的最大宽度
  padding: 20px 0;

  @media (max-width: $screen-sm) {
    padding: 10px 0;

    ::v-deep .el-step__title {
      font-size: 14px;
    }

    ::v-deep .el-step__icon {
      width: 30px;
      height: 30px;
    }
  }

  @media (max-width: $screen-xs) {
    ::v-deep .el-step__title {
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 80px;
    }
  }
}

.one-click-input {
  margin-bottom: 30px;

  .el-input {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
  }
}

.stream-content {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);

  @media (max-width: $screen-sm) {
    padding: 15px;
    border-radius: 8px;
  }

  .el-input.el-textarea {
    width: 100%;
  }
}

.upload-demo {
  .el-upload__tip {
    font-size: 14px;
    color: #909399;
    margin-top: 12px;
    text-align: center;

    @media (max-width: $screen-sm) {
      font-size: 12px;
    }
  }
}

.upload-content {
  text-align: center;
  padding: 20px;

  @media (max-width: $screen-sm) {
    padding: 15px 10px;

    .el-icon-upload {
      font-size: 32px;
    }

    .el-upload__text {
      font-size: 14px;
    }
  }
}

.upload-success-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: $screen-sm) {
    flex-wrap: wrap;
    gap: 5px;
    padding: 8px;
  }
}

.text-tip {
  margin: 20px 0;
  text-align: right;

  .el-link {
    font-size: 14px;
    color: #409EFF;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

.text-tip-box {
  margin-top: 30px;

  .el-card {
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
  }
}

.step-btn-box {
  text-align: center;
  margin-top: 40px;
  padding: 20px 0;

  @media (max-width: $screen-sm) {
    margin-top: 20px;
    padding: 15px 0;
  }

  .el-button {
    min-width: 120px;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    margin: 0 10px;
    transition: all 0.3s ease;

    @media (max-width: $screen-sm) {
      min-width: 100px;
      height: 40px;
      font-size: 14px;
      margin: 0 5px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);

      @media (max-width: $screen-sm) {
        transform: translateY(-1px);
      }
    }
  }
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.fontTip {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  background-color: white;
  padding: 5px 10px;
  text-align: right;
  font-size: 11px;
  color: #191818;
  transition: all 0.3s ease;
}

.fontTip:hover {
  color: #04bc87;
  font-size: 15px;
}

.text-tip-active {
  color: #04bc87;
  font-size: 15px;
  font-style: italic;
}


.elBackTop {
  transition: all 0.3s ease;
}

.elBackTop:hover {
  color: #0ef6b0;
  transform: scale(1.3);
}

.box-card {

}

.buildPptProgressDivBox {
  width: 90%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.textRulesItem {
  font-size: 14px;
}

.textExamplesItem {
  font-size: 12px;
}

.highlighted {
  color: #068769;
  transform: scale(1.05);
  transition: all 0.3s ease;
  font-weight: bold;
  font-style: italic;
}

.highlighted:hover {
  display: flex;
  font-weight: bold;
  font-style: italic;
  justify-content: left;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.highlightedSelectLongImg {
  background-color: #d9ebff; /* 高亮背景颜色 */
}

.imgBox {
  width: 70%;
  position: relative;
  display: block;
  margin: 0 auto;
  text-align: center;
  transition: all 1s ease;
}

.imgBox:hover {
  width: 72%;
  border: 3px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}

.elimg {
  position: relative;
  display: inline-block;
}

.imgBorder {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.shoLongImgBoxDiv {
  margin: 30px 5px 50px 5px;
}

.footerTipFont {
  height: 20px;
  font-size: 10px;
  padding: 5px; /* 添加内边距 */
  color: #666666;
  position: relative;
  right: 10px;
  bottom: 30px;
  margin-bottom: -20px;
  z-index: 10;
}

.api-key-missing {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
  z-index: 1000;
  overflow: hidden;
}

.message-container {
  width: 600px;
  text-align: center;
  padding: 50px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.5s ease-in-out;
  position: relative;
  z-index: 1;
}

.icon-wrapper {
  background: rgba(230, 162, 60, 0.1);
  width: 96px;
  height: 96px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}

.warning-icon {
  font-size: 48px;
  color: #E6A23C;
}

.divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, #ebeef5, transparent);
  margin: 24px 0;
}

.message-container h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 28px;
  font-weight: 600;
}

.main-message {
  /* 基础样式 */
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin: 0 auto 20px;
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer;
  display: inline-block;
  position: relative;
  border: none;
  text-align: center;
  user-select: none;

  /* 现代渐变背景 - 使用项目主色调 */
  background: linear-gradient(135deg, #409EFF 0%, #66b3ff 100%);

  /* 阴影效果 */
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25),
  0 1px 4px rgba(0, 0, 0, 0.08);

  /* 平滑过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 悬停效果 */
  &:hover {
    color: #ffffff;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  /* 点击效果 */
  &:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3),
    0 1px 4px rgba(0, 0, 0, 0.1);
  }

  /* 焦点状态（键盘导航） */
  &:focus {
    outline: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1),
    0 0 0 3px rgba(102, 126, 234, 0.2);
  }

  /* 禁用状态 */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #a0a0a0 0%, #808080 100%);

    &:hover {
      transform: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      background: linear-gradient(135deg, #a0a0a0 0%, #808080 100%);
    }
  }

  /* 加载状态动画 */
  &.loading {
    position: relative;
    color: transparent;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid #ffffff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: button-loading-spin 1s linear infinite;
    }
  }
}

/* 加载动画关键帧 */
@keyframes button-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-message {
    font-size: 14px;
    padding: 12px 24px;
    margin: 0 auto 16px;
  }
}

@media (max-width: 480px) {
  .main-message {
    font-size: 13px;
    padding: 10px 20px;
    margin: 0 auto 12px;
    border-radius: 8px;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .main-message {
    transition: none;

    &:hover {
      transform: none;
    }

    &:active {
      transform: none;
    }
  }

  @keyframes button-loading-spin {
    0%, 100% {
      transform: rotate(0deg);
    }
  }
}


.additional-info {
  background: #f8f9fb;
  padding: 20px;
  border-radius: 8px;
  text-align: left;
  margin: 24px 0;

  p {
    color: #409EFF;
    font-size: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      color: #606266;
      margin: 8px 0;
      display: flex;
      align-items: center;

      i {
        color: #67C23A;
        margin-right: 8px;
      }
    }
  }
}

.contact-support {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  margin-top: 24px;

  i {
    margin-right: 8px;
    font-size: 20px;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.one-click-input {
  margin-bottom: 20px;

  .el-input {
    width: 100%;
  }
}

.stream-content {
  margin-top: 20px;

  .content-box {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    max-height: 300px;
    overflow-y: auto;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}

.step-container {
  animation: fadeIn 0.5s ease-out;
}

.step-header {
  text-align: center;
  margin-bottom: 40px;

  @media (max-width: $screen-sm) {
    margin-bottom: 25px;
  }

  h2 {
    font-size: 28px;
    color: #303133;
    margin-bottom: 12px;
    font-weight: 600;

    @media (max-width: $screen-sm) {
      font-size: 22px;
      margin-bottom: 8px;
    }
  }

  .step-description {
    font-size: 16px;
    color: #606266;
    margin: 0;

    @media (max-width: $screen-sm) {
      font-size: 14px;
    }
  }
}

.input-type-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;

  .el-radio-group {
    display: flex;
    gap: 20px;

    @media (max-width: $screen-md) {
      gap: 10px;
    }

    @media (max-width: $screen-sm) {
      flex-direction: column;
      width: 100%;
      align-items: center;
      gap: 15px;
    }

    .el-radio {
      opacity: 0; // 初始隐藏，配合入场动画
      height: 80px;
      width: 200px;
      border: 1px solid #e0e6ed; // 更细的边框
      border-radius: 12px;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      background-color 0.3s ease,
      border-color 0.3s ease;
      background-color: #fff; // 默认白色背景
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04); // 默认阴影
      position: relative; // 用于定位选中标记
      overflow: hidden; // 防止选中标记溢出

      // 应用入场动画
      animation-name: fadeInUp;
      animation-duration: 0.6s;
      animation-fill-mode: forwards;
      animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);

      @media (max-width: $screen-md) {
        width: 180px;
        height: 70px;
      }

      @media (max-width: $screen-sm) {
        width: 100%;
        max-width: 300px;
        height: 60px;
      }

      &:hover {
        transform: translateY(-2px);
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.05);
      }

      &.is-checked {
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.1);

        .el-radio__label {
          color: #409EFF;
        }
      }

      .el-radio__input {
        display: none;
      }

      .el-radio__label {
        font-size: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;

        @media (max-width: $screen-sm) {
          flex-direction: row;
          gap: 10px;
          font-size: 14px;
        }

        i {
          font-size: 24px;
          margin-bottom: 8px;

          @media (max-width: $screen-sm) {
            margin-bottom: 0;
            font-size: 20px;
          }
        }

        span { // 容器内的文字
          font-weight: 500; // 稍粗
          color: #303133; // 深灰色文字
          transition: color 0.3s ease;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-textarea {
  width: 100%;
  margin-bottom: 10px;
}

.generate-input-container {
  width: 100%;
  max-width: 2400px; // 增加生成输入容器的最大宽度
  margin: 0 auto;
}

.format-helper {
  margin-top: 12px;
  padding: 0 10px;
  width: 100%;
  max-width: 2400px; // 增加格式助手的最大宽度
  margin-left: auto;
  margin-right: auto;
}

.format-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  padding: 10px;
  background: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.format-toggle-link {
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    transform: translateY(-2px);
  }

  i {
    color: #409EFF;
    font-size: 16px;
  }

  span {
    font-size: 14px;
  }
}

.format-tip-component {
  flex: 1;
}

.format-instructions {
  margin-top: 20px;
  background: #f9fafc;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 2400px; // 增加最大宽度
  margin-left: auto;
  margin-right: auto;
}

.upload-demo {
  width: 100%;
  max-width: 2400px; // 增加上传演示的最大宽度
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

.upload-content {
  text-align: center;
  padding: 20px;
}

.upload-success-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.theme-selector-container {
  max-width: 800px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

.theme-form-item {
  @media (max-width: $screen-sm) {
    ::v-deep .el-form-item__label {
      float: none;
      text-align: left;
      display: block;
      margin-bottom: 10px;
    }

    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

.theme-select {
  width: 100%;
}

.theme-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .theme-label {
    font-size: 15px;
    color: #303133;

    @media (max-width: $screen-sm) {
      font-size: 14px;
    }
  }

  .theme-value {
    color: #909399;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;

    @media (max-width: $screen-sm) {
      font-size: 12px;
    }
  }
}

.theme-preview-hint {
  text-align: center;
  margin-top: 15px;

  @media (max-width: $screen-sm) {
    margin-top: 10px;
  }
}

.author-input-container {
  max-width: 600px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }

  @media (max-width: $screen-sm) {
    ::v-deep .el-form-item__label {
      float: none;
      text-align: left;
      display: block;
      margin-bottom: 10px;
    }

    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .author-input {
    width: 100%;
  }
}

.options-container {
  margin-bottom: 30px;

  @media (max-width: $screen-sm) {
    // 在小屏幕上调整el-row的gutter
    ::v-deep .el-row {
      margin-left: -5px !important;
      margin-right: -5px !important;
    }

    ::v-deep .el-col {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }
}

.option-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 150px;
  margin-bottom: 20px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: $screen-sm) {
    min-height: 120px;
    margin-bottom: 10px;

    &:hover {
      transform: translateY(-3px);
    }
  }

  .option-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    i {
      margin-right: 8px;
      font-size: 18px;
      color: #409EFF;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    @media (max-width: $screen-sm) {
      padding: 12px 15px;

      i {
        font-size: 16px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  .option-content {
    padding: 20px;
    display: flex;
    justify-items: center;


    @media (max-width: $screen-sm) {
      padding: 15px;
    }

    // Logo设置盒子特殊样式
    .el-form-item[prop="logoPosition"] {
      display: flex;
      flex-direction: column;
      align-items: center;

      .el-form-item__label {
        text-align: center;
        padding: 0 0 10px;
      }

      .el-form-item__content {
        margin-left: 0 !important;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.generation-container {
  max-width: 800px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

.generation-result {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeInUp 0.8s ease-out;

  @media (max-width: $screen-sm) {
    padding: 20px;
    border-radius: 10px;
  }

  .result-header {
    margin-bottom: 30px;

    @media (max-width: $screen-sm) {
      margin-bottom: 20px;
    }

    .result-icon {
      font-size: 60px;
      color: #67C23A;
      margin-bottom: 20px;

      @media (max-width: $screen-sm) {
        font-size: 40px;
        margin-bottom: 15px;
      }
    }

    h3 {
      font-size: 24px;
      color: #303133;
      margin: 0;

      @media (max-width: $screen-sm) {
        font-size: 20px;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: $screen-sm) {
      flex-direction: column;
      gap: 10px;
      align-items: center;
    }

    .el-button {
      min-width: 160px;
      height: 50px;
      border-radius: 25px;
      font-size: 16px;

      @media (max-width: $screen-sm) {
        min-width: 200px;
        height: 44px;
        font-size: 14px;
      }
    }
  }
}

// 美化的空状态样式
.beautiful-empty-state {
  position: relative;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-radius: 24px;
  padding: 30px 30px;
  text-align: center;
  border: 1px solid rgba(64, 158, 255, 0.1);
  box-shadow: 0 8px 32px rgba(64, 158, 255, 0.08);
  overflow: hidden;
  height: auto;
  max-height: 60vh; // 限制最大高度为视口高度的60%
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: $screen-sm) {
    padding: 30px 20px;
    border-radius: 16px;
    max-height: 50vh; // 移动端限制为50%
  }

  // 浮动装饰元素
  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .floating-element {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
      animation: float 6s ease-in-out infinite;

      &.element-1 {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.element-2 {
        width: 60px;
        height: 60px;
        top: 20%;
        right: 15%;
        animation-delay: 1s;
      }

      &.element-3 {
        width: 40px;
        height: 40px;
        bottom: 30%;
        left: 20%;
        animation-delay: 2s;
      }

      &.element-4 {
        width: 70px;
        height: 70px;
        bottom: 15%;
        right: 10%;
        animation-delay: 3s;
      }

      &.element-5 {
        width: 50px;
        height: 50px;
        top: 50%;
        left: 5%;
        animation-delay: 4s;
      }
    }
  }

  // 主要内容
  .main-content {
    position: relative;
    z-index: 2;
    max-width: 500px;
    margin: 0 auto;
  }

  // 动画图标容器
  .animated-icon-container {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;

    .icon-background {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
      border-radius: 50%;
      animation: pulse 2s ease-in-out infinite;
    }

    .animated-icon {
      position: relative;
      font-size: 60px;
      color: #409EFF;
      z-index: 3;
      animation: bounce 2s ease-in-out infinite;

      @media (max-width: $screen-sm) {
        font-size: 50px;
      }
    }

    .icon-pulse {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100px;
      height: 100px;
      border: 2px solid rgba(64, 158, 255, 0.3);
      border-radius: 50%;
      animation: ripple 3s ease-out infinite;
    }
  }

  // 内容文字
  .content-text {
    margin-bottom: 25px;

    .main-title {
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      background: linear-gradient(135deg, #409EFF, #67C23A);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      @media (max-width: $screen-sm) {
        font-size: 20px;
      }
    }

    .description {
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
      margin: 0;

      @media (max-width: $screen-sm) {
        font-size: 13px;
      }
    }
  }

  // 特性网格
  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 25px;

    @media (max-width: $screen-sm) {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px 10px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 12px;
      border: 1px solid rgba(64, 158, 255, 0.1);
      transition: all 0.3s ease;
      animation: fadeInUp 0.6s ease-out;

      &:nth-child(1) { animation-delay: 0.1s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.3s; }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
        background: rgba(255, 255, 255, 0.95);
      }

      .feature-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409EFF, #67C23A);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        box-shadow: 0 3px 12px rgba(64, 158, 255, 0.3);

        i {
          font-size: 20px;
          color: white;
        }
      }

      span {
        font-size: 13px;
        font-weight: 500;
        color: #2c3e50;
      }
    }
  }

  // 操作提示
  .action-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px;
    background: rgba(64, 158, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(64, 158, 255, 0.1);

    .hint-icon {
      animation: bounce 1.5s ease-in-out infinite;

      i {
        font-size: 16px;
        color: #409EFF;
      }
    }

    p {
      font-size: 14px;
      color: #409EFF;
      margin: 0;
      font-weight: 500;

      @media (max-width: $screen-sm) {
        font-size: 13px;
      }
    }
  }
}

// 动画定义
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.9;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-dialog {
}

.preview-container {
  max-width: 100%;
  overflow: hidden;
}

.preview-disclaimer {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  i {
    color: #E6A23C;
    margin-right: 8px;
    font-size: 16px;
  }

  span {
    font-size: 14px;
    color: #606266;
  }
}

.preview-actions {
  display: flex;
  justify-content: center;
  gap: 20px;

  .el-button {
    min-width: 140px;
  }
}

.theme-preview-dialog {
  @media (max-width: $screen-sm) {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin-top: 3vh !important;
    }
  }

  .el-dialog {
    margin-top: 5vh !important;
    border-radius: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  }

}

.template-preview-container {
  height: 100%;
  background: #f9f9f9;
  position: relative;
  display: flex;
  flex-direction: column;
}

.carousel-item-container {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.image-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.preview-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.preview-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 2;

  .el-button-group {
    display: flex;

    .el-button {
      padding: 5px;
      font-size: 12px;
      opacity: 0.8;
      transition: all 0.2s ease;

      &:hover {
        opacity: 1;
      }
    }
  }

  .preview-counter {
    color: #606266;
    font-size: 12px;
    min-width: 40px;
    text-align: center;
    opacity: 0.9;
  }
}

.preview-thumbnails-container {
  margin-top: auto;
  height: 90px;
  background: #f5f5f5;
  border-top: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  position: relative;

  .thumbnail-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    transition: all 0.3s ease;

    &.thumbnail-nav-prev {
      left: 10px;
    }

    &.thumbnail-nav-next {
      right: 10px;
    }
  }
}

.preview-thumbnails {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0 50px;
  gap: 8px;
  overflow-x: auto;
  scroll-behavior: smooth;
  justify-content: flex-start; /* 默认左对齐 */

  /* 当子元素数量较少时（可以完全显示）则居中显示 */
  &.centered {
    justify-content: center;
    padding: 0 10px; /* 居中时减少内边距 */
  }

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  .thumbnail-item {
    flex: 0 0 auto;
    width: 80px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.active {
      border-color: #409EFF;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .el-image {
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-wrapper {
  width: 100%;
  max-width: 2400px; // 增加内容包装器的最大宽度
  margin: 0 auto;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

.content-wrapper-top {
  margin-top: 20px;
}

.content-textarea {
  width: 100%;
  margin-bottom: 10px;
}

.format-helper {
  margin-top: 12px;
  padding: 0 10px;
  width: 100%;
}

.format-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  padding: 10px;
  background: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.format-toggle-link {
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    transform: translateY(-2px);
  }

  i {
    color: #409EFF;
    font-size: 16px;
  }

  span {
    font-size: 14px;
  }
}

.format-tip-component {
  flex: 1;
}

.format-instructions {
  margin-top: 20px;
  background: #f9fafc;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.file-upload-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-demo {
  width: 100%;
  display: flex;
  justify-content: center;
}

.upload-content {
  text-align: center;
  padding: 20px;
}

.upload-success-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  width: 100%;
  max-width: 900px;

  i {
    color: #67C23A;
    font-size: 20px;
  }

  span {
    color: #67C23A;
    font-weight: 500;
    margin-right: 15px;
  }

  .remove-file-btn {
    color: #909399;

    &:hover {
      color: #f56c6c;
    }

    i {
      color: inherit;
      font-size: 14px;
      margin-right: 5px;
    }
  }
}

// 保留其他格式说明的样式
.format-instruction-header {
  text-align: center;
  margin-bottom: 20px;

  h3 {
    font-size: 20px;
    color: #303133;
    margin-bottom: 8px;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #606266;
    margin: 0;
  }
}

.format-instruction-container {
  display: flex;
  gap: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.format-left-panel, .format-right-panel {
  flex: 1;
}

.format-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 300px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  &.example-card {
    background: #f0f9ff;
  }
}

.format-card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.format-card-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.format-rule-item, .format-example-item {
  padding: 8px 0;
  transition: all 0.3s ease;
  position: relative;

  &.highlighted {
    color: #409EFF;
    transform: translateX(10px);
    font-weight: 500;
  }
}

.format-rule-item {
  padding-left: 28px;

  .format-rule-icon {
    position: absolute;
    left: 0;
    top: 9px;
    color: #67C23A;
  }
}

.format-example-item {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  padding: 4px 8px;
  border-radius: 4px;

  &.highlighted {
    background: rgba(64, 158, 255, 0.1);
  }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.preview-counter {
  font-size: 14px;
  color: #606266;
}

.thumbnail-item {
  width: 80px;
  height: 60px;
  object-fit: cover;
  margin-right: 10px;
  cursor: pointer;
  border: 2px solid transparent;

  &.active {
    border-color: #409EFF;
  }
}

.keyboard-tips {
  font-size: 12px;
  color: #909399;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;

  i {
    font-size: 40px;
    margin-bottom: 10px;
  }
}

// 为移动端添加辅助工具类
@media (max-width: $screen-sm) {
  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-mt-10 {
    margin-top: 10px !important;
  }

  .mobile-mb-10 {
    margin-bottom: 10px !important;
  }

  .mobile-p-10 {
    padding: 10px !important;
  }

  .mobile-hidden {
    display: none !important;
  }
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.upload-tip {
  text-align: center;
  margin-bottom: 8px;
  width: 100%;

  span {
    color: #909399;
    font-size: 11px;
    display: inline-flex;
    align-items: center;
    gap: 4px;

    i {
      color: #909399;
      font-size: 12px;
    }
  }
}

.logo-uploader {
  text-align: center;
  width: 100%;
  margin-bottom: 8px;

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409EFF;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  transition: color 0.3s ease;
}

.logo-actions {
  text-align: center;
  margin-top: 12px;

  .remove-logo-btn {
    color: #f56c6c;
    padding: 5px 15px;
    transition: all 0.3s ease;

    &:hover {
      color: #ff4949;
      transform: translateY(-1px);
    }

    i {
      margin-right: 4px;
    }
  }
}

.chat-container {
  margin-top: 20px;
  padding: 25px; // 增加内边距
  background: #ffffff; // 添加白色背景，去除透明
  border-radius: 16px; // 更大的圆角
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); // 更明显的外阴影
  border: 1px solid #e8e8e8; // 添加边框
  transition: all 0.3s ease;

  .chat-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    .chat-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333;

      i {
        font-size: 18px;
        color: #409EFF;
      }
    }
  }

  @media (max-width: $screen-sm) {
    padding: 20px;
    border-radius: 12px;
  }

  .chat-messages {
    max-height: 1200px;
    overflow-y: auto;
    padding: 10px 15px; // 调整滚动区padding
    will-change: transform; /* 提示浏览器这个元素会有变换，创建新的渲染层 */
    transform: translateZ(0); /* 触发GPU加速 */
    backface-visibility: hidden; /* 减少重绘 */

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15); // 加深滚动条颜色
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.03); // 调整轨道背景
      border-radius: 3px;
    }
  }

  // 优化消息项渲染性能
  .message-item {
    margin-bottom: 28px; // 增加消息间距
    display: flex;
    flex-direction: column;
    max-width: 85%;
    transition: opacity 0.3s ease; // Keep opacity transition for initial render
    will-change: transform, opacity; /* 提示浏览器这些属性会改变 */
    contain: layout; /* 告诉浏览器这个元素内部的布局不会影响外部 */

    // Card styles
    background: linear-gradient(135deg, #fff 0%, hsl(0, 0%, 98%) 100%); // Blueish icon background (kept)

    border-radius: 12px; // Card rounded corners
    padding: 15px 20px; // Padding inside the card
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); // More subtle shadow
    border: 1px solid #fff; // Very subtle border
    position: relative; // Ensure this is set for absolute positioning of children

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08); // Slightly stronger shadow on hover
    }

    &.message-user {
      align-self: flex-end;
      margin-left: auto;
      // Removed background gradient for milk white
      // Removed specific box-shadow/border-color for milk white, use default

      .message-icon {
        order: 1;
        margin-left: 15px; // Space between content and icon
        margin-right: 0;
        background: linear-gradient(135deg, #4dd0e1 0%, #00bcd4 100%); // Blueish icon background (kept)
      }
    }

    &.message-assistant {
      align-self: flex-start;
      margin-right: auto;
      // Removed background gradient for milk white
      // Removed specific box-shadow/border-color for milk white, use default

      .message-icon {
        order: -1;
        margin-right: 15px; // Space between icon and content
        margin-left: 0;
        background: linear-gradient(135deg, #aed581 0%, #8bc34a 100%); // Greenish icon background (kept)
      }
    }

    .message-bubble {
      // Remove bubble specific styles, it's now part of the card padding
      padding: 0; // Padding moved to .message-item
      background: none; // No background
      border: none; // No border
      display: flex; // Flex inside the card item
      align-items: flex-start;
      transition: none; // Remove transform/shadow transition from bubble
      will-change: auto; // Reset will-change
      contain: none; // Reset contain
      position: static; // Reset position
    }

    .message-icon {
      width: 36px; // Slightly larger icons
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        color: white;
        font-size: 20px; // Larger icon font size
      }
    }

    .message-content {
      font-size: 15px; // Slightly larger font size
      line-height: 1.7; // Improved line height
      white-space: pre-wrap;
      word-break: break-word;
      padding: 0; // Padding handled by message-item
      flex: 1;
      contain: content; /* 内容隔离，减少重绘范围 */
      color: #303133; // Darker text color
      // Add fade-in animation for stream updates
      animation: fadeInStream 0.4s ease-out;
    }

    // Stream fade-in animation
    @keyframes fadeInStream {
      from {
        opacity: 0.6;
      }
      // Start slightly transparent
      to {
        opacity: 1;
      }
    }

    // Typing indicator positioning
    .typing-indicator {
      display: flex;
      align-items: center;
      gap: 6px; // Increased gap between dots
      height: 20px; // Can be kept or adjusted
      position: absolute; // Position relative to message-item
      bottom: 8px; // Adjusted from bottom, relative to padding

      @keyframes typing-dot-bounce {
        0%, 80%, 100% {
          transform: scale(0);
        }
        40% {
          transform: scale(1.0);
        }
      }

      span {
        width: 10px; // Slightly larger dots
        height: 10px;
        background: linear-gradient(135deg, #85e085 0%, #67c23a 100%); // Using gradient color (kept)
        border-radius: 50%;
        display: inline-block;
        animation: typing-dot-bounce 1.4s infinite ease-in-out both;
        will-change: transform;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:nth-child(3) {
          animation-delay: 0s;
        }
      }
    }

    // Position typing indicator based on message type - now always at right bottom    &.message-user .typing-indicator,    &.message-assistant .typing-indicator {       left: auto; // Clear left positioning        right: 15px; // Position on the right for all messages, inside padding    }
  }

  .message-actions {
    position: absolute; // Position relative to message-item
    bottom: -28px; // Adjust vertical position below the card
    // Default position - overridden below
    right: 20px; // Position to the right for user messages

    &.message-assistant { // Corrected selector: apply to message-item with message-assistant class
      .message-actions { // Target the actions within assistant message items
        right: auto; // Clear right
        left: 20px; // Position left for assistant
      }
    }

    font-size: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(3px); // Add slight blur effect
    border-radius: 16px; // More rounded
    padding: 4px 8px; // Adjusted padding
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); // Enhanced shadow
    transition: all 0.2s ease;
    opacity: 0;
    display: flex;
    align-items: center;

    .el-button {
      padding: 4px 8px;

      i {
        margin-right: 2px;
      }
    }

    .ready-indicator {
      display: flex;
      align-items: center;
      margin-left: 8px;
      color: #67C23A;
      font-size: 12px;
      animation: pulse 1.5s infinite;

      i {
        margin-right: 4px;
      }
    }
  }

  // Show actions on hover of the message item
  .message-item:hover .message-actions {
    opacity: 1;
    transform: translateY(-2px);
  }

  .editor-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9;
    background: #fff;

    .close-button {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 1001;
    }
  }
}

.edit-actions {
  position: relative;
  width: 100%;
  padding-bottom: 40px;
  margin: 10px 0;
}

.edit-textarea {
  width: 100%;
  margin-bottom: 8px;
}


.edit-buttons {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 8px 0;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.edit-buttons .el-button {
  padding: 6px 12px;
  font-size: 12px;
  min-width: 60px;
}

.message-actions {
  position: absolute;
  right: 8px;
  bottom: -24px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 2px 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  opacity: 0;
  display: flex;
  align-items: center;

  .el-button {
    padding: 4px 8px;

    i {
      margin-right: 2px;
    }
  }

  .ready-indicator {
    display: flex;
    align-items: center;
    margin-left: 8px;
    color: #67C23A;
    font-size: 12px;
    animation: pulse 1.5s infinite;

    i {
      margin-right: 4px;
    }
  }
}

.message-item:hover .message-actions {
  opacity: 1;
  transform: translateY(-2px);
}

/* 终止生成按钮 - 优化设计 */
.abort-button-container {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;

  /* 响应式设计 */
  @media (max-width: 768px) {
    bottom: 30px;
  }

  .abort-generation-btn {
    background: #6c757d !important;
    border: none !important;
    color: #ffffff !important;
    border-radius: 8px !important;
    padding: 10px 20px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2) !important;
    transition: all 0.3s ease !important;
    min-width: 110px !important;
    height: 38px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(10px) !important;

    &:hover {
      background: #5a6268 !important;
      box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3) !important;
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(108, 117, 125, 0.2) !important;
    }

    &:focus {
      outline: none !important;
      box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2), 0 0 0 3px rgba(108, 117, 125, 0.1) !important;
    }

    i {
      margin-right: 4px;
      font-size: 12px;
    }

    span {
      font-weight: 400;
      line-height: 1;
    }
  }
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  height: 20px;
}

.editor-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9;
  background: #fff;

  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1001;
  }
}

.templateCreationView {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9;
}

// 移除不再使用的模板选择相关样式，现在直接使用模板选择器组件

.template-dialog-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 新增：模板步骤容器样式
.template-step-container {
  height: calc(100vh - 120px); // 减少减去的高度，让绿色区域更高
  display: flex;
  flex-direction: column;

  .template-selector {
    flex: 1;
    height: 100%;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
}

// 新增：消息编辑按钮样式
.message-item-with-hover {
  // 悬停时显示编辑按钮
  &:hover .message-edit-button {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

// 消息气泡容器样式
.message-bubble {
  position: relative;

  .message-edit-button {
    position: absolute;
    bottom: -14px;
    right: -14px;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: all 0.35s ease;
    z-index: 10;

    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 0;
    font-size: 12px;
    color: #606266;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;

    &:hover {
      background: #409eff;
      color: white;
      border-color: #409eff;
      transform: scale(1.05);
    }

    i {
      margin: 0;
      font-size: 12px;
    }
  }
}

// 新增：模板背景相关样式
.modern-aippt-container {
  transition: background-image 0.6s ease-in-out;
  position: relative;
  min-height: 100vh;
}

.modern-background {
  transition: opacity 0.6s ease-in-out;

  &.template-background-active {
    opacity: 0.3; // 当模板背景激活时，降低原背景的透明度

    .gradient-overlay {
      opacity: 0.5; // 减少渐变覆盖层的透明度
    }

    .floating-shapes {
      opacity: 0.2; // 减少浮动形状的透明度
    }
  }
}

.progress-steps {
  background: white;
  padding: 20px 40px;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1;
  flex-shrink: 0; // 防止压缩
}

.template-dialog-header {
  background: white;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0; // 防止压缩

  .header-left {

    h2 {
      font-size: 24px;
      color: #303133;
      margin: 0 0 8px;
    }

    .description-row {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header-description {
      margin: 0;
      color: #666;
    }
  }

  .header-center {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;

    .el-input {
      width: 100%;
    }
  }
}

.template-content {
  flex: 1;
  overflow-y: auto;
  background: #f5f7fa;
  position: relative;
  height: calc(100vh - 250px); // 减去其他区域的高度
  // 移除 min-height，让内容自然撑开

  .template-grid, // Modified line
  .history-grid { // Added line
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); // 与对话框中的尺寸保持一致
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px; // 减少内边距
    // 确保网格内容紧凑显示
    align-content: start;
  }
}

.template-dialog-footer {
  padding: 16px 24px;
  background: white;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  position: sticky;
  bottom: 0;
  z-index: 10;
  flex-shrink: 0; // 防止压缩

  .footer-left, .footer-right {
    .el-button {
      min-width: 120px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .template-dialog-header {

    .header-left {
      margin-bottom: 12px;

      h2 {
        font-size: 20px;
      }
    }

    .header-center {
      max-width: 100%;
    }
  }

  .template-content {
    height: calc(100vh - 240px); // 移动端调整高度

    .template-grid, // Modified line
    .history-grid { // Added line
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); // Modified line
      gap: 16px;
      padding: 16px 10px;
    }
  }

  .template-card, // Modified line
  .history-card { // Added line
    .template-card-cover, // Modified line
    .history-card-cover { // Added line
      height: 140px; // Modified line
    }

    .template-card-info, // Modified line
    .history-card-info { // Added line
      padding: 12px;

      h3 {
        font-size: 14px;
      }
    }
  }

  .template-dialog-footer {
    padding: 12px 16px;

    .el-button {
      min-width: 100px !important;
      height: 36px !important;
    }
  }
}

.template-dialog {
  .el-dialog {
    margin: 0 !important;
    height: 100vh;
    background: #f5f7fa;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    padding: 0;
    height: 100vh;
    overflow: hidden;
  }
}

.template-dialog-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 修改进度条样式，与主页面保持一致
  .steps-nav {
    padding: 20px 40px;
    margin-bottom: 0;
    flex-shrink: 0;

    ::v-deep .el-step__title {
      font-size: 16px;
      line-height: 1.4;
    }

    ::v-deep .el-step__description {
      font-size: 12px;
      margin-top: 4px;
    }

    ::v-deep .el-step__icon {
      width: 34px;
      height: 34px;
      font-size: 16px;
    }

    @media (max-width: 768px) {
      padding: 15px 20px;

      ::v-deep .el-step__title {
        font-size: 14px;
      }

      ::v-deep .el-step__description {
        font-size: 11px;
      }

      ::v-deep .el-step__icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
      }
    }
  }
}

.template-dialog-header {
  padding: 20px 30px;
  border-bottom: 1px solid #ebeef5;
  background: white;
  flex-shrink: 0;
  // ... rest of the header styles ...
}


.fullscreen-template-dialog {

  // 调整 Element UI Dialog 默认 Header 和 Body 的内边距
  ::v-deep .el-dialog__header {
    padding: 15px 20px 5px; // 保留顶部/左右，减少底部 padding
    border-bottom: 1px solid #ebeef5; // 可能需要显式加回边框
  }

  ::v-deep .el-dialog__body {
    padding: 0; // 移除 body 的默认 padding
  }

  .template-dialog-container {
    height: 100vh; // 明确设置为视口高度
    display: flex;
    flex-direction: column;
    max-height: 100vh; // 防止超出视口
    overflow: hidden; // 防止整体滚动

    .fixed-header {
      flex-shrink: 0; // 防止被压缩
      background: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      border-bottom: 1px solid #ebeef5;

      .steps-nav {
        padding: 20px 40px;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
      }
    }

    .scrollable-content {
      flex: 1; // 占据剩余空间
      overflow-y: auto; // 允许内容滚动
      background: #f5f7fa; // 背景色
      display: flex;
      flex-direction: column;

      .template-dialog-header {
        flex-shrink: 0; // 防止被压缩
        padding: 0 20px 15px; // 减少底部间距
        background: #f5f7fa; // 匹配背景色
      }

      .template-content {
        flex: 1; // 占据剩余空间，但不设置最小高度
        padding: 10px 30px 10px; // 减少底部内边距，消除空白
        // 移除 min-height，让内容自然撑开

        .template-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 24px;
          max-width: 1400px;
          margin: 0 auto;
          // 确保网格内容紧凑显示
          align-content: start;
        }
      }
    }

    .fixed-footer {
      flex-shrink: 0; // 防止被压缩
      background: #fff;
      box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
      border-top: 1px solid #ebeef5;
      padding: 12px 30px; // 减少内边距
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 10; // 确保在最上层
      min-height: 68px; // 调整最小高度

      .footer-left, .footer-right {
        .el-button {
          padding: 12px 28px; // 与其他按钮保持一致
          font-size: 14px;
          height: 44px; // 与其他按钮保持一致
          min-width: 120px; // 增加最小宽度
          border-radius: 12px; // 与整体圆角保持一致
          font-weight: 500;

          i {
            margin: 0 4px;
          }
        }

        .el-button--primary {
          background: $gradient-primary !important;
          border: none !important;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;

          &:hover {
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.6) !important;
            transform: translateY(-2px) !important;
          }

          &:active {
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4) !important;
            transform: translateY(1px) !important;
          }
        }
      }
    }
  }
}

// 移动端响应式调整
@media screen and (max-width: 768px) {
  .fullscreen-template-dialog {
    .scrollable-content {
      padding: 15px;

      .template-dialog-header {
        padding: 0 10px 15px;
      }

      .template-content {
        padding: 16px 20px; // 调整移动端模板列表内边距
        .template-grid {
          grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
          gap: 16px;
        }
      }
    }

    .fixed-footer {
      padding: 12px 20px;

      .footer-left, .footer-right {
        .el-button {
          padding: 8px 15px;
        }
      }
    }
  }
}

// ... other styles ...

.template-card, // Modified line
.history-card { // Added line
  border: 1px solid rgba(230, 235, 245, 0.8); // 更柔和的边框颜色
  border-radius: 16px; // 增大圆角
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%); // 微妙的渐变背景
  overflow: hidden; // 防止内容溢出
  cursor: pointer; // 提示可点击（模板需要，历史记录可选）
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); // 更流畅的过渡
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.04); // 双层阴影
  display: flex; // 使用 flex 布局
  flex-direction: column; // 垂直排列内容
  position: relative; // 为伪元素定位
  backdrop-filter: blur(10px); // 背景模糊效果

  // 添加光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02); // 更明显的悬停效果
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08); // 更深的阴影
    border-color: rgba(102, 126, 234, 0.3); // 悬停时边框变色
  }

  .template-card-cover, // Modified line
  .history-card-cover { // Added line
    width: 100%;
    height: 160px; // 稍微增加高度
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); // 渐变背景
    position: relative;
    border-radius: 12px 12px 0 0; // 只有顶部圆角

    // 添加装饰性边框
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      opacity: 0.6;
    }

    .el-image {
      width: 100%;
      height: 100%;
      display: block;
      transition: transform 0.4s ease;

      &:hover {
        transform: scale(1.05); // 图片悬停放大效果
      }

      .image-slot { // 图片加载错误/占位符样式
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        color: #94a3b8;
        font-size: 32px;

        i {
          opacity: 0.7;
        }
      }
    }
  }

  .template-card-info, // Modified line
  .history-card-info { // Added line
    padding: 16px; // 信息区域内边距
    flex-grow: 1; // 占据剩余空间
    display: flex;
    flex-direction: column;
    justify-content: space-between; // 使标题和元信息分开

    h3 {
      font-size: 16px;
      color: #303133;
      margin: 0 0 8px;
      font-weight: 500;
      line-height: 1.4; /* 控制行高 */
      white-space: nowrap; /* 强制不换行 */
      overflow: hidden; /* 隐藏溢出部分 */
      text-overflow: ellipsis; /* 显示省略号 */
      /* 确保移除或注释掉以下属性 */
      /* display: -webkit-box; */
      /* -webkit-box-orient: vertical; */
      /* -webkit-line-clamp: 2; */
      /* min-height: 42px; */
    }

    .template-meta, // Modified line
    .history-meta { // Added line
      font-size: 13px;
      color: #909399;
      display: flex;
      align-items: center;
      margin-top: 8px; // 与标题的间距

      i {
        margin-right: 5px;
        font-size: 14px; // 图标大小
      }
    }

    // 新增：模板名称和时间容器样式
    .template-name-time-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      gap: 8px;
    }

    // 模板名称区域样式
    .template-name-section {
      flex: 1;
      min-width: 0; // 允许flex项目收缩
      position: relative; // 为字符计数定位提供参考

      // 编辑状态时增加底部间距
      &.editing {
        margin-bottom: 15px;
      }
    }

    .template-name-display {
      display: inline-block;
      max-width: 100%;
      font-size: 13px;
      color: #303133;
      font-weight: 500;
      cursor: pointer;
      transition: color 0.2s ease;

      // 文字省略样式
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:hover {
        color: #409EFF;
      }
    }

    .template-name-input {
      width: 100%;

      .el-input__inner {
        font-size: 13px;
        height: 24px;
        line-height: 24px;
        padding: 0 8px;
      }

      // 字符计数样式 - 移除背景色
      .el-input__count {
        font-size: 10px;
        color: #909399;
        line-height: 1;
        position: absolute;
        right: 5px;
        bottom: -15px;
        background: transparent; // 移除背景色
        padding: 0 2px;
      }

      // 调整输入框容器高度以容纳字符计数
      &.el-input--mini.is-exceed .el-input__count {
        color: #F56C6C;
      }
    }

    // 时间区域样式
    .template-time-section {
      flex-shrink: 0; // 防止时间区域被压缩
    }

    .template-time {
      font-size: 11px;
      color: #909399;
      white-space: nowrap;

      i {
        margin-right: 3px;
        font-size: 11px;
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      .template-name-time-container {
        gap: 6px;
      }

      .template-name-display {
        font-size: 12px;
      }

      .template-time {
        font-size: 10px;

        i {
          font-size: 10px;
        }
      }
    }

    @media (max-width: 480px) {
      .template-name-time-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }

      .template-name-section,
      .template-time-section {
        width: 100%;
      }

      .template-name-display {
        font-size: 11px;
      }

      .template-time {
        font-size: 9px;

        i {
          font-size: 9px;
        }
      }
    }
  }

  // 新增：历史记录专用的名称编辑样式（分行布局）
  .history-name-section {
    margin-bottom: 8px; // 名称区域与时间区域的间距
    position: relative; // 为字符计数定位提供参考

    // 编辑状态时增加底部间距
    &.editing {
      margin-bottom: 20px;
    }
  }

  .history-name-display {
    display: block;
    width: 100%;
    font-size: 16px;
    color: #303133;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s ease;
    line-height: 1.4;

    // 文字省略样式
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      color: #409EFF;
    }
  }

  .history-name-input {
    width: 100%;

    .el-input__inner {
      font-size: 16px;
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
    }

    // 字符计数样式 - 移除背景色
    .el-input__count {
      font-size: 10px;
      color: #909399;
      line-height: 1;
      position: absolute;
      right: 5px;
      bottom: -15px;
      background: transparent; // 移除背景色
      padding: 0 2px;
    }

    // 调整输入框容器高度以容纳字符计数
    &.el-input--mini.is-exceed .el-input__count {
      color: #F56C6C;
    }
  }
}

// ... existing code ...

// 模板选中状态的样式 (保持不变，只应用于 .template-card)
.template-card.selected {
  border-color: #409EFF !important;
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2) !important;
}

// ... existing code ...

.template-thumbnail img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  object-fit: cover;
}

.template-info {
  text-align: center;
  margin-top: 8px;
}

.selected-card {
  border-color: blue !important; // 确保样式应用
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2) !important;
}

.selected {
  border-color: #1E90FF !important; // 深蓝色边框
  background-color: #E6F7FF; // 淡蓝色背景
  box-shadow: 0 8px 16px rgba(30, 144, 255, 0.3) !important; // 增强阴影效果
}

/* 刷新按钮样式 */
.refresh-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7eb;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  i {
    font-size: 14px;
    color: #409EFF;
    transition: transform 0.5s ease;
  }

  &:hover i {
    transform: rotate(180deg);
  }

  span {
    font-size: 13px;
    color: #409EFF;
    user-select: none;
  }
}

.outline-editor-container {
  border-radius: 12px;
}

.outline-header {
  display: flex;
  justify-content: center; // 主题居中
  align-items: center;
  position: relative; // 用于定位编辑图标
  cursor: pointer; // 提示可点击编辑
}

.outline-title-display {
  display: inline-flex; // 使图标和文本在一行
  align-items: center;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #ecf5ff; // 悬停效果
  }
}

.outline-title-text {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin: 0;
}

.title-edit-icon {
  margin-left: 10px;
  color: #409EFF;
  font-size: 16px;
  opacity: 0.6; // 默认稍透明
  transition: opacity 0.2s ease;

  .outline-title-display:hover & {
    opacity: 1; // 悬停时完全显示
  }
}

.outline-title-input {
  font-size: 20px;
  font-weight: bold;
  text-align: center; // 输入时也居中
  max-width: 70%; // 限制最大宽度
  .el-input__inner {
    font-weight: bold; // 确保输入框内字体加粗
    text-align: center; // 再次确保文本居中
  }
}

.outline-tree-container {
  max-height: 500px; // 限制最大高度，超出则滚动
  overflow-y: auto;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #f2f6fc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.outline-tree {
  background-color: transparent; // 使树背景透明，显示容器颜色
}

// 自定义树节点样式
.custom-tree-node {
  flex: 1; // 占据可用空间
  display: flex;
  align-items: center;
  justify-content: space-between; // 使编辑输入框靠右，如果添加按钮的话
  font-size: 14px;
  padding-right: 8px;
  width: 100%; // 确保节点占满宽度
}

.node-label-display {
  flex: 1; // 占据主要空间
  cursor: pointer;
  padding: 3px 5px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
  display: inline-block; // 允许点击
  width: calc(100% - 30px); // 减去可能的按钮宽度

  &:hover {
    background-color: #f5f7fa;
  }
}

.node-edit-input {
  flex: 1; // 占据主要空间
  font-size: 14px;
  height: 28px; // 调整输入框高度
  .el-input__inner {
    height: 28px; // 确保内部高度一致
    line-height: 28px;
  }
}

// 调整 el-tree 默认样式（可选）
::v-deep .el-tree-node__content {
  height: auto; // 允许内容自动高度
  padding-top: 5px;
  padding-bottom: 5px;
}

::v-deep .el-tree-node__label {
  width: 100%; // 让标签容器占满
}

.outline-editor-header {
  text-align: center; // 标题居中
  margin-bottom: 20px; // 增加底部间距

  h3 {
    font-size: 18px;
    color: #303133;
    margin: 0 0 10px 0; // 标题和提示间加点距离
  }

  .editor-tip-alert {
    margin-top: 10px;
    // 可以覆盖 el-alert 的默认样式，如果需要
    // 例如，使其不那么宽:
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
}

.editor-tip-alert {
  margin-top: 10px;
  // 可以覆盖 el-alert 的默认样式，如果需要
  // 例如，使其不那么宽:
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

// --- 新增：章节标记样式 ---
.chapter-badge {
  background-color: #f0f9eb;
  border-radius: 4px;
  padding: 4px 8px;
  margin-right: 8px;
  font-size: 12px;
  color: #67c23a;
}

// --- 章节标记样式结束 ---

// --- 新增：Level 3 和 4 前缀标记样式 ---
.level-3-prefix {
  color: #4facfe; // 替换为冷色调
  font-weight: bold;
}

.level-4-prefix {
  color: #00f2fe; // 替换为冷色调
  font-weight: bold;
}

// --- 前缀标记样式结束 ---

// --- 新增：顶部操作栏样式 ---
.top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.add-chapter-btn {
  background-color: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #57a72a;
  }
}

.add-section-btn {
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #337ab7;
  }
}

// --- 顶部操作栏样式结束 ---

// 新增：文本输入模式下按钮容器的样式
.text-input-wrapper {
  position: relative; // 允许按钮定位
  // padding-bottom: 60px; // 移除，按钮现在在外部容器居中
}

.generate-button-container {
  position: absolute; // 绝对定位到底部
  bottom: 10px; // 距离底部10px
  right: 10px; // 距离右侧10px
  text-align: right; // 按钮靠右（如果只有一个按钮）
  margin-top: 10px; // 与文本框的间距
}

// 新增：居中按钮容器的样式
.generate-button-container-center {
  // text-align: center; // 由 flex 替代
  margin-top: 15px; // 与上方输入控件的间距
  display: flex; // 设置为 flex 容器
  align-items: center; // 垂直居中
  justify-content: center; // 水平居中
  gap: 15px; // 设置元素之间的间距，例如 15px
}

.theme-selector-container {
  max-width: 800px;
  margin: 0 auto 30px;

  @media (max-width: $screen-md) {
    max-width: 100%;
  }
}

// 新增：标题和描述的入场动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px); // 从下方 15px 开始
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 新增：文字颜色流动动画
@keyframes animateGradientText {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.step-header h2,
.step-header h2,
.step-header p {
  opacity: 0;

  animation-name: fadeInUp, animateGradientText;
  animation-duration: 1.2s, 10s;
  animation-timing-function: ease-out, ease-in-out;
  animation-fill-mode: forwards, none;
  animation-iteration-count: 1, infinite;
  background-image: linear-gradient(
          90deg,
          #409eff,
          #67c23a,
          #e6a23c,
          #4facfe,
          #79bbff,
          #909399,
          #00f2fe,
          #54b4d3,
          #6dd47e,
          #4facfe,
          #00f2fe,
          #b3c23a,
          #5d4037,
          #667eea,
          #409eff
  );
  background-size: 250% auto;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-header h2 {
  animation-delay: 0.2s, 0s;
}

.step-header p {
  animation-delay: 0.6s, 0s;
}

// 移动端响应式调整
@media screen and (max-width: 768px) {
  .fullscreen-template-dialog {
    .scrollable-content {
      padding: 15px;

      .template-dialog-header {
        padding: 0 10px 15px;
      }

      .template-content {
        padding: 16px 20px; // 调整移动端模板列表内边距
        .template-grid {
          grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
          gap: 16px;
        }
      }
    }

    .fixed-footer {
      padding: 12px 20px;

      .footer-left, .footer-right {
        .el-button {
          padding: 8px 15px;
        }
      }
    }
  }
}

// 悬停效果
.el-radio:hover {
  transform: translateY(-4px) scale(1.02); // 上移更明显并轻微放大
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); // 阴影加深
  border-color: #a0cfff; // 边框变蓝

  .el-radio__label span {
    color: #409EFF; // 文字变蓝
  }

  .el-radio__label i {
    color: #409EFF; // 图标变蓝
  }
}

// 选中效果
.el-radio.is-checked {
  border-color: #409EFF; // 主题色边框
  background-color: #f0f7ff; // 主题色浅色背景
  transform: translateY(-2px) scale(1.01); // 轻微上移和放大
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.15); // 主题色阴影

  .el-radio__label span {
    color: #409EFF; // 保持蓝色文字
    font-weight: 600; // 文字加粗
  }

  .el-radio__label i {
    color: #409EFF; // 保持蓝色图标
  }

  // 选中标记 (右上角对勾)
  &::after {
    content: '\e6da'; // Element UI 的 check 图标代码 (可能需要根据实际图标库调整)
    font-family: 'element-icons' !important; // 确保使用 Element 图标字体
    position: absolute;
    top: -2px;
    right: -5px;
    font-size: 24px;
    color: white;
    background-color: #409EFF;
    padding: 4px 4px 2px 6px; // 微调padding使图标居中
    border-radius: 0 10px 0 10px; // 特殊形状圆角
    transform: scale(0.8);
    transform-origin: top right;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55); // 带回弹效果的过渡
  }

  // 选中时显示标记并放大
  &::after {
    opacity: 1;
    transform: scale(1);
  }
}

// 旧的输入框样式已移至新的统一样式中，此处移除避免冲突

// 针对生成按钮容器内的禁用状态按钮进行样式覆盖

// 文件上传区域样式
.file-upload-container {
  padding: 30px 24px 40px; // 增大内边距
  border: 3px dashed #dcdfe6; // 默认灰色虚线边框
  border-radius: 12px; // 增大圆角
  background-color: #fdfdfd;
  transition: border-color 0.3s, background-color 0.3s; // 确保包含 border-color 过渡
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 200px; // 增加最小高度

  &:hover {
    border-color: #9b59b6; // 鼠标悬停时边框变为紫色
    background-color: #f8fcff; // 保留背景色变化
  }
}

.upload-intro-text {
  font-size: 13px;
  color: #909399;
  line-height: 1.6;
  margin-bottom: 20px;
  text-align: center;
  max-width: 800px;
}

.upload-demo {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 15px; // 为显示文件名留出空间

  ::v-deep .el-upload-dragger {
    background-color: transparent;
    border: none;
    width: 100%;
    height: auto; // 高度自适应内容
    padding: 20px 16px; // 增大内边距
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 150px; // 增加最小高度
  }
}

.upload-content {
  text-align: center;
}

.file-type-icons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;

  i {
    font-size: 32px; // 图标大小
    padding: 8px;
    border-radius: 8px;
    color: white; // 图标颜色为白色
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  // 不同文件类型的背景色
  .icon-word {
    background-color: #2b579a;
  }

  // Word 蓝色
  .icon-ppt {
    background-color: #d24726;
  }

  // PPT 橙红色
  .icon-pdf {
    background-color: #ee3124;
  }

  // PDF 红色
  .icon-txt {
    background-color: #5f6368;
  }

  // TXT 灰色
}

.el-upload__text {
  color: #303133; // 主文字颜色加深
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 8px; // 调整间距
  em {
    color: #409EFF;
    font-style: normal;
  }
}

.el-upload__tip {
  margin-top: 0; // 移除默认顶部间距
  margin-bottom: 15px;
  font-size: 13px;
  color: #909399;
}

.upload-privacy-text {
  font-size: 12px;
  color: #b0b3b8;
  margin-top: 5px;
}

// 新增：文件上传后状态样式
.upload-file-selected-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%; // 尝试占满拖拽区域
  min-height: 10px; // 保持一个最小高度
}

.file-info-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa; // 浅灰背景
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 15px; // 与下方链接间距
  width: auto; // 宽度自适应内容
  max-width: 100%; // 防止过宽
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  i.el-icon-document {
    font-size: 28px; // 文件图标大小
    color: #606266; // 文件图标颜色
    margin-right: 15px;
  }
}

.file-details {
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止文件名过长溢出
  text-align: left; // 确保文字左对齐
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
  max-width: 300px; // 给文件名一个最大宽度
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.re-upload-link {
  font-size: 14px;
  color: #409EFF;
  cursor: pointer;
  display: inline-flex; // 使用 flex 对齐图标和文字
  align-items: center;

  i {
    margin-right: 4px;
  }

  &:hover {
    color: #66b1ff;
  }
}

// 文件上传区域在处理时的占位符样式 (之前的)
.generating-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 180px; // 与拖拽区高度一致
  color: #909399;

  i {
    font-size: 30px;
    margin-bottom: 10px;
    animation: rotating 2s linear infinite; // 添加旋转动画
  }

  p {
    font-size: 14px;
  }
}

// 确保移除了外部的 selected-file-info 样式 (如果之前添加过)
// .selected-file-info { display: none; } // 或者直接删除这个规则

// --- 新增：一句话生成建议浮窗样式 ---
.topic-input-wrapper {
  position: relative; // 使得内部绝对定位的元素有效
  // overflow: visible; // Removed line: This didn't solve the issue
}

.topic-suggestion-box {
  position: absolute;
  background-color: white;
  border-radius: 12px; // 与输入框圆角保持一致
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12); // 更明显的阴影
  border: 1px solid #e5e8eb; // 添加边框
  padding: 16px;
  z-index: 1001; // 确保在输入框之上
  width: 320px; // 固定宽度
  overflow-y: auto; // 超出则滚动

  // 默认向下展示的样式
  &:not(.show-above) {
    // 向下展示时的特殊样式（如果需要）
  }

  // 向上展示的样式
  &.show-above {
    // 向上展示时可以添加特殊样式（如果需要）
    box-shadow: 0 -8px 24px rgba(0, 0, 0, 0.12); // 向上的阴影
  }

  .suggestion-title {
    font-size: 14px;
    font-weight: 600;
    color: #6a0dad; // 紫色标题
    margin-bottom: 12px; // Reverted user change
    padding-bottom: 8px; // Reverted user change
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      font-size: 13px;
      color: #555;
      padding: 8px 10px; // Reverted user change
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;

      &:hover {
        background-color: #f0e6ff; // 淡紫色背景
        color: #6a0dad;
        transform: translateX(3px);
      }
    }
  }
}

// 建议浮窗的过渡效果
.suggestion-fade-enter-active,
.suggestion-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.suggestion-fade-enter,
.suggestion-fade-leave-to {
  opacity: 0;
}

// 默认向下展示的动画
.topic-suggestion-box:not(.show-above) {
  &.suggestion-fade-enter,
  &.suggestion-fade-leave-to {
    transform: translateY(-10px) scale(0.95);
  }
}

// 向上展示的动画
.topic-suggestion-box.show-above {
  &.suggestion-fade-enter,
  &.suggestion-fade-leave-to {
    transform: translateY(10px) scale(0.95);
  }
}

// --- 建议浮窗样式结束 ---

// --- 新增：模板类型选择器样式 ---
.template-type-selector {
  margin-top: 10px; // 与上方元素间距
}

// --- 建议浮窗样式结束 ---

// --- 新增：强制覆盖 el-tabs 内容区域的 overflow --- //
::v-deep .el-tabs__content {
  overflow: visible; // Allow absolutely positioned children to overflow
}

// --- 新增：标签页内容过渡动画 --- //
.tab-content-fade-enter-active,
.tab-content-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.tab-content-fade-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.98);
}

.tab-content-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(1.02);
}

.tab-content-wrapper {
  width: 100%;
  min-height: 400px;
}

// --- 新增：创作内容区域过渡动画 --- //
.creation-content-fade-enter-active,
.creation-content-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.creation-content-fade-enter {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}

.creation-content-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px) scale(1.05);
}

.creation-content-wrapper {
  width: 100%;
}

// --- 新增：输入区域滑动过渡动画 --- //
.input-section-slide-enter-active,
.input-section-slide-leave-active {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.input-section-slide-enter {
  opacity: 0;
  transform: translateY(40px) scale(0.95);
}

.input-section-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(1.05);
}

// --- 新增：聊天区域淡入过渡动画 --- //
.chat-fade-enter-active,
.chat-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.chat-fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.chat-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// --- 新增：工具栏滑动过渡动画 --- //
.toolbar-slide-enter-active,
.toolbar-slide-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.toolbar-slide-enter {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
}

.toolbar-slide-leave-to {
  opacity: 0;
  transform: translateY(-15px) scale(1.05);
}

// --- 新增：创作方式卡片增强过渡效果 --- //
.method-card {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;

  &:hover {
    transform: translateY(-8px) scale(1.03) !important;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15) !important;
  }

  &.active {
    transform: translateY(-4px) scale(1.02) !important;
    animation: activeCardPulse 2s ease-in-out infinite !important;
  }
}

@keyframes activeCardPulse {
  0%, 100% {
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.2);
  }
  50% {
    box-shadow: 0 16px 40px rgba(102, 126, 234, 0.3);
  }
}

// --- 新增：创作内容区域统一进入动画 --- //
.modern-input-section,
.modern-chat-container,
.file-upload-container {
  animation: contentSlideUp 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  opacity: 0;
  transform: translateY(40px);
}

// 为不同元素添加延迟动画
.modern-chat-container {
  animation-delay: 0.1s;
}

.modern-input-section {
  animation-delay: 0.2s;
}

.input-toolbar {
  animation-delay: 0.3s;
}

@keyframes contentSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// --- 卡片进入动画 --- //
.history-item-wrapper,
.template-item-wrapper {
  animation: cardSlideIn 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  opacity: 0;
  transform: translateY(30px);
}

// 为每个卡片添加延迟动画
@for $i from 1 through 20 {
  .history-item-wrapper:nth-child(#{$i}),
  .template-item-wrapper:nth-child(#{$i}) {
    animation-delay: #{($i - 1) * 0.1}s;
  }
}

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// --- 分页容器样式优化 --- //
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;

  ::v-deep .el-pagination {
    .el-pager li {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
      margin: 0 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
      }

      &.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }

    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
      }
    }
  }
}

.history-container {
  padding: 32px 24px; /* 增加内边距 */
  flex: 1; /* Allow it to grow if needed (Keep for flex parent if applicable) */
  display: block; /* Ensure block display */
  width: 100%; /* Ensure full width */
  box-sizing: border-box; /* Include padding in width calculation */
  overflow-y: auto; /* Allow scrolling */
  padding-bottom: 80px; // 增加分页空间
  border-radius: 16px; // 添加圆角
  margin: 8px; // 添加外边距
}

.history-grid {
  display: grid !important; // Force grid display
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); // 增加最小宽度
  gap: 32px; // 增加间距
  max-width: 1400px;
  width: 100%; // Ensure grid tries to use full width
  margin: 0 auto;
  padding: 8px; // 添加内边距
}

// Styles for the action buttons container below the card
.history-item-actions {
  text-align: right; // Modified line: Align buttons to the right
  padding: 12px 16px; // 增加内边距
  border-top: 1px solid rgba(240, 242, 245, 0.6); // 更柔和的分割线
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%); // 与卡片背景一致
  border-radius: 0 0 16px 16px; // 底部圆角

  .el-button {
    margin: 0 0 0 8px; // 减少按钮间距
    border-radius: 8px; // 圆角按钮
    font-size: 12px; // 稍小的字体
    padding: 6px 12px; // 紧凑的内边距
    font-weight: 500; // 中等字重
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); // 流畅过渡
    border: 1px solid transparent; // 透明边框
    position: relative;
    overflow: hidden;

    // 添加微妙的渐变背景
    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    &.el-button--danger {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
      }
    }

    &.el-button--warning {
      background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 8px rgba(254, 202, 87, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(254, 202, 87, 0.4);
      }
    }

    // 图标样式优化
    i {
      font-size: 14px;
      margin-right: 4px;
    }

    // 按钮点击效果
    &:active {
      transform: translateY(0);
    }
  }
}

// ... rest of styles ...

// Responsive adjustments
@media screen and (max-width: 768px) {
  // ... existing responsive styles ...
  .history-card-info {
    padding: 12px;

    h3 {
      font-size: 14px;
      // min-height: 36px; // Removed line (also for mobile)
    }
  }
  .history-item-actions {
    padding: 8px 10px; // Adjusted mobile padding
  }
}

// --- 新增：用户设置区域样式 ---
.user-settings-area {
  position: fixed;
  top: 14px;
  right: 25px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 15px;
  pointer-events: none; /* 让容器不阻挡下层元素 */

  /* 子元素恢复指针事件 */
  > * {
    pointer-events: auto;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  background: transparent;
  padding: 6px 12px 6px 6px;
  border-radius: 25px;
  transition: all 0.3s ease;

  &:hover {
    backdrop-filter: blur(15px);
    transform: translateY(-1px);

    .user-avatar {
      transform: scale(1.05);
    }

    .user-name {
      color: white;
    }
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--theme-gradient, linear-gradient(135deg, #409EFF, #67C23A));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.6s ease-in-out;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
  }

  .user-name {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
  }
}

.settings-container {
  position: relative;
}

.settings-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  i {
    font-size: 18px;
    color: #666;
    transition: all 0.3s ease;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transform: scale(0.9);
    background: var(--theme-gradient, linear-gradient(135deg,
        rgba(26, 206, 233, 0.60) 0%, /* 青蓝 */
        rgba(64, 160, 222, 0.70) 10%, /* 蓝紫中转 */
        rgba(100, 100, 216, 0.70) 20%, /* 蓝紫 */
        rgba(123, 35, 211, 0.70) 30%, /* 紫色 */
        rgba(150, 20, 205, 0.60) 40%, /* 深紫 */
        rgba(176, 10, 199, 0.50) 50%, /* 品红紫 */
        rgba(168, 22, 130, 0.60) 60%, /* 紫红过渡 */
        rgba(160, 33, 52, 0.40) 70%, /* 暖红色 */
        rgba(80, 90, 190, 0.60) 85%, /* 紫蓝拉回 */
        rgba(5, 135, 250, 0.80) 100% /* 冷蓝收尾 */
    ));
    i {
      color: #409EFF;
      transform: rotate(90deg);
    }
  }

  &.active {
    background: rgba(64, 158, 255, 0.2);
    backdrop-filter: blur(10px);

    i {
      color: #409EFF;
      transform: rotate(180deg);
    }
  }
}

.theme-selector-dropdown {
  position: absolute;
  right: 0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  min-width: 200px;
  animation: themeDropdownFadeIn 0.3s ease;
}

@keyframes themeDropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-selector-header {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.theme-options {
  padding: 8px 0;
  max-height: 250px; /* 限制最大高度，约5个选项的高度 */
  overflow-y: auto; /* 添加垂直滚动条 */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background: #f8f9fa;
  }

  &.active {
    background: #e6f7ff;

    .theme-name {
      color: #409EFF;
      font-weight: 500;
    }
  }
}

.theme-preview {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  margin-right: 12px;
  border: 2px solid #eee;
  transition: all 0.2s ease;
}

.theme-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.theme-check {
  color: #409EFF;
  font-size: 16px;
}

// --- 新增：自定义模板 Tab 页样式 ---
.custom-template-container {
  padding: 30px 20px;
  background: #f5f7fa;
  min-height: 400px; // 保证最小高度
}

.create-template-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(100, 100, 150, 0.1);
  border: 1px solid #eef;
  max-width: 350px; // 控制卡片最大宽度
  margin: 0 auto 40px auto; // 居中并添加底部间距
  position: relative;
  overflow: hidden; // 隐藏溢出的伪元素

  // 光晕效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(138, 43, 226, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
    pointer-events: none; // 允许点击卡片
  }

  &:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(100, 100, 150, 0.15);
    border-color: #d8cfff;

    &::before {
      opacity: 1; // 悬停时显示光晕
    }

    .create-template-icon-wrapper {
      background: linear-gradient(135deg, #a88cff 0%, #8a64ff 100%); // 悬停时渐变加深
      transform: scale(1.1);
    }

    .create-template-text {
      color: #6a0dad; // 悬停时文字变深紫色
    }
  }
}

.create-template-icon-wrapper {
  width: 72px;
  height: 72px;
  background: linear-gradient(135deg, #c0aaff 0%, #a88cff 100%); // 默认淡紫色渐变
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;

  i.el-icon-plus {
    font-size: 36px;
    color: white;
    font-weight: bold;
  }
}

.create-template-text {
  font-size: 18px;
  font-weight: 600;
  color: #4a1f7d; // 深紫色文字
  margin-bottom: 10px;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
}

.create-template-subtext {
  font-size: 14px;
  color: #aaa; // 浅灰色文字
  display: flex;
  justify-content: center;
  gap: 10px;
  position: relative;
  z-index: 1;

  i {
    font-size: 18px;
  }
}

.my-templates-section {
  margin-top: 40px;
}

.my-templates-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-left: 10px; // 左侧留白

  i.el-icon-folder-opened {
    font-size: 24px;
    color: #409EFF; // 使用 Element UI 蓝色
    margin-right: 12px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.my-templates-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px;
  min-height: 200px; // 保证内容区域最小高度
  display: flex; // 使用 flex 布局
  justify-content: center; // 水平居中
  align-items: center; // 垂直居中
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

// --- 自定义模板样式结束 ---

// ... rest of styles ...

/* 自定义模板卡片样式 */
.template-card {
  height: 250px; // Ensure all cards have the same height
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex; // Use flex to control inner layout
  flex-direction: column; // Stack image, info, actions vertically

  .el-card__body {
    padding: 0; // Remove default card padding
    display: flex;
    flex-direction: column;
    flex-grow: 1; // Allow body to grow
  }

  .el-image {
    flex-shrink: 0; // Prevent image from shrinking
  }

  .card-content-padding { // Add a wrapper for padding
    padding: 14px;
    flex-grow: 1; // Allow this area to grow
    display: flex;
    flex-direction: column;
    justify-content: space-between; // Push actions to bottom
  }

  .template-subject {
    font-size: 16px;
    color: #303133;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }

  .template-meta {
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
  }

  /* Remove styles for the old .template-info */
  /* .template-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .time {
      font-size: 12px;
      color: #999;
    }
  } */

  .mark-link-bottom {
    margin-top: 23px; // Remove fixed margin-t
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mark-link-bottom .action-btn {
    font-size: 20px; // 减小字体大小
    padding: 0 6px; // 减小按钮内边距
  }

  .button {
    padding: 0;
    min-height: auto;
  }

  .text-danger {
    color: #F56C6C;

    &:hover {
      color: mix(#F56C6C, #fff, 80%);
    }
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;

    i {
      font-size: 20px;
      margin-right: 5px;
    }
  }
}

.create-template-cover {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  .create-template-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    padding: 30px 0;
    position: relative;
    z-index: 1;

    i {
      font-size: 52px;
      margin-bottom: 12px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    span {
      font-size: 18px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}

.history-card:hover .create-template-cover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: scale(1.02);

  &::before {
    animation-duration: 3s; // 加速动画
  }

  .create-template-content {
    color: white;
    transform: translateY(-2px);

    i {
      transform: scale(1.1);
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }
  }
}

/* 保留旧样式以兼容其他地方可能的引用 */
.create-template-card {
  height: 250px; // Match the approximate height of template cards (image height + padding + info/button height)
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #dcdfe6;
  background-color: #f9f9f9;

  .create-template-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 15px;
    }

    span {
      font-size: 16px;
    }
  }

  &:hover {
    border-color: #409eff;
    color: #409eff;
    background-color: #f4f8ff;

    .create-template-content {
      color: #409eff;
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  // margin-top: 4px; // 移除或注释掉，因为现在是绝对定位
  // height: 20px; // 可以保留或调整
  position: absolute; // 新增：使用绝对定位
  bottom: 8px; // 新增：定位到父容器底部
  right: 15px; // 新增：定位到父容器右侧

  span {
    width: 9px; // 稍微增大点
    height: 9px;
    background: #67C23A;
    border-radius: 50%;
    display: inline-block;
    animation: typing-dot-bounce 1.4s infinite ease-in-out both;
    will-change: transform;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

/* 自定义模板样式结束 */

/* 创建按钮单独一行样式 */
.create-button-row {
  display: flex;
  justify-content: center;
  margin-top: 10px; /* 减小上边距 */
  width: 100%;
}

/* 场景卡片样式 */
.scene-card {
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  padding: 10px;
  text-align: center;
  margin-bottom: 10px;
  border: 1px solid #dcdfe6;
}

/* 调整生成按钮容器样式，使内部元素可以换行 */
.generate-button-container-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 15px; /* 减小间距 */
  padding: 10px 15px; /* 减少内边距 */
  width: 100%;
}

/* 场景主题、语言和模型选择器的样式 */
.scene-theme-selector, .language-selector, .model-selector {
  flex: 1;
  min-width: 200px; /* 减小最小宽度 */
  max-width: 320px; /* 减小最大宽度 */
  margin: 5px; /* 减小外边距 */
  position: relative;
}

/* 统一卡片容器高度 */
.scene-cards-container,
.language-cards-container,
.model-cards-container {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  gap: 12px;
  min-height: 150px; /* 确保最小高度统一 */
  max-height: 300px; /* 限制最大高度 */
  overflow-y: auto; /* 内容超出时显示滚动条 */
}

/* 在小屏幕上调整选择器宽度 */
@media screen and (max-width: 768px) {
  .scene-theme-selector,
  .language-selector,
  .model-selector {
    flex-basis: 100%;
    min-width: 100%;
    max-width: 100%;
  }
}

.create-button-row .el-button {
  padding: 12px 25px;
  font-size: 16px;
  min-width: 120px;
}

/* 强调立即创作按钮 */
.create-button-row .el-button--primary {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.create-button-row .el-button--primary:hover {
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.6);
  transform: translateY(-2px);
}

.create-button-row .el-button--primary:active {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
  transform: translateY(1px);
}

/* 设置不同的z-index以确保展开时不会被遮挡 */
.scene-theme-selector {
  z-index: 13;
}

.language-selector {
  z-index: 12;
}

.model-selector {
  z-index: 11;
}

/* 展开时的卡片容器悬浮于底层内容之上 */
.scene-cards-container, .language-cards-container, .model-cards-container {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  margin-top: 2px;
  display: flex;
  flex-wrap: wrap;
  padding: 12px;
  gap: 8px;
  min-height: 100px; /* 减小最小高度 */
  max-height: 300px;
  overflow-y: auto;
  z-index: 20;
}

/* 网络搜索开关样式 */
.web-search-toggle {
  display: flex;
  align-items: center;
  margin-right: 15px;
  padding: 6px 10px;
  background-color: #f5f7fa;
  border-radius: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.web-search-toggle:hover {
  background-color: #eef1f6;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.web-search-toggle .el-icon-question {
  margin-left: 5px;
  color: #909399;
  cursor: pointer;
  font-size: 14px;
}

.web-search-toggle .el-icon-question:hover {
  color: #409EFF;
}

.web-search-toggle .el-switch {
  margin-right: 5px;
}

/* 确保开关在移动设备上也能正常显示 */
@media (max-width: $screen-sm) {
  .web-search-toggle {
    padding: 4px 8px;
    margin-right: 10px;
  }

  .web-search-toggle .el-switch__label {
    font-size: 12px;
  }
}

/* 流式输出时的禁用样式 - 只针对tab项，不影响整个容器 */
.tabs-disabled {
  .el-tabs__item {
    pointer-events: none !important;
    cursor: not-allowed !important;
    color: #c0c4cc !important;
    opacity: 0.5;
    user-select: none;

    &:hover {
      color: #c0c4cc !important;
      cursor: not-allowed !important;
    }

    &.is-active {
      color: #c0c4cc !important;
      border-bottom-color: #c0c4cc !important;
    }
  }
}

/* 单选按钮组禁用样式 - 只针对单选按钮，不影响整个容器 */
.radio-group-disabled {
  .el-radio {
    pointer-events: none !important;
    cursor: not-allowed !important;
    opacity: 0.6;

    .el-radio__input {
      cursor: not-allowed !important;
    }

    .el-radio__label {
      cursor: not-allowed !important;
      color: #c0c4cc !important;
    }

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }

    &.is-checked .el-radio__label {
      color: #c0c4cc !important;
    }
  }
}

/* 骨架屏样式优化 */
.skeleton-full-container {
  width: 100% !important;
  height: 100% !important;
  display: block !important;

  .el-skeleton {
    width: 100% !important;
    height: 100% !important;
    display: block !important;

    .el-skeleton__item {
      width: 100% !important;
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;

      &.is-image {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-position: center center !important;
        background-repeat: no-repeat !important;
        background-size: 48px 48px !important;
      }
    }
  }
}

/* 确保图片容器内的骨架屏完全填充 */
.history-card-cover,
.template-card-cover {
  position: relative;

  .skeleton-full-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: block !important;

    .el-skeleton {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      display: block !important;
      margin: 0 !important;
      padding: 0 !important;

      .el-skeleton__item {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;

        &.is-image {
          position: absolute !important;
          top: 0 !important;
          left: 0 !important;
          width: 100% !important;
          height: 100% !important;
          border-radius: 0 !important;
          margin: 0 !important;
          padding: 0 !important;
          display: block !important;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
          background-size: 400% 100%;
          animation: skeleton-loading 1.4s ease infinite;
        }
      }
    }
  }
}

/* 图片加载占位符样式优化 */
.image-slot {
  position: relative;

  .skeleton-full-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: block !important;

    .el-skeleton {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      display: block !important;
      margin: 0 !important;
      padding: 0 !important;

      .el-skeleton__item.is-image {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-position: center center !important;
        background-repeat: no-repeat !important;
        background-size: 48px 48px !important;
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* 全局骨架屏图片居中修复 */
.el-skeleton__item.is-image {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-size: 48px 48px !important;
}

/* 针对历史记录和模板卡片的骨架屏图片 */
.history-card-cover .el-skeleton__item.is-image,
.template-card-cover .el-skeleton__item.is-image {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-size: 48px 48px !important;
}

/* 针对图片占位符的骨架屏 */
.image-slot .el-skeleton__item.is-image {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-size: 48px 48px !important;
}


</style>
