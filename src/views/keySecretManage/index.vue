<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="78px">
      <el-form-item label="客户id" prop="clientId">
        <el-input
            v-model="queryParams.clientId"
            placeholder="请输入客户id"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名" prop="clientName">
        <el-input
            v-model="queryParams.clientName"
            placeholder="请输入客户名"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="handleRefreshCache"
        >更新缓存
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="manageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="api_key" align="center" prop="apiKey"/>
      <el-table-column label="secret_key" align="center" prop="secretKey"/>
      <el-table-column label="客户Id" align="center" prop="clientId"/>
      <el-table-column label="客户名" align="center" prop="clientName"/>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="text"
              icon="el-icon-key"
              @click="handleManageToken(scope.row)"
          >管理Token
          </el-button>
          <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改key_secret管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="key" prop="apiKey">
          <el-input type="textarea" v-model="form.apiKey" placeholder="请输入key" onnly/>
        </el-form-item>
        <el-form-item label="secretKey" prop="secretKey">
          <el-input type="textarea" v-model="form.secretKey" placeholder="请输入secretKey"/>
        </el-form-item>
        <el-form-item label="客户Id" prop="clientId">
          <el-input type="textarea" v-model="form.clientId" placeholder="请输入客户Id"/>
        </el-form-item>
        <el-form-item label="客户名" prop="clientName">
          <el-input v-model="form.clientName" placeholder="请输入客户名"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listManage,
  getManage,
  delManage,
  addManage,
  updateManage,
  setCacheApiKey,
  createApiKeySecretApi
} from "@/api/keySecretManage/manage";

export default {
  name: "Manage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // key_secret管理表格数据
      manageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        apiKey: null,
        secretKey: null,
        clientName: null,
        clientId: null,
        expired: null,
        orderByColumn: "create_time",
        isAsc: "desc"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        apiKey: [
          {required: true, message: '请输入key', trigger: ['blur', 'change', 'input']},
        ],
        secretKey: [
          {required: true, message: '请输入secretKey', trigger: ['blur', 'change', 'input']},
        ],
        clientName: [
          {required: true, message: '请输入客户名', trigger: ['blur', 'change', 'input']},
        ],
        clientId: [
          {required: true, message: '请输入客户Id', trigger: ['blur', 'change', 'input']},
        ],
      },
      // 定时器
      refreshTimer: null
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询key_secret管理列表 */
    getList() {
      this.loading = true;
      listManage(this.queryParams).then(response => {
        this.manageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        apiKey: null,
        secretKey: null,
        remark: null,
        expired: 0,
        clientName: null,
        clientId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加key_secret管理"

      this.handleCreateApiKeySecret()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getManage(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改key_secret管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateManage(this.form).then(response => {
              this.$notify({
                title: '成功',
                message: '修改成功',
                type: 'success'
              });
              this.open = false;
              this.getList();
            });
          } else {
            addManage(this.form).then(response => {
              this.$notify({
                title: '成功',
                message: '新增成功',
                type: 'success'
              });
              this.open = false;
              this.getList();
              this.handleRefreshCache()
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除key_secret管理编号为"' + ids + '"的数据项？').then(function () {
        return delManage(ids);
      }).then(() => {
        this.getList();
        this.$notify({
          title: '成功',
          message: '删除成功',
          type: 'success'
        });
      }).catch(() => {
      });
    },

    /**
     * 更新key 缓存
     */
    handleRefreshCache() {
      // 清除之前的定时器
      clearTimeout(this.refreshTimer);

      // 设置新的定时器，1秒后执行实际逻辑
      this.refreshTimer = setTimeout(() => {
        console.log("执行缓存刷新操作");
        setCacheApiKey().then((res) => {
          this.$notify({
            title: '成功',
            message: '缓存更新成功',
            type: 'success'
          });
        })
      }, 100);
    },

    /**
     * 创建新的api key secret
     */
    handleCreateApiKeySecret() {
      createApiKeySecretApi().then((res) => {
            if (res.code === 200) {
              this.form.apiKey = res.data.apiKey
              this.form.secretKey = res.data.secretKey
              this.form.clientId = res.data.clientId
            }
          }
      )
    },

    /**
     * 管理Token - 跳转到Token管理页面
     */
    handleManageToken(row) {
      this.$router.push({
        path: '/keySecretToken',
        query: {
          keySecretManageId: row.id,
          apiKey: row.apiKey,
        }
      });
    }


  }
};
</script>
