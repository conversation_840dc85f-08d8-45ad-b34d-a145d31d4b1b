<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="Token" prop="token">
        <el-input
          v-model="queryParams.token"
          placeholder="请输入token"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入客户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="截止时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-back"
          size="mini"
          @click="handleBack"
        >返回
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['keySecretToken:keySecretToken:add']"
        >新增Token
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['keySecretToken:keySecretToken:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['keySecretToken:keySecretToken:remove']"
        >删除
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="keySecretTokenList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="Token" align="center" prop="token" width="240">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.token }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="客户名" align="center" prop="clientName" width="120"/>
      <el-table-column label="客户ID" align="center" prop="clientId" width="100" show-overflow-tooltip/>
      <el-table-column label="有效期(天)" align="center" prop="expired" width="90"/>
      <el-table-column label="截止时间" align="center" prop="expiredTime" width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.expiredTime">{{ scope.row.expiredTime }}</span>
          <el-tag v-else type="warning">未设置</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160"/>
      <el-table-column label="状态" align="center" prop="status" width="70" fixed="right">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
<!--      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip/>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width"  fixed="right" width="280">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['keySecretToken:keySecretToken:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-data-analysis"
            @click="handleViewUsage(scope.row)"
          >查看用量
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-key"
            @click="handleViewPermission(scope.row)"
          >接口权限
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['keySecretToken:keySecretToken:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改key_secret_token管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="关联Key ID" prop="keySecretManageId">
          <el-input v-model="form.keySecretManageId" placeholder="关联的Key Secret管理ID" readonly/>
        </el-form-item>
        <el-form-item label="API Key" prop="apiKey">
          <el-input v-model="form.apiKey" placeholder="API Key" readonly/>
        </el-form-item>
        <el-form-item label="Token" prop="token">
          <el-input v-model="form.token" placeholder="12位随机Token">
            <el-button slot="append" @click="generateTokenAndClientId" icon="el-icon-refresh">重新生成</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="客户ID" prop="clientId">
          <el-input v-model="form.clientId" placeholder="UUID客户ID"/>
        </el-form-item>
        <el-form-item label="客户名" prop="clientName">
          <el-input v-model="form.clientName" placeholder="请输入客户名"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注"/>
        </el-form-item>
        <el-form-item label="有效期(天)" prop="expired">
          <el-input-number
            v-model="form.expired"
            :min="0.01"
            :max="3650"
            :precision="2"
            :step="1"
            placeholder="请输入有效期天数"
            style="width: 100%"
            @change="handleExpiredChange"
          />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            默认90天，最长3650天，支持小数，修改后会自动更新截止时间
          </div>
        </el-form-item>
        <el-form-item label="截止时间" prop="expiredTime">
          <el-date-picker
            v-model="form.expiredTime"
            type="datetime"
            placeholder="选择截止时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
            :default-time="'23:59:59'"
            @change="handleExpiredTimeChange"
            :picker-options="pickerOptions"
          />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            精确的截止时间，格式：yyyy-MM-dd HH:mm:ss，必须在当前时间之后，修改后会自动更新有效期
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            Token的使用状态，停用后将无法使用
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- Token用量查看抽屉 -->
    <el-drawer
      title="Token用量管理"
      :visible.sync="usageDrawerVisible"
      direction="rtl"
      size="700px"
      :before-close="handleUsageDrawerClose"
    >
      <div style="padding: 20px;">
        <!-- Token基本信息 -->
        <el-card class="token-info-card" shadow="never">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">Token信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="Token">
              <el-tag type="success">{{ currentUsageToken.token }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="客户名">
              {{ currentUsageToken.clientName || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="客户ID">
              {{ currentUsageToken.clientId || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentUsageToken.status === '0' ? 'success' : 'danger'">
                {{ currentUsageToken.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 用量统计信息 -->
        <el-card class="usage-stats-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">用量统计</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="refreshUsageData"
              icon="el-icon-refresh"
            >刷新</el-button>
          </div>

          <div v-loading="usageLoading">
            <div v-if="usageData">
              <!-- 使用率展示 -->
              <div class="usage-overview" :class="{ 'usage-disabled': usageData.status === '1' }">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="stat-item">
                      <div class="stat-label">总积分</div>
                      <div class="stat-value">{{ usageData.totalPointCount || 0 }}</div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="stat-item">
                      <div class="stat-label">已使用积分</div>
                      <div class="stat-value" :style="{ color: getUsageColor() }">
                        {{ usageData.pointUsage || 0 }}
                      </div>
                    </div>
                  </el-col>
                </el-row>

                <div class="usage-progress" style="margin: 20px 0;">
                  <div class="progress-label">积分使用率</div>
                  <el-progress
                    :percentage="getUsagePercentage()"
                    :color="getProgressColor()"
                    :stroke-width="12"
                    :show-text="true"
                  ></el-progress>
                </div>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-label">剩余积分</div>
                      <div class="stat-value" :style="{ color: getRemainingColor() }">
                        {{ getRemainingCount() }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-label">总调用次数</div>
                      <div class="stat-value">
                        {{ usageData.totalCallCount || 0 }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-label">最后访问时间</div>
                      <div class="stat-value small">
                        {{ usageData.lastAccessTime || '未访问' }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 用量编辑表单 -->
              <el-divider content-position="left">编辑用量</el-divider>

              <!-- 停用状态提示 -->
              <el-alert
                v-if="usageData.status === '1'"
                title="当前用量已停用"
                description="用量处于停用状态，只能修改状态字段。如需编辑其他字段，请先将状态改为正常。"
                type="warning"
                :closable="false"
                style="margin-bottom: 20px;"
              ></el-alert>

              <el-form ref="usageForm" :model="usageForm" :rules="usageRules" label-width="120px" :class="{ 'form-disabled': usageData.status === '1' }">
                <el-form-item label="总积分" prop="totalPointCount">
                  <el-input-number
                    v-model="usageForm.totalPointCount"
                    :disabled="usageData.status === '1'"
                    :min="0"
                    :max="999999999"
                    :step="1"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="已使用积分" prop="pointUsage">
                  <el-input-number
                    v-model="usageForm.pointUsage"
                    :disabled="usageData.status === '1'"
                    :min="0"
                    :max="usageForm.totalPointCount || 999999999"
                    :step="1"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="总调用次数" prop="totalCallCount">
                  <el-input-number
                    v-model="usageForm.totalCallCount"
                    :disabled="usageData.status === '1'"
                    :min="0"
                    :max="999999999"
                    :step="1"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="最后访问时间" prop="lastAccessTime">
                  <el-date-picker
                    v-model="usageForm.lastAccessTime"
                    :disabled="usageData.status === '1'"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="请选择最后访问时间"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="usageForm.status">
                    <el-radio label="0">正常</el-radio>
                    <el-radio label="1">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="saveUsageData" :loading="usageSaving">
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div v-else class="no-usage-data">
              <el-empty description="暂无用量数据">
                <el-button type="primary" @click="createUsageData">创建用量记录</el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </div>
    </el-drawer>

    <!-- Token接口权限管理抽屉 -->
    <el-drawer
      title="Token接口权限管理"
      :visible.sync="permissionDrawerVisible"
      direction="rtl"
      size="1000px"
      :before-close="handlePermissionDrawerClose"
    >
      <div style="padding: 20px;">
        <!-- Token基本信息 -->
        <el-card class="token-info-card" shadow="never">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">Token信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="Token">
              <el-tag type="success">{{ currentPermissionToken.token }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="客户名">
              {{ currentPermissionToken.clientName || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="客户ID">
              {{ currentPermissionToken.clientId || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentPermissionToken.status === '0' ? 'success' : 'danger'">
                {{ currentPermissionToken.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 权限管理操作区 -->
        <el-card class="permission-actions-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">权限管理</span>
            <div style="float: right;">
              <el-button
                type="primary"
                size="mini"
                @click="handleAddPermission"
                icon="el-icon-plus"
              >添加权限</el-button>
              <el-button
                type="warning"
                size="mini"
                @click="handleRefreshCache"
                icon="el-icon-upload2"
                :loading="cacheRefreshing"
              >写入缓存</el-button>
              <el-button
                type="success"
                size="mini"
                @click="refreshPermissionData"
                icon="el-icon-refresh"
              >刷新</el-button>
            </div>
          </div>

          <!-- 权限列表 -->
          <div v-loading="permissionLoading">
            <el-table
              :data="permissionList"
              style="width: 100%"
              @selection-change="handlePermissionSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="接口路径" prop="apiPaths" width="250">
                <template slot-scope="scope">
                  <div class="api-paths-container">
                    <el-tooltip
                      v-for="(path, index) in parseApiPathsForDisplay(scope.row.apiPaths)"
                      :key="index"
                      :content="path"
                      placement="top"
                      effect="dark"
                    >
                      <el-tag
                        type="info"
                        size="mini"
                        style="margin: 2px; cursor: pointer;"
                      >
                        {{ path }}
                      </el-tag>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="请求方法" prop="limitMethod" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getMethodType(scope.row.limitMethod)">
                    {{ scope.row.limitMethod || '未设置' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="权限描述" prop="description" show-overflow-tooltip />
              <el-table-column label="状态" prop="status" width="100" align="center">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.status"
                    active-value="0"
                    inactive-value="1"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    @change="handlePermissionStatusChange(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="创建时间" prop="createTime" width="160" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.createTime }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleEditPermission(scope.row)"
                  >修改</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDeletePermission(scope.row)"
                    style="color: #F56C6C;"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 批量操作 -->
            <div style="margin-top: 15px;" v-if="permissionSelectedIds.length > 0">
              <el-button
                type="danger"
                size="mini"
                @click="handleBatchDeletePermission"
                icon="el-icon-delete"
              >批量删除 ({{ permissionSelectedIds.length }})</el-button>
            </div>

            <!-- 分页组件 -->
            <pagination
              v-show="permissionTotal > 0"
              :total="permissionTotal"
              :page.sync="permissionQueryParams.pageNum"
              :limit.sync="permissionQueryParams.pageSize"
              @pagination="getPermissionList"
              style="margin-top: 15px;"
            />

            <!-- 空状态 -->
            <div v-if="!permissionLoading && permissionList.length === 0" class="no-permission-data">
              <el-empty description="暂无接口权限配置">
                <el-button type="primary" @click="handleAddPermission">添加第一个权限</el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </div>
    </el-drawer>

    <!-- 添加/编辑权限对话框 -->
    <el-dialog
      :title="permissionDialogTitle"
      :visible.sync="permissionDialogVisible"
      width="700px"
      append-to-body
    >
      <el-form ref="permissionForm" :model="permissionForm" :rules="permissionRules" label-width="120px">
<!--        <el-form-item label="Token" prop="token">-->
<!--          <el-input v-model="permissionForm.token" placeholder="Token" readonly />-->
<!--        </el-form-item>-->
        <el-form-item label="接口路径" prop="apiPaths">
          <el-input
            v-model="permissionForm.apiPaths"
            type="textarea"
            :rows="3"
            placeholder="请输入接口路径，支持多个路径用逗号分割;如:/api/user/info, /api/user/list, /api/user/*"
            @input="handleApiPathsInput"
          />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            支持多个路径：使用逗号(,)分割多个接口路径，每个路径支持通配符(*)
          </div>

          <!-- 路径预览区域 -->
          <div v-if="permissionForm.apiPaths && permissionForm.apiPaths.trim()" class="api-paths-preview">
            <div class="preview-header" @click="togglePathsPreview">
              <i :class="pathsPreviewExpanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
              <span>解析预览 ({{ parsedPathsPreview.length }}个路径)</span>
            </div>
            <el-collapse-transition>
              <div v-show="pathsPreviewExpanded" class="preview-content">
                <div class="preview-paths">
                  <el-tooltip
                    v-for="(path, index) in parsedPathsPreview"
                    :key="index"
                    :content="path || '(空路径)'"
                    placement="top"
                    effect="dark"
                  >
                    <el-tag
                      :type="validateSinglePath(path) ? 'success' : 'danger'"
                      size="small"
                      style="cursor: pointer;"
                    >
                      <i v-if="validateSinglePath(path)" class="el-icon-check" style="margin-right: 3px;"></i>
                      <i v-else class="el-icon-close" style="margin-right: 3px;"></i>
                      {{ path || '(空路径)' }}
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-if="invalidPathsCount > 0" class="preview-warning">
                  <i class="el-icon-warning" style="color: #E6A23C;"></i>
                  <span style="color: #E6A23C; font-size: 12px;">
                    发现 {{ invalidPathsCount }} 个无效路径，请检查格式
                  </span>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </el-form-item>
        <el-form-item label="请求方法" prop="limitMethod">
          <el-select v-model="permissionForm.limitMethod" placeholder="请选择请求方法" style="width: 100%">
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
            <el-option label="PATCH" value="PATCH" />
            <el-option label="ALL" value="ALL" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="permissionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="permissionForm.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="permissionForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="savePermissionData" :loading="permissionSaving">
          确 定
        </el-button>
        <el-button @click="cancelPermissionDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listKeySecretToken,
  getKeySecretToken,
  delKeySecretToken,
  addKeySecretToken,
  updateKeySecretToken
} from "@/api/keySecretToken/keySecretToken";
import {
  listTokenUsage,
  getTokenUsage,
  addTokenUsage,
  updateTokenUsage
} from "@/api/keySecretTokenUsage/tokenUsage";
import {
  listTokenPermission,
  getTokenPermission,
  addTokenPermission,
  updateTokenPermission,
  delTokenPermission,
  refreshTokenPermissionCache
} from "@/api/KeySecretTokenPermission/tokenPermission";
import {v4 as UUID} from 'uuid';

export default {
  name: "KeySecretToken",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // key_secret_token管理表格数据
      keySecretTokenList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前API Key信息
      currentApiKey: '',
      currentKeySecretManageId: null,
      currentClientName: '',
      // 时间范围
      dateRange: [],
      // 防止循环更新的标志
      isUpdatingTime: false,
      // 日期选择器限制选项
      pickerOptions: {
        disabledDate(time) {
          // 禁用今天之前的日期（不包括今天）
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return time.getTime() < today.getTime();
        },
        disabledTime(time) {
          const now = new Date();
          const selectedDate = new Date(time);

          // 如果选择的是今天，则禁用当前时间之前的时间
          if (selectedDate.toDateString() === now.toDateString()) {
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            const currentSecond = now.getSeconds();

            return {
              disabledHours: () => {
                const hours = [];
                for (let i = 0; i < currentHour; i++) {
                  hours.push(i);
                }
                return hours;
              },
              disabledMinutes: (hour) => {
                if (hour === currentHour) {
                  const minutes = [];
                  for (let i = 0; i < currentMinute; i++) {
                    minutes.push(i);
                  }
                  return minutes;
                }
                return [];
              },
              disabledSeconds: (hour, minute) => {
                if (hour === currentHour && minute === currentMinute) {
                  const seconds = [];
                  for (let i = 0; i <= currentSecond; i++) {
                    seconds.push(i);
                  }
                  return seconds;
                }
                return [];
              }
            };
          }
          return {};
        }
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keySecretManageId: null,
        token: null,
        clientName: null,
        expired: null,
        beginExpiredTime: null,
        endExpiredTime: null,
        status: null,
        orderByColumn: "create_time",
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        token: [
          {required: true, message: "token不能为空", trigger: "blur"}
        ],
        clientId: [
          {required: true, message: "客户ID不能为空", trigger: "blur"}
        ],
        clientName: [
          {required: true, message: "客户名不能为空", trigger: "blur"}
        ],
        expiredTime: []
      },

      // 用量管理抽屉相关
      usageDrawerVisible: false,
      usageLoading: false,
      usageSaving: false,
      currentUsageToken: {},
      usageData: null,
      usageForm: {
        id: null,
        token: null,
        totalPointCount: null,
        pointUsage: null,
        totalCallCount: null,
        lastAccessTime: null,
        status: "0"
      },
      usageRules: {
        totalPointCount: [
          { required: true, message: "总积分不能为空", trigger: "blur" },
          { type: 'number', min: 0, message: '总积分不能小于0', trigger: 'blur' }
        ],
        pointUsage: [
          { required: true, message: "已使用积分不能为空", trigger: "blur" },
          { type: 'number', min: 0, message: '已使用积分不能小于0', trigger: 'blur' }
        ],
        totalCallCount: [
          { required: true, message: "总调用次数不能为空", trigger: "blur" },
          { type: 'number', min: 0, message: '总调用次数不能小于0', trigger: 'blur' }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },

      // 接口权限管理抽屉相关
      permissionDrawerVisible: false,
      permissionLoading: false,
      permissionSaving: false,
      cacheRefreshing: false, // 缓存刷新loading状态
      currentPermissionToken: {},
      permissionList: [],
      permissionSelectedIds: [],
      // 权限列表分页
      permissionTotal: 0,
      permissionQueryParams: {
        pageNum: 1,
        pageSize: 10,
        token: null,
        orderByColumn: "create_time",
        isAsc: 'desc'
      },
      permissionDialogVisible: false,
      permissionDialogTitle: "",
      permissionForm: {
        id: null,
        token: null,
        apiPaths: null,
        limitMethod: null,
        description: null,
        status: "0", // 默认正常
        remark: null,
      },
      // 路径预览相关
      pathsPreviewExpanded: false, // 默认折叠
      parsedPathsPreview: [],
      invalidPathsCount: 0,
      permissionRules: {
        apiPaths: [
          { required: true, message: "接口路径不能为空", trigger: "blur" },
          { validator: this.validateApiPaths, trigger: "blur" }
        ],
        limitMethod: [
          { required: true, message: "请求方法不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    // 从路由参数中获取信息
    this.currentKeySecretManageId = this.$route.query.keySecretManageId;
    this.currentApiKey = this.$route.query.apiKey || '';
    this.currentClientName = this.$route.query.clientName || '';

    // 设置查询参数
    if (this.currentKeySecretManageId) {
      this.queryParams.keySecretManageId = this.currentKeySecretManageId;
    }

    // 设置表单验证规则（确保this绑定正确）
    this.rules.expiredTime = [
      {required: true, message: "截止时间不能为空", trigger: "blur"},
      {validator: this.validateExpiredTime, trigger: "blur"}
    ];

    this.getList();
  },
  methods: {
    /** 查询key_secret_token管理列表 */
    getList() {
      this.loading = true;
      listKeySecretToken(this.queryParams).then(response => {
        this.keySecretTokenList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        keySecretManageId: null,
        apiKey: null,
        token: null,
        clientName: null,
        clientId: null,
        remark: null,
        expired: null,
        expiredTime: null,
        status: "0", // 默认正常状态
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // 处理时间范围查询
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.beginExpiredTime = this.dateRange[0];
        this.queryParams.endExpiredTime = this.dateRange[1];
      } else {
        this.queryParams.beginExpiredTime = null;
        this.queryParams.endExpiredTime = null;
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.beginExpiredTime = null;
      this.queryParams.endExpiredTime = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加key_secret_token管理";

      // 自动填充关联信息
      this.form.keySecretManageId = this.currentKeySecretManageId;
      this.form.apiKey = this.currentApiKey;
      this.form.clientName = this.currentClientName;
      this.form.expired = 90; // 默认90天（3个月）

      // 设置默认截止时间（当前时间 + 90天）
      this.setDefaultExpiredTime();

      // 自动生成token和clientId
      this.generateTokenAndClientId();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getKeySecretToken(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改key_secret_token管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateKeySecretToken(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addKeySecretToken(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除key_secret_token管理编号为"' + ids + '"的数据项？').then(function () {
        return delKeySecretToken(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('keySecretToken/keySecretToken/export', {
        ...this.queryParams
      }, `keySecretToken_${new Date().getTime()}.xlsx`)
    },

    /**
     * 生成16位随机token和UUID clientId
     */
    generateTokenAndClientId() {
      const timestamp = Date.now().toString(36); // 通常是9-10位
      const randomPart = Math.random().toString(36).substring(2); // 随机字符
      this.form.token = "sk_tok_" + (timestamp + randomPart).toUpperCase(); // 截取前16位


      // 生成UUID作为clientId
      this.form.clientId = UUID().replace(/-/g, '').substring(0, 8); // 8位clientId
    },


    /**
     * 有效期变化时更新截止时间
     */
    handleExpiredChange(value) {
      if (this.isUpdatingTime || !value || value <= 0) return;

      this.isUpdatingTime = true;
      this.updateExpiredTimeFromDays(value);
      this.$nextTick(() => {
        this.isUpdatingTime = false;
      });
    },

    /**
     * 截止时间变化时更新有效期
     */
    handleExpiredTimeChange(value) {
      if (this.isUpdatingTime || !value) return;

      // 验证截止时间是否在当前时间之后
      if (!this.isValidExpiredTime(value)) {
        this.$message.error('截止时间必须在当前时间之后');
        // 重置为有效的默认值
        this.setDefaultExpiredTime();
        return;
      }

      this.isUpdatingTime = true;
      this.updateExpiredDaysFromTime(value);
      this.$nextTick(() => {
        this.isUpdatingTime = false;
      });
    },

    /**
     * 根据有效期天数更新截止时间
     */
    updateExpiredTimeFromDays(days) {
      const now = new Date();
      const expiredDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
      this.form.expiredTime = this.formatDateTime(expiredDate);
    },

    /**
     * 根据截止时间更新有效期天数
     */
    updateExpiredDaysFromTime(timeStr) {
      const now = new Date();
      const expiredTime = new Date(timeStr);
      const diffMs = expiredTime.getTime() - now.getTime();
      const diffDays = diffMs / (24 * 60 * 60 * 1000);

      // 保留2位小数，最小值为0.01
      this.form.expired = Math.max(0.01, Math.round(diffDays * 100) / 100);
    },

    /**
     * 设置默认截止时间
     */
    setDefaultExpiredTime() {
      this.updateExpiredTimeFromDays(this.form.expired || 90);
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    /**
     * 验证截止时间是否有效（必须在当前时间之后）
     */
    isValidExpiredTime(timeStr) {
      if (!timeStr) return false;
      const expiredTime = new Date(timeStr);
      const now = new Date();
      return expiredTime.getTime() > now.getTime();
    },

    /**
     * 表单验证：截止时间必须在当前时间之后
     */
    validateExpiredTime(rule, value, callback) {
      if (!value) {
        callback(new Error('截止时间不能为空'));
        return;
      }

      if (!this.isValidExpiredTime(value)) {
        callback(new Error('截止时间必须在当前时间之后'));
        return;
      }

      callback();
    },

    /**
     * 状态变更处理
     */
    handleStatusChange(row) {
      const statusText = row.status === "0" ? "正常" : "停用";
      this.$modal.confirm(`确认要将Token状态改为"${statusText}"吗？`).then(() => {
        // 只传递id和status进行更新
        const updateData = {
          id: row.id,
          token: row.token,
          status: row.status,
          expired: row.expired,
          expiredTime: row.expiredTime,
        };

        updateKeySecretToken(updateData).then(response => {
          this.$modal.msgSuccess(`状态已更改为${statusText}`);
          this.getList(); // 刷新列表
        }).catch(() => {
          // 如果更新失败，恢复原状态
          row.status = row.status === "0" ? "1" : "0";
        });
      }).catch(() => {
        // 用户取消，恢复原状态
        row.status = row.status === "0" ? "1" : "0";
      });
    },

    /**
     * 返回主页面
     */
    handleBack() {
      this.$router.go(-1)
    },

    /**
     * 查看Token用量
     */
    handleViewUsage(row) {
      this.currentUsageToken = { ...row };
      this.usageDrawerVisible = true;
      this.loadUsageData(row.token);
    },

    /**
     * 加载用量数据
     */
    loadUsageData(token) {
      this.usageLoading = true;
      this.usageData = null;

      // 查询该token的用量数据
      listTokenUsage({ token: token, pageNum: 1, pageSize: 1 }).then(response => {
        if (response.rows && response.rows.length > 0) {
          this.usageData = response.rows[0];
          this.usageForm = { ...this.usageData };
        } else {
          this.usageData = null;
        }
        this.usageLoading = false;
      }).catch(() => {
        this.usageLoading = false;
        this.$message.error('加载用量数据失败');
      });
    },

    /**
     * 刷新用量数据
     */
    refreshUsageData() {
      if (this.currentUsageToken.token) {
        this.loadUsageData(this.currentUsageToken.token);
      }
    },

    /**
     * 关闭用量抽屉
     */
    handleUsageDrawerClose() {
      this.usageDrawerVisible = false;
      this.currentUsageToken = {};
      this.usageData = null;
      this.resetUsageForm();
    },

    /**
     * 重置用量表单
     */
    resetUsageForm() {
      this.usageForm = {
        id: null,
        token: null,
        totalPointCount: null,
        pointUsage: null,
        totalCallCount: null,
        lastAccessTime: null,
        status: "0"
      };
      if (this.$refs.usageForm) {
        this.$refs.usageForm.resetFields();
      }
    },

    /**
     * 创建用量数据
     */
    createUsageData() {
      this.resetUsageForm();
      this.usageForm.token = this.currentUsageToken.token;
      this.usageForm.totalPointCount = 10000; // 默认1万积分
      this.usageForm.pointUsage = 0; // 默认未使用
      this.usageForm.totalCallCount = 0; // 默认0次调用
      this.usageForm.status = "0"; // 默认正常状态
    },

    /**
     * 保存用量数据
     */
    saveUsageData() {
      this.$refs.usageForm.validate(valid => {
        if (valid) {
          this.usageSaving = true;

          const savePromise = this.usageForm.id
            ? updateTokenUsage(this.usageForm)
            : addTokenUsage(this.usageForm);

          savePromise.then(response => {
            this.$message.success(this.usageForm.id ? '修改成功' : '创建成功');
            this.usageSaving = false;
            this.refreshUsageData(); // 刷新数据
          }).catch(() => {
            this.usageSaving = false;
          });
        }
      });
    },

    /**
     * 获取使用率百分比
     */
    getUsagePercentage() {
      if (!this.usageData || !this.usageData.totalPointCount || this.usageData.totalPointCount <= 0) return 0;
      const percentage = Math.round((this.usageData.pointUsage / this.usageData.totalPointCount) * 100);
      return Math.min(percentage, 100);
    },

    /**
     * 获取进度条颜色
     */
    getProgressColor() {
      const percentage = this.getUsagePercentage();
      if (percentage >= 90) return '#F56C6C'; // 红色
      if (percentage >= 70) return '#E6A23C'; // 橙色
      if (percentage >= 50) return '#409EFF'; // 蓝色
      return '#67C23A'; // 绿色
    },

    /**
     * 获取使用次数颜色
     */
    getUsageColor() {
      const percentage = this.getUsagePercentage();
      if (percentage >= 90) return '#F56C6C';
      if (percentage >= 70) return '#E6A23C';
      return '#67C23A';
    },

    /**
     * 获取剩余积分
     */
    getRemainingCount() {
      if (!this.usageData || !this.usageData.totalPointCount || this.usageData.totalPointCount <= 0) return '无限制';
      const remaining = this.usageData.totalPointCount - (this.usageData.pointUsage || 0);
      return remaining > 0 ? remaining : 0;
    },

    /**
     * 获取剩余积分颜色
     */
    getRemainingColor() {
      if (!this.usageData || !this.usageData.totalPointCount) return '#909399';
      const remaining = this.getRemainingCount();
      if (remaining === '无限制') return '#909399';
      if (remaining <= this.usageData.totalPointCount * 0.1) return '#F56C6C'; // 剩余不足10%
      if (remaining <= this.usageData.totalPointCount * 0.3) return '#E6A23C'; // 剩余不足30%
      return '#67C23A';
    },

    /**
     * 查看Token接口权限
     */
    handleViewPermission(row) {
      this.currentPermissionToken = { ...row };
      this.permissionDrawerVisible = true;
      this.loadPermissionData(row.token);
    },

    /**
     * 加载权限数据
     */
    loadPermissionData(token) {
      this.permissionQueryParams.token = token;
      this.getPermissionList();
    },

    /**
     * 获取权限列表（分页）
     */
    getPermissionList() {
      this.permissionLoading = true;

      listTokenPermission(this.permissionQueryParams).then(response => {
        this.permissionList = response.rows || [];
        this.permissionTotal = response.total || 0;
        this.permissionLoading = false;
      }).catch(() => {
        this.permissionLoading = false;
        this.$message.error('加载权限数据失败');
      });
    },

    /**
     * 刷新权限数据
     */
    refreshPermissionData() {
      if (this.currentPermissionToken.token) {
        this.getPermissionList();
      }
    },

    /**
     * 关闭权限抽屉
     */
    handlePermissionDrawerClose() {
      this.permissionDrawerVisible = false;
      this.currentPermissionToken = {};
      this.permissionList = [];
      this.permissionSelectedIds = [];
      this.permissionTotal = 0;
      // 重置分页参数
      this.permissionQueryParams = {
        pageNum: 1,
        pageSize: 10,
        token: null
      };
      this.resetPermissionForm();
    },

    /**
     * 权限选择变化
     */
    handlePermissionSelectionChange(selection) {
      this.permissionSelectedIds = selection.map(item => item.id);
    },

    /**
     * 获取请求方法标签类型
     */
    getMethodType(method) {
      const methodTypes = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info',
        'ALL': '',
        '*': ''
      };
      return methodTypes[method] || 'info';
    },

    /**
     * 权限状态变更
     */
    handlePermissionStatusChange(row) {
      const statusText = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm(`确认要${statusText}该权限吗？`).then(() => {
        const updateData = {
          id: row.id,
          token: row.token,
          apiPaths: row.apiPaths,
          limitMethod: row.limitMethod,
          description: row.description,
          status: row.status,
          remark: row.remark
        };

        updateTokenPermission(updateData).then(response => {
          this.$modal.msgSuccess(`权限已${statusText}`);
          this.refreshPermissionData();
        }).catch(() => {
          // 如果更新失败，恢复原状态
          row.status = row.status === "0" ? "1" : "0";
        });
      }).catch(() => {
        // 用户取消，恢复原状态
        row.status = row.status === "0" ? "1" : "0";
      });
    },

    /**
     * 添加权限
     */
    handleAddPermission() {
      this.resetPermissionForm();
      this.permissionForm.token = this.currentPermissionToken.token;
      this.permissionForm.limitMethod = "ALL";
      this.permissionForm.status = "0"; // 默认正常/启用
      this.permissionDialogTitle = "添加接口权限";
      this.permissionDialogVisible = true;
    },

    /**
     * 编辑权限
     */
    handleEditPermission(row) {
      this.resetPermissionForm();
      this.permissionForm = {
        ...row
        // 直接使用后端返回的apiPaths和limitMethod字段
      };
      // 更新路径预览
      this.updatePathsPreview(this.permissionForm.apiPaths);
      this.permissionDialogTitle = "修改接口权限";
      this.permissionDialogVisible = true;
    },

    /**
     * 删除权限
     */
    handleDeletePermission(row) {
      this.$modal.confirm(`确认删除接口权限"${row.apiPaths} ${row.limitMethod}"吗？`).then(() => {
        delTokenPermission(row.id).then(response => {
          this.$modal.msgSuccess("删除成功");
          // 如果当前页没有数据了，回到上一页
          if (this.permissionList.length === 1 && this.permissionQueryParams.pageNum > 1) {
            this.permissionQueryParams.pageNum--;
          }
          this.refreshPermissionData();
        });
      });
    },

    /**
     * 批量删除权限
     */
    handleBatchDeletePermission() {
      if (this.permissionSelectedIds.length === 0) {
        this.$message.warning('请选择要删除的权限');
        return;
      }

      this.$modal.confirm(`确认删除选中的${this.permissionSelectedIds.length}个权限吗？`).then(() => {
        delTokenPermission(this.permissionSelectedIds.join(',')).then(response => {
          this.$modal.msgSuccess("批量删除成功");
          this.permissionSelectedIds = [];
          // 如果当前页没有数据了，回到上一页
          if (this.permissionList.length <= this.permissionSelectedIds.length && this.permissionQueryParams.pageNum > 1) {
            this.permissionQueryParams.pageNum--;
          }
          this.refreshPermissionData();
        });
      });
    },

    /**
     * 重置权限表单
     */
    resetPermissionForm() {
      this.permissionForm = {
        id: null,
        token: null,
        apiPaths: null,
        limitMethod: null,
        description: null,
        status: "0", // 默认正常
        remark: null
      };
      // 重置预览相关数据
      this.pathsPreviewExpanded = false;
      this.parsedPathsPreview = [];
      this.invalidPathsCount = 0;

      if (this.$refs.permissionForm) {
        this.$refs.permissionForm.resetFields();
      }
    },

    /**
     * 取消权限对话框
     */
    cancelPermissionDialog() {
      this.permissionDialogVisible = false;
      this.resetPermissionForm();
    },

    /**
     * 验证接口路径
     */
    validateApiPaths(rule, value, callback) {
      if (!value || value.trim() === '') {
        callback(new Error('接口路径不能为空'));
        return;
      }

      const paths = value.split(',').map(path => path.trim()).filter(path => path);

      if (paths.length === 0) {
        callback(new Error('请输入有效的接口路径'));
        return;
      }

      for (let path of paths) {
        if (!path.startsWith('/')) {
          callback(new Error(`接口路径"${path}"必须以/开头`));
          return;
        }
      }

      callback();
    },

    /**
     * 解析接口路径为数组
     */
    parseApiPaths(apiPaths) {
      if (!apiPaths) return [];
      return apiPaths.split(',')
        .map(path => path.trim())
        .filter(path => path);
    },

    /**
     * 解析接口路径用于显示
     */
    parseApiPathsForDisplay(apiPaths) {
      if (!apiPaths) return ['未设置'];
      const paths = apiPaths.split(',')
        .map(path => path.trim())
        .filter(path => path);
      return paths.length > 0 ? paths : ['未设置'];
    },

    /**
     * 处理接口路径输入变化
     */
    handleApiPathsInput(value) {
      this.updatePathsPreview(value);
    },

    /**
     * 更新路径预览
     */
    updatePathsPreview(apiPaths) {
      if (!apiPaths || apiPaths.trim() === '') {
        this.parsedPathsPreview = [];
        this.invalidPathsCount = 0;
        return;
      }

      const paths = apiPaths.split(',').map(path => path.trim());
      this.parsedPathsPreview = paths;

      // 计算无效路径数量
      this.invalidPathsCount = paths.filter(path => !this.validateSinglePath(path)).length;
    },

    /**
     * 验证单个路径格式
     */
    validateSinglePath(path) {
      if (!path || path.trim() === '') return false;
      return path.startsWith('/');
    },

    /**
     * 切换路径预览展开/折叠
     */
    togglePathsPreview() {
      this.pathsPreviewExpanded = !this.pathsPreviewExpanded;
    },

    /**
     * 刷新权限缓存
     */
    handleRefreshCache() {
      this.$modal.confirm('确认要刷新Token权限缓存吗？此操作将更新所有Token的权限配置到缓存中。').then(() => {
        this.cacheRefreshing = true;

        // 调用刷新缓存API
        refreshTokenPermissionCache({}).then(response => {
          this.$modal.msgSuccess('权限缓存刷新成功');
          this.cacheRefreshing = false;
        }).catch(error => {
          this.$modal.msgError('权限缓存刷新失败：' + (error.message || '未知错误'));
          this.cacheRefreshing = false;
        });
      }).catch(() => {
        // 用户取消操作
      });
    },

    /**
     * 保存权限数据
     */
    savePermissionData() {
      this.$refs.permissionForm.validate(valid => {
        if (valid) {
          this.permissionSaving = true;

          // 将输入的多个路径合并为逗号分割的字符串发送给后端
          const permissionData = {
            ...this.permissionForm,
            // 保持apiPaths字段，后端接收逗号分割的路径字符串
            limitMethod: this.permissionForm.limitMethod
          };

          const savePromise = this.permissionForm.id
            ? updateTokenPermission(permissionData)
            : addTokenPermission(permissionData);

          savePromise.then(response => {
            this.$message.success(this.permissionForm.id ? '修改成功' : '添加成功');
            this.permissionSaving = false;
            this.permissionDialogVisible = false;
            // 如果是新增，回到第一页；如果是修改，保持当前页
            if (!this.permissionForm.id) {
              this.permissionQueryParams.pageNum = 1;
            }
            this.refreshPermissionData();
            this.resetPermissionForm();
          }).catch(() => {
            this.permissionSaving = false;
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.app-breadcrumb {
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid #e6e6e6;
}

.app-breadcrumb a {
  color: #409EFF;
  text-decoration: none;
}

.app-breadcrumb a:hover {
  text-decoration: underline;
}

.el-tag {
  font-family: 'Courier New', monospace;
}

/* 用量管理抽屉样式 */
.token-info-card {
  margin-bottom: 20px;
}

.usage-stats-card {
  margin-bottom: 20px;
}

.usage-overview {
  padding: 10px 0;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.small {
  font-size: 14px;
  font-weight: normal;
}

.usage-progress {
  padding: 10px 0;
}

.progress-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: 500;
}

.no-usage-data {
  text-align: center;
  padding: 40px 0;
}

.el-descriptions {
  margin-top: 10px;
}

.el-drawer__body {
  padding: 0;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

/* 权限管理抽屉样式 */
.permission-actions-card {
  margin-bottom: 20px;
}

.no-permission-data {
  text-align: center;
  padding: 40px 0;
}

.el-table {
  margin-top: 10px;
}

.el-table .el-tag {
  margin: 0;
}

/* 权限对话框样式 */
.el-dialog__body {
  padding-bottom: 10px;
}

.dialog-footer {
}

/* 批量操作区域 */
.batch-actions {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

/* 接口路径容器样式 */
.api-paths-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  max-width: 240px;
}

.api-paths-container .el-tag {
  margin: 1px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 路径预览区域样式 */
.api-paths-preview {
  margin-top: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #FAFAFA;
}

.preview-header {
  padding: 8px 12px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #606266;
  transition: background-color 0.3s;
}

.preview-header:hover {
  background-color: #E6E8EB;
}

.preview-header i {
  margin-right: 5px;
  transition: transform 0.3s;
}

.preview-content {
  padding: 10px 12px;
}

.preview-paths {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preview-paths .el-tag {
  margin: 2px;
  font-family: 'Courier New', monospace;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

.preview-warning {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 6px 8px;
  background-color: #FDF6EC;
  border: 1px solid #FAECD8;
  border-radius: 4px;
}

.preview-warning i {
  margin-right: 5px;
}

/* 用量禁用状态样式 */
.usage-disabled {
  opacity: 0.5;
  filter: grayscale(100%);
  pointer-events: none;
}

.usage-disabled .stat-value {
  color: #C0C4CC !important;
}

.usage-disabled .el-progress {
  opacity: 0.6;
}

.usage-disabled .el-progress__text {
  color: #C0C4CC !important;
}
</style>
