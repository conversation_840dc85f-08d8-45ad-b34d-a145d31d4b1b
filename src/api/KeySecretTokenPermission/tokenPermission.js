import request from '@/utils/request'

// 查询Token接口权限配置列表
export function listTokenPermission(query) {
  return request({
    url: '/system/tokenPermission/list',
    method: 'get',
    params: query
  })
}

// 查询Token接口权限配置详细
export function getTokenPermission(id) {
  return request({
    url: '/system/tokenPermission/' + id,
    method: 'get'
  })
}

// 新增Token接口权限配置
export function addTokenPermission(data) {
  return request({
    url: '/system/tokenPermission',
    method: 'post',
    data: data
  })
}

// 修改Token接口权限配置
export function updateTokenPermission(data) {
  return request({
    url: '/system/tokenPermission',
    method: 'put',
    data: data
  })
}

// 删除Token接口权限配置
export function delTokenPermission(id) {
  return request({
    url: '/system/tokenPermission/' + id,
    method: 'delete'
  })
}

// 刷新Token权限缓存
export function refreshTokenPermissionCache(data) {
  return request({
    url: '/system/tokenPermission/refreshTokenPermissionCache',
    method: 'post',
    data: data
  })
}

