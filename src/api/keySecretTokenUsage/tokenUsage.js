import request from '@/utils/request'

// 查询token用量列表
export function listTokenUsage(query) {
  return request({
    url: '/system/tokenUsage/list',
    method: 'get',
    params: query
  })
}

// 查询token用量详细
export function getTokenUsage(id) {
  return request({
    url: '/system/tokenUsage/' + id,
    method: 'get'
  })
}

// 新增token用量
export function addTokenUsage(data) {
  return request({
    url: '/system/tokenUsage',
    method: 'post',
    data: data
  })
}

// 修改token用量
export function updateTokenUsage(data) {
  return request({
    url: '/system/tokenUsage',
    method: 'put',
    data: data
  })
}

// 删除token用量
export function delTokenUsage(id) {
  return request({
    url: '/system/tokenUsage/' + id,
    method: 'delete'
  })
}
