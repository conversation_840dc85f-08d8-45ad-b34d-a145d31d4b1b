import request from '@/utils/request'

// 查询key_secret_token管理列表
export function listKeySecretToken(query) {
  return request({
    url: '/system/keySecretToken/list',
    method: 'get',
    params: query
  })
}

// 查询key_secret_token管理详细
export function getKeySecretToken(id) {
  return request({
    url: '/system/keySecretToken/' + id,
    method: 'get'
  })
}

// 新增key_secret_token管理
export function addKeySecretToken(data) {
  return request({
    url: '/system/keySecretToken',
    method: 'post',
    data: data
  })
}

// 修改key_secret_token管理
export function updateKeySecretToken(data) {
  return request({
    url: '/system/keySecretToken',
    method: 'put',
    data: data
  })
}

// 删除key_secret_token管理
export function delKeySecretToken(id) {
  return request({
    url: '/system/keySecretToken/' + id,
    method: 'delete'
  })
}

// 生成Token和ClientId
export function generateTokenAndClientId() {
  return request({
    url: '/system/keySecretToken/generate',
    method: 'get'
  })
}

// 验证token
export function validateToken(data) {
  return request({
    url: '/system/keySecretToken/validateToken',
    method: 'post',
    data: data
  })
}
