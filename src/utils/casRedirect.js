import Cookies from 'js-cookie'
import { getCasEnter } from '@/api/intellectSmartPpt/intellectSmartPpt'
import {UID_CAS_ENTER_PPT} from "@/utils/auth";

/**
 * 检查是否需要CAS认证，如果需要，则重定向到CAS登录
 * @param {Object} route 当前路由对象
 * @param {Function} getUrlParam 获取URL参数的函数
 * @returns {Promise<boolean>} 是否需要重定向
 */
export async function checkForCasRedirect(route) {
  // 如果当前路径是 /cas/pptV1，需要重定向到CAS登录
  if (route.path === '/cas/pptV1') {
    try {
      // 获取URL中的uid参数
      const uid = getUrlParamFromLocation('uid')
      const inOneMinute = new Date()
      inOneMinute.setTime(inOneMinute.getTime() + (60 * 1000)) // 当前时间加1分钟

      const res = await getCasEnter()
      if (res.code === 200 && res.data && res.data.length > 0) {
        console.log('获取CAS登录地址成功:', res.data[0].value)
        // 设置Cookie，用于后续认证
        Cookies.set(UID_CAS_ENTER_PPT, uid, { expires: inOneMinute })
        Cookies.set('route_url_label', '/pptV1', { expires: inOneMinute })

        // 直接重定向到CAS登录页，阻止Vue Router完成导航
        window.location.href = res.data[0].value
        return true
      } else {
        console.error('获取CAS登录地址失败:', res)
        return false
      }
    } catch (error) {
      console.error('CAS重定向过程中出错:', error)
      return false
    }
  }
  return false
}

/**
 * 从URL中获取参数
 * @param {string} name 参数名
 * @returns {string|null} 参数值或null
 */
function getUrlParamFromLocation(name) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`)
  const r = window.location.search.slice(1).match(reg)
  return r ? decodeURIComponent(r[2]) : null
}
