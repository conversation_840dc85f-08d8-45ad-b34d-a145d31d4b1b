
import {getApiToken, getToken} from '@/utils/auth';
import {validateString} from "@/utils/util";

// 基础URL，比如 process.env.VUE_APP_BASE_API
const baseURL = process.env.VUE_APP_BASE_API;

const fetchRequest = (options) => {
	const {
		url,
		method = 'get',
		data = null,
		headers = {},
		params = {} // 用于 GET 请求参数
	} = options;

	// 拼接完整 URL（包含 base API）
	let fullUrl = baseURL + url;

	// 处理 GET 请求参数
	if ((method.toLowerCase() === 'get' || method.toLowerCase() === 'delete') && params) {
		const queryString = new URLSearchParams(params).toString();
		if (queryString) {
			fullUrl += '?' + queryString;
		}
	}
  // 3. 获取可用的认证信息
  const systemToken = getToken(); // 系统登录token
  const apiToken = getApiToken(); // API token

  // 构建请求配置
  const config = {
    method: method.toUpperCase(),
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };
  // 4. 认证优先级处理
  if (validateString(apiToken)) {
    // 优先使用 api-token
    config.headers['api-token'] = apiToken;
  } else if (validateString(systemToken)) {
    // 其次使用系统 token（仅在未禁用时）
    config.headers.Authorization = `Bearer ${systemToken}`;
  }


	// POST/PUT/PATCH 等需要 body
	if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
		config.body = JSON.stringify(data);
	}

	return new Promise(async (resolve, reject) => {
		try {
			const response = await fetch(fullUrl, config);
			resolve(response);
		} catch (error) {
			reject(error);
		}
	});
};

export default fetchRequest;
