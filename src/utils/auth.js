import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

const ExpiresInKey = 'Admin-Expires-In'

export const API_TOKEN = 'api-token'
export const API_KEY = 'api-key'
export const UID_CAS_ENTER_PPT = 'uid_cas_enter_ppt'




export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}

export function getApiToken() {
  return sessionStorage.getItem(API_TOKEN);
}
export function setApiToken(token) {
  return  sessionStorage.setItem(API_TOKEN, token)
}
export function removeApiToken() {
  return Cookies.remove(API_TOKEN)
}



export function setApiKey(token) {
  return Cookies.set(API_KEY, token)
}
export function getApiKey() {
  return Cookies.get(API_KEY)
}

export function removeApiKey() {
  return Cookies.remove(API_KEY)
}

export function setUidCasEnterPpt(uid) {
  return Cookies.set(UID_CAS_ENTER_PPT, uid)
}
export function getUidCasEnterPpt() {
  return Cookies.get(UID_CAS_ENTER_PPT)
}
export function removeUidCasEnterPpt() {
  return Cookies.remove(UID_CAS_ENTER_PPT)
}



